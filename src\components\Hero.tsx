
import { But<PERSON> } from '@/components/ui/button';
import { ChevronRight, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

const Hero = () => {
  return (
    <section className="relative min-h-screen flex items-center pt-24 pb-20 px-6 md:px-10 overflow-hidden bg-earnhub-lightGray">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-[500px] h-[500px] bg-gradient-radial from-earnhub-blue/20 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 -left-40 w-[300px] h-[300px] bg-gradient-radial from-earnhub-red/10 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto w-full relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 lg:gap-20 items-center">
          {/* Left Content - Text */}
          <div className="order-2 lg:order-1 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <div className="inline-block px-4 py-1 rounded-full bg-white shadow-soft mb-6">
              <p className="text-earnhub-red font-medium text-sm flex items-center">
                <span className="bg-earnhub-red text-white text-xs px-2 py-0.5 rounded-full mr-2">LIMITED</span>
                First 100 sign-ups get KES 1,000 bonus credit!
              </p>
            </div>

            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-earnhub-dark balance-text mb-6 leading-tight">
              Turn <span className="text-earnhub-red">Ambition</span> Into Income
            </h1>

            <p className="text-earnhub-darkGray text-lg md:text-xl mb-8 max-w-xl balance-text">
              Join thousands of Kenyans earning up to <span className="font-bold">KES 50,000 monthly</span> with multiple income streams. No special skills needed - just your smartphone.
            </p>

            <div className="space-y-4 mb-8">
              <h3 className="text-xl font-semibold text-earnhub-dark">Four simple ways to earn with EarnHub:</h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-earnhub-red mr-2">1.</span>
                  <span><strong>Referrals:</strong> Earn KES 400-1,924 per person you invite</span>
                </li>
                <li className="flex items-start">
                  <span className="text-earnhub-red mr-2">2.</span>
                  <span><strong>Simple Tasks:</strong> Complete quick 5-15 minute activities for KES 50-500</span>
                </li>
                <li className="flex items-start">
                  <span className="text-earnhub-red mr-2">3.</span>
                  <span><strong>Competitions:</strong> Win weekly cash prizes up to KES 5,000</span>
                </li>
                <li className="flex items-start">
                  <span className="text-earnhub-red mr-2">4.</span>
                  <span><strong>Partner Program:</strong> Earn commissions selling digital products</span>
                </li>
              </ul>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Link to="/signup">
                <Button size="lg" className="bg-earnhub-red hover:bg-earnhub-red/90 text-white text-lg px-8 py-6 rounded-lg shadow-md hover:shadow-lg transition-all hover:-translate-y-1 group w-full sm:w-auto">
                  <span>Start Earning Now</span>
                  <span className="ml-2 text-xs font-normal bg-white/20 px-2 py-0.5 rounded-full">Free</span>
                </Button>
              </Link>
              <a href="#testimonials">
                <Button size="lg" variant="outline" className="bg-white border-earnhub-gray hover:border-earnhub-red text-earnhub-dark hover:text-earnhub-red px-8 py-6 rounded-lg flex items-center gap-2 hover:gap-3 transition-all w-full sm:w-auto justify-center">
                  <span>See Success Stories</span>
                  <ChevronRight size={18} />
                </Button>
              </a>
            </div>

            <div className="mt-10 flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
              <div className="flex -space-x-2">
                <div className="w-10 h-10 rounded-full bg-earnhub-blue flex items-center justify-center text-white font-medium text-sm">JD</div>
                <div className="w-10 h-10 rounded-full bg-earnhub-red flex items-center justify-center text-white font-medium text-sm">AR</div>
                <div className="w-10 h-10 rounded-full bg-earnhub-dark flex items-center justify-center text-white font-medium text-sm">KP</div>
                <div className="w-10 h-10 rounded-full border-2 border-white bg-white flex items-center justify-center text-earnhub-dark font-medium text-sm">+2K</div>
              </div>
              <div>
                <p className="text-earnhub-darkGray text-sm">
                  Join over <span className="font-semibold">2,000+ people</span> already earning
                </p>
                <p className="text-earnhub-red font-medium text-xs">Last withdrawal: KES 12,500, 3 minutes ago</p>
              </div>
            </div>
          </div>

          {/* Right Content - Image/Illustration */}
          <div className="order-1 lg:order-2 animate-fade-in" style={{ animationDelay: '0.4s' }}>
            <div className="relative">
              {/* Main Image Container with Phone Frame */}
              <div className="bg-white p-6 rounded-3xl shadow-card flex items-center justify-center">
                <div className="relative w-full max-w-sm mx-auto">
                  {/* Phone Screen Content */}
                  <div className="aspect-[9/16] bg-earnhub-lightGray rounded-2xl overflow-hidden p-4">
                    {/* App UI Mockup */}
                    <div className="h-full flex flex-col">
                      {/* App Header */}
                      <div className="flex justify-between items-center mb-4">
                        <div className="text-lg font-bold">
                          <span className="text-earnhub-red">Earn</span>
                          <span className="text-earnhub-dark">Hub</span>
                        </div>
                        <div className="w-8 h-8 rounded-full bg-earnhub-dark/10 flex items-center justify-center">
                          <div className="w-2 h-2 rounded-full bg-earnhub-red"></div>
                        </div>
                      </div>
                      
                      {/* Balance Card */}
                      <div className="bg-gradient-to-r from-earnhub-red to-earnhub-dark p-4 rounded-xl text-white mb-4">
                        <p className="text-xs opacity-80 mb-1">Available Balance</p>
                        <p className="text-2xl font-bold">KES 24,500</p>
                        <div className="flex justify-between items-center mt-4">
                          <div>
                            <p className="text-xs opacity-80">Today's Earnings</p>
                            <p className="text-lg font-medium">+KES 2,400</p>
                          </div>
                          <button className="bg-white/20 px-3 py-1 rounded-lg text-sm">
                            Withdraw
                          </button>
                        </div>
                      </div>
                      
                      {/* Quick Actions */}
                      <div className="grid grid-cols-4 gap-2 mb-4">
                        {['Tasks', 'Referrals', 'Shop', 'Compete'].map((item, index) => (
                          <div key={index} className="flex flex-col items-center">
                            <div className="w-10 h-10 rounded-lg bg-white shadow-sm flex items-center justify-center mb-1">
                              <div className="w-5 h-5 rounded-full bg-earnhub-red/20"></div>
                            </div>
                            <p className="text-xs text-earnhub-dark">{item}</p>
                          </div>
                        ))}
                      </div>
                      
                      {/* Referral Card */}
                      <div className="bg-white p-3 rounded-xl shadow-sm mb-4">
                        <div className="flex justify-between items-center mb-2">
                          <p className="font-medium text-earnhub-dark">Your Referrals</p>
                          <div className="text-xs px-2 py-0.5 rounded-full bg-earnhub-red/10 text-earnhub-red">+3 New</div>
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-2xl font-bold text-earnhub-dark">42</p>
                            <p className="text-xs text-earnhub-darkGray">Total Referrals</p>
                          </div>
                          <div>
                            <p className="text-2xl font-bold text-earnhub-red">KES 16,800</p>
                            <p className="text-xs text-earnhub-darkGray">Earnings from Referrals</p>
                          </div>
                        </div>
                      </div>
                      
                      {/* Recent Activity */}
                      <div className="bg-white p-3 rounded-xl shadow-sm flex-1 overflow-hidden">
                        <p className="font-medium text-earnhub-dark mb-2">Recent Activity</p>
                        <div className="space-y-2">
                          {['Task Completed', 'Competition Entry', 'New Referral'].map((item, index) => (
                            <div key={index} className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div className="w-6 h-6 rounded-full bg-earnhub-red/10"></div>
                                <p className="text-xs text-earnhub-dark">{item}</p>
                              </div>
                              <p className="text-xs font-medium text-earnhub-red">+KES 400</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Floating Highlight Elements */}
              <div className="absolute -top-6 -right-6 bg-white p-3 rounded-xl shadow-card animate-float" style={{ animationDelay: '1s' }}>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-earnhub-red/10 flex items-center justify-center text-earnhub-red">
                    <div className="w-5 h-5 rounded-full bg-earnhub-red/40"></div>
                  </div>
                  <div>
                    <p className="text-xs text-earnhub-darkGray">Earn per referral</p>
                    <p className="text-lg font-bold text-earnhub-dark">KES 400-1,924</p>
                  </div>
                </div>
              </div>
              
              <div className="absolute -bottom-6 -left-6 bg-white p-3 rounded-xl shadow-card animate-float" style={{ animationDelay: '1.5s' }}>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-earnhub-blue/10 flex items-center justify-center text-earnhub-blue">
                    <div className="w-5 h-5 rounded-full bg-earnhub-blue/40"></div>
                  </div>
                  <div>
                    <p className="text-xs text-earnhub-darkGray">Top Earners Make</p>
                    <p className="text-lg font-bold text-earnhub-dark">KES 50,000+</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Call to Action Arrow - Mobile only */}
        <div className="flex justify-center mt-8 lg:hidden">
          <a href="#features" className="text-earnhub-red flex flex-col items-center animate-bounce">
            <span className="text-sm font-medium mb-1">Learn More</span>
            <ArrowRight className="transform rotate-90" size={24} />
          </a>
        </div>
      </div>
    </section>
  );
};

export default Hero;
