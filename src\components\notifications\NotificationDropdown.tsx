
import React, { useState } from 'react';
import { <PERSON>, Check, Filter, X } from 'lucide-react';
import { useNotifications } from '@/hooks/use-notifications';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { NotificationType, PriorityLevel } from '@/types/notification';
import NotificationItem from './NotificationItem';
import { CheckIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export function NotificationDropdown() {
  const [open, setOpen] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const { 
    notifications, 
    unreadCount, 
    filters, 
    updateFilters, 
    resetFilters, 
    markAllAsRead 
  } = useNotifications();
  const navigate = useNavigate();
  
  // Notification type options for filtering
  const typeOptions: { value: NotificationType; label: string }[] = [
    { value: 'system', label: 'System' },
    { value: 'account', label: 'Account' },
    { value: 'competition', label: 'Competitions' },
    { value: 'task', label: 'Tasks' },
    { value: 'promotion', label: 'Promotions' },
    { value: 'partner', label: 'Partner' },
    { value: 'recommendation', label: 'Recommendations' },
  ];
  
  // Priority options for filtering
  const priorityOptions: { value: PriorityLevel | 'all'; label: string }[] = [
    { value: 'all', label: 'All Priorities' },
    { value: 'high', label: 'High Priority' },
    { value: 'medium', label: 'Medium Priority' },
    { value: 'low', label: 'Low Priority' },
  ];
  
  // Toggle notification type filter
  const toggleTypeFilter = (type: NotificationType) => {
    const currentTypes = [...filters.types];
    const index = currentTypes.indexOf(type);
    
    if (index === -1) {
      updateFilters({ types: [...currentTypes, type] });
    } else {
      currentTypes.splice(index, 1);
      updateFilters({ types: currentTypes });
    }
  };
  
  // View all notifications
  const viewAllNotifications = () => {
    setOpen(false);
    navigate('/notifications');
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              className="absolute -top-1 -right-1 px-1 min-w-5 h-5 flex items-center justify-center bg-earnhub-red text-white" 
              variant="default"
            >
              {unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80" align="end">
        <div className="flex items-center justify-between p-4">
          <DropdownMenuLabel className="font-semibold text-lg">Notifications</DropdownMenuLabel>
          <div className="flex space-x-1">
            <Popover open={filterOpen} onOpenChange={setFilterOpen}>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4" align="end">
                <div className="space-y-4">
                  <h4 className="font-medium">Filter Notifications</h4>
                  
                  <div>
                    <h5 className="text-sm font-medium mb-2">Types</h5>
                    <div className="grid grid-cols-2 gap-2">
                      {typeOptions.map((option) => (
                        <Button
                          key={option.value}
                          variant={filters.types.includes(option.value) ? "default" : "outline"}
                          size="sm"
                          className="justify-start"
                          onClick={() => toggleTypeFilter(option.value)}
                        >
                          {filters.types.includes(option.value) && (
                            <CheckIcon className="mr-1 h-3 w-3" />
                          )}
                          {option.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h5 className="text-sm font-medium mb-2">Priority</h5>
                    <div className="grid grid-cols-2 gap-2">
                      {priorityOptions.map((option) => (
                        <Button
                          key={option.value}
                          variant={filters.priority === option.value ? "default" : "outline"}
                          size="sm"
                          className="justify-start"
                          onClick={() => updateFilters({ priority: option.value })}
                        >
                          {filters.priority === option.value && (
                            <CheckIcon className="mr-1 h-3 w-3" />
                          )}
                          {option.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h5 className="text-sm font-medium mb-2">Status</h5>
                    <div className="grid grid-cols-3 gap-2">
                      {[
                        { value: 'all', label: 'All' },
                        { value: 'unread', label: 'Unread' },
                        { value: 'read', label: 'Read' },
                      ].map((option) => (
                        <Button
                          key={option.value}
                          variant={filters.readStatus === option.value ? "default" : "outline"}
                          size="sm"
                          className="justify-start"
                          onClick={() => updateFilters({ readStatus: option.value as any })}
                        >
                          {filters.readStatus === option.value && (
                            <CheckIcon className="mr-1 h-3 w-3" />
                          )}
                          {option.label}
                        </Button>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" size="sm" onClick={resetFilters}>
                      Reset Filters
                    </Button>
                    <Button size="sm" onClick={() => setFilterOpen(false)}>
                      Apply Filters
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            
            <Button variant="ghost" size="icon" onClick={markAllAsRead} title="Mark all as read">
              <Check className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuGroup className="max-h-[300px] overflow-y-auto">
          {notifications.length > 0 ? (
            notifications.map((notification) => (
              <DropdownMenuItem key={notification.id} className="p-0 focus:bg-transparent">
                <NotificationItem notification={notification} />
              </DropdownMenuItem>
            ))
          ) : (
            <div className="py-6 text-center text-muted-foreground">
              <p>No notifications</p>
            </div>
          )}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <div className="p-2">
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={viewAllNotifications}
          >
            View All Notifications
          </Button>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default NotificationDropdown;
