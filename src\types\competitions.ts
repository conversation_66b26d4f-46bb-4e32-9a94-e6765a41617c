
export interface CompetitionPrize {
  position: number;
  reward: string;
}

export interface CompetitionType {
  id: string;
  title: string;
  description: string;
  type: string; // 'referral', 'sales', 'task', etc.
  image: string;
  startDate: string;
  endDate: string;
  participants: number;
  prizes: CompetitionPrize[];
  userPosition: number | null;
  status: 'active' | 'upcoming' | 'past';
  rules?: string[];
  /** Sanity CMS fields */
  _createdAt?: string;
  _updatedAt?: string;
  _id?: string;
  _rev?: string;
  _type?: string;
  slug?: {
    current: string;
    _type: string;
  };
  mainImage?: {
    asset: {
      _ref: string;
      _type: string;
    };
    _type: string;
  };
  body?: any;
}

export interface NewsItem {
  id: string;
  title: string;
  description: string;
  date: string;
  image?: string;
  category: string;
  /** Sanity CMS fields */
  _createdAt?: string;
  _updatedAt?: string;
  _id?: string;
  _rev?: string;
  _type?: string;
  slug?: {
    current: string;
  };
}

export interface LiveUpdate {
  id: string;
  type: 'withdrawal' | 'competition' | 'achievement' | 'referral' | 'task';
  user: string;
  action: string;
  amount?: string;
  timestamp: string;
  /** Sanity CMS fields */
  _createdAt?: string;
  _id?: string;
}
