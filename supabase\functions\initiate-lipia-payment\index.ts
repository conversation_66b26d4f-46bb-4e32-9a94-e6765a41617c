// supabase/functions/initiate-lipia-payment/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from './cors.ts';

const LIPIA_API_URL = 'https://lipia-api.kreativelabske.com/api/request/stk';
// TODO: Replace with actual Supabase secret in production
const LIPIA_API_KEY = 'LIPIA_PLACEHOLDER_API_KEY'; 
// TODO: Replace with actual Sanity client and data fetching in production
const SANITY_PLACEHOLDER_PRICES: Record<string, number> = {
  'starter': 0,
  'bronze': 1500,
  'silver': 3499,
  'gold': 7500,
};

interface RequestBody {
  phone: string;
  membership_tier_sanity_id: string;
}

// Placeholder function for fetching membership tier price from Sanity
// TODO: Replace with actual Sanity client call
async function getMembershipTierPriceFromSanity(tierId: string): Promise<number | null> {
  console.log(`Fetching price for tier ID (from Sanity mock): ${tierId}`);
  const price = SANITY_PLACEHOLDER_PRICES[tierId];
  if (price === undefined) {
    console.warn(`Tier ID ${tierId} not found in Sanity mock data.`);
    return null;
  }
  return price;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    );

    const { data: { user }, error: userError } = await supabaseClient.auth.getUser();
    if (userError || !user) {
      console.error('User not authenticated:', userError);
      return new Response(JSON.stringify({ error: 'User not authenticated' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 401,
      });
    }
    const userId = user.id;
    console.log(`Authenticated user ID: ${userId}`);

    const { phone, membership_tier_sanity_id }: RequestBody = await req.json();
    console.log('Received request body:', { phone, membership_tier_sanity_id });

    // 1. Validate phone number format (07XXXXXXXX)
    if (!/^07\d{8}$/.test(phone)) {
      console.error('Invalid phone number format:', phone);
      return new Response(JSON.stringify({ error: 'Invalid phone number format. Expected 07XXXXXXXX' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // 2. Fetch membership tier price (mocked)
    const amount = await getMembershipTierPriceFromSanity(membership_tier_sanity_id);
    if (amount === null) {
      console.error('Membership tier not found or price not available:', membership_tier_sanity_id);
      return new Response(JSON.stringify({ error: `Membership tier '${membership_tier_sanity_id}' not found or price not available.` }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }
    console.log(`Fetched amount for tier ${membership_tier_sanity_id}: ${amount}`);

    // 3. Validate amount (must be positive for actual payment, starter tier can be 0)
    if (amount < 0) { // Allow 0 for starter tier, but not negative
        console.error('Invalid amount for membership tier (must be non-negative):', amount);
        return new Response(JSON.stringify({ error: 'Amount for membership tier must be non-negative.' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400,
        });
    }
    
    // Skip Lipia API call for free tiers (amount is 0)
    let lipiaResponseData = null;
    if (amount > 0) {
        // 4. Prepare and call Lipia API
        console.log(`Initiating Lipia payment for phone: ${phone}, amount: ${amount}`);
        const lipiaPayload = { phone, amount: amount.toString() };

        const lipiaResponse = await fetch(LIPIA_API_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${LIPIA_API_KEY}`, // Use the placeholder API key
        },
        body: JSON.stringify(lipiaPayload),
        });

        lipiaResponseData = await lipiaResponse.json();
        console.log('Lipia API raw response:', lipiaResponseData);

        if (!lipiaResponse.ok || lipiaResponseData.status_code !== "201") { // Lipia uses 201 for successful STK push
            console.error('Lipia API request failed:', lipiaResponseData);
            const errorMessage = lipiaResponseData?.message || lipiaResponseData?.response_message || 'Lipia API request failed';
            const errorDetails = lipiaResponseData?.errors ? JSON.stringify(lipiaResponseData.errors) : '';
            return new Response(JSON.stringify({ 
                error: `Lipia API error: ${errorMessage}${errorDetails ? ` Details: ${errorDetails}` : ''}`, 
                lipia_response: lipiaResponseData 
            }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: lipiaResponse.status === 400 ? 400 : 500, // Use 400 if Lipia indicates bad request
            });
        }
        console.log('Lipia API call successful. Response:', lipiaResponseData);
    } else {
        console.log('Skipping Lipia API call for free tier.');
    }


    // 5. Database Interaction
    // 5.a Get or create user's wallet
    let { data: wallet, error: walletError } = await supabaseClient
      .from('wallets')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (walletError && walletError.code !== 'PGRST116') { // PGRST116: "Searched item was not found"
      console.error('Error fetching wallet:', walletError);
      throw new Error(`Error fetching wallet: ${walletError.message}`);
    }

    if (!wallet) {
      console.log(`Wallet not found for user ${userId}, creating one.`);
      const { data: newWallet, error: newWalletError } = await supabaseClient
        .from('wallets')
        .insert({ user_id: userId, currency: 'KES', balance: 0, pending_balance: 0 })
        .select('id')
        .single();
      if (newWalletError) {
        console.error('Error creating wallet:', newWalletError);
        throw new Error(`Error creating wallet: ${newWalletError.message}`);
      }
      wallet = newWallet;
      console.log(`Wallet created with ID: ${wallet.id}`);
    } else {
      console.log(`Found existing wallet for user ${userId} with ID: ${wallet.id}`);
    }
    const walletId = wallet.id;

    // 5.b Create Membership Purchase record
    console.log('Creating membership purchase record...');
    const membershipPurchasePayload = {
      user_id: userId,
      sanity_membership_tier_id: membership_tier_sanity_id,
      status: amount === 0 ? 'completed' : 'pending_payment', // Mark as completed if free
      amount_paid: amount,
      currency: 'KES',
      // transaction_id will be updated later if a separate transaction is made for this
    };
    const { data: membershipPurchase, error: membershipError } = await supabaseClient
      .from('membership_purchases')
      .insert(membershipPurchasePayload)
      .select('id')
      .single();

    if (membershipError) {
      console.error('Error creating membership purchase:', membershipError);
      throw new Error(`Error creating membership purchase: ${membershipError.message}`);
    }
    console.log('Membership purchase record created with ID:', membershipPurchase.id);

    // 5.c Create Transaction record
    console.log('Creating transaction record...');
    const transactionDescription = `Membership payment for ${membership_tier_sanity_id} tier.`;
    const transactionPayload = {
      user_id: userId,
      wallet_id: walletId,
      amount: amount,
      type: 'spend_membership_payment', // New type
      status: amount === 0 ? 'completed' : 'pending_payment', // Mark as completed if free
      description: transactionDescription,
      reference_entity_type: 'membership_purchase',
      reference_entity_id: membershipPurchase.id,
      // Lipia specific fields, ensure they exist or handle nulls if API call was skipped
      payment_processor_checkout_id: lipiaResponseData?.data?.CheckoutRequestID || null,
      payment_processor_reference: lipiaResponseData?.data?.reference || null,
      payment_purpose_details: { 
        tier: membership_tier_sanity_id, 
        phone: phone 
      },
    };

    const { data: transaction, error: transactionError } = await supabaseClient
      .from('transactions')
      .insert(transactionPayload)
      .select('id')
      .single();

    if (transactionError) {
      console.error('Error creating transaction:', transactionError);
      // Attempt to roll back membership purchase if transaction fails? Complex for Edge Functions.
      // For now, just log and report.
      throw new Error(`Error creating transaction: ${transactionError.message}`);
    }
    console.log('Transaction record created with ID:', transaction.id);

    // Optionally, link transaction_id back to membership_purchases if design requires it
    // For now, reference_entity_id in transactions links to membership_purchases.

    console.log('All operations successful.');
    return new Response(JSON.stringify({ 
        message: amount > 0 ? 'Lipia STK push initiated successfully. Await payment confirmation.' : 'Free membership tier processed successfully.',
        lipia_response: lipiaResponseData, // This will be null for free tiers
        membership_purchase_id: membershipPurchase.id,
        transaction_id: transaction.id,
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Unhandled error in Edge Function:', error);
    return new Response(JSON.stringify({ error: error.message || 'An unexpected error occurred' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});
