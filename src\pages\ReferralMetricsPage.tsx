
import { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { BarChart2, TrendingUp, Users, DollarSign, Calendar, ArrowUpRight, Check } from "lucide-react";
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, CartesianGrid, LineChart, Line, Legend, <PERSON>C<PERSON>, <PERSON>, Cell } from 'recharts';
import anime from "animejs";

// Mock referral data (compatible with Sanity CMS format)
const monthlyEarnings = [
  { name: 'Jan', amount: 1200 },
  { name: 'Feb', amount: 1850 },
  { name: 'Mar', amount: 1420 },
  { name: 'Apr', amount: 2500 },
  { name: 'May', amount: 2100 }
];

const weeklyReferrals = [
  { day: 'Mon', count: 3 },
  { day: '<PERSON>e', count: 5 },
  { day: 'Wed', count: 2 },
  { day: 'Thu', count: 7 },
  { day: 'Fri', count: 4 },
  { day: 'Sat', count: 6 },
  { day: 'Sun', count: 8 }
];

const referralSources = [
  { name: 'Social Media', value: 45 },
  { name: 'Direct Link', value: 30 },
  { name: 'Email', value: 15 },
  { name: 'QR Code', value: 10 }
];

const referralStatus = [
  { name: 'Active', value: 68 },
  { name: 'Pending', value: 12 },
  { name: 'Inactive', value: 20 }
];

const recentReferrals = [
  { id: 1, name: 'John Smith', date: '2025-05-10', status: 'active', earnings: 250 },
  { id: 2, name: 'Mary Johnson', date: '2025-05-09', status: 'active', earnings: 175 },
  { id: 3, name: 'David Williams', date: '2025-05-07', status: 'pending', earnings: 0 },
  { id: 4, name: 'Sarah Brown', date: '2025-05-05', status: 'active', earnings: 125 },
  { id: 5, name: 'Michael Lee', date: '2025-05-03', status: 'inactive', earnings: 50 }
];

const COLORS = ['#8b5cf6', '#06b6d4', '#10b981', '#f97316'];
const STATUS_COLORS = {
  active: 'bg-green-100 text-green-800 border-green-200',
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  inactive: 'bg-gray-100 text-gray-800 border-gray-200'
};

const ReferralMetricsPage = () => {
  useEffect(() => {
    // Animation for the cards
    anime({
      targets: '.metrics-card',
      opacity: [0, 1],
      translateY: [15, 0],
      delay: anime.stagger(120),
      easing: 'easeOutExpo',
      duration: 700
    });
    
    // Chart animation
    anime({
      targets: '.recharts-layer',
      translateY: [10, 0],
      opacity: [0, 1],
      delay: anime.stagger(50),
      easing: 'easeOutQuad',
      duration: 800
    });
    
    // Icon animation
    anime({
      targets: '.chart-icon',
      scale: [0.8, 1],
      opacity: [0, 1],
      easing: 'easeOutElastic(1, .6)',
      duration: 1000
    });
  }, []);
  
  // Format date from ISO string to readable format
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };
  
  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded shadow-md">
          <p className="text-sm font-medium">{label}</p>
          <p className="text-sm text-earnhub-red">
            {payload[0].name === 'amount' ? `KES ${payload[0].value}` : `${payload[0].value} referrals`}
          </p>
        </div>
      );
    }
    return null;
  };
  
  // Statistics for the cards
  const totalReferrals = weeklyReferrals.reduce((sum, item) => sum + item.count, 0);
  const activeReferrals = referralStatus.find(item => item.name === 'Active')?.value || 0;
  const totalEarnings = monthlyEarnings.reduce((sum, item) => sum + item.amount, 0);
  const averageEarnings = Math.round(totalEarnings / monthlyEarnings.length);
  
  return (
    <div className="container mx-auto px-4 py-6 pb-24 md:pb-6">
      <div className="mb-8 text-center">
        <div className="inline-block p-4 rounded-full bg-earnhub-red/10 mb-4">
          <BarChart2 size={32} className="text-earnhub-red chart-icon" />
        </div>
        <h1 className="text-2xl md:text-3xl font-bold mb-2">Referral Dashboard</h1>
        <p className="text-gray-600 max-w-lg mx-auto">
          Track your referral performance, earnings, and statistics all in one place.
        </p>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="metrics-card">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Total Referrals
              </CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalReferrals}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <ArrowUpRight className="h-3 w-3 mr-1" />
              <span>12% increase this month</span>
            </p>
          </CardContent>
        </Card>
        
        <Card className="metrics-card">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Active Referrals
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeReferrals}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {Math.round((activeReferrals / totalReferrals) * 100)}% activation rate
            </p>
          </CardContent>
        </Card>
        
        <Card className="metrics-card">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Total Earnings
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">KES {totalEarnings.toLocaleString()}</div>
            <p className="text-xs text-green-600 flex items-center mt-1">
              <ArrowUpRight className="h-3 w-3 mr-1" />
              <span>35% increase from last period</span>
            </p>
          </CardContent>
        </Card>
        
        <Card className="metrics-card">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Monthly Average
              </CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">KES {averageEarnings.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Based on last 5 months
            </p>
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="overview" className="space-y-8">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="earnings">Earnings</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="referrals">Referrals</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="metrics-card">
              <CardHeader>
                <CardTitle>Weekly Referral Activity</CardTitle>
                <CardDescription>Number of new referrals per day this week</CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={weeklyReferrals} margin={{ top: 20, right: 30, left: 0, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip content={<CustomTooltip />} />
                      <Bar dataKey="count" fill="#8b5cf6" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card className="metrics-card">
              <CardHeader>
                <CardTitle>Monthly Earnings</CardTitle>
                <CardDescription>Earnings from referrals over the last 5 months</CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={monthlyEarnings} margin={{ top: 20, right: 30, left: 0, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip content={<CustomTooltip />} />
                      <Line type="monotone" dataKey="amount" stroke="#ef4444" strokeWidth={2} dot={{ r: 4 }} />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="metrics-card">
              <CardHeader>
                <CardTitle>Referral Sources</CardTitle>
                <CardDescription>Where your referrals are coming from</CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                <div className="h-72 flex items-center justify-center">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={referralSources}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={90}
                        fill="#8884d8"
                        paddingAngle={5}
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {referralSources.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card className="metrics-card">
              <CardHeader>
                <CardTitle>Referral Status</CardTitle>
                <CardDescription>Distribution of your referrals by status</CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                <div className="h-72 flex items-center justify-center">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={referralStatus}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={90}
                        fill="#8884d8"
                        paddingAngle={5}
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        <Cell fill="#10b981" /> {/* Active - Green */}
                        <Cell fill="#f59e0b" /> {/* Pending - Yellow */}
                        <Cell fill="#6b7280" /> {/* Inactive - Gray */}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="earnings" className="space-y-6">
          <Card className="metrics-card">
            <CardHeader>
              <CardTitle>Monthly Earnings Breakdown</CardTitle>
              <CardDescription>Detailed view of your referral earnings each month</CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={monthlyEarnings} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar dataKey="amount" name="Earnings (KES)" fill="#ef4444" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="metrics-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Earned</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">KES {totalEarnings.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground mt-1">Lifetime earnings</p>
              </CardContent>
            </Card>
            
            <Card className="metrics-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">This Month</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">KES 2,100</div>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <ArrowUpRight className="h-3 w-3 mr-1" />
                  <span>16% from last month</span>
                </p>
              </CardContent>
            </Card>
            
            <Card className="metrics-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Pending Payout</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">KES 750</div>
                <p className="text-xs text-muted-foreground mt-1">Available on May 31st</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="metrics-card">
              <CardHeader>
                <CardTitle>Conversion Rate</CardTitle>
                <CardDescription>Percentage of referrals that become active users</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-5xl font-bold text-center my-8 text-earnhub-red">68%</div>
                <p className="text-center text-muted-foreground">68 out of 100 referred users become active</p>
              </CardContent>
            </Card>
            
            <Card className="metrics-card">
              <CardHeader>
                <CardTitle>Average Value</CardTitle>
                <CardDescription>Average earnings per successful referral</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-5xl font-bold text-center my-8 text-earnhub-red">KES 150</div>
                <p className="text-center text-muted-foreground">Average value per active referral</p>
              </CardContent>
            </Card>
          </div>
          
          <Card className="metrics-card">
            <CardHeader>
              <CardTitle>Referral Growth</CardTitle>
              <CardDescription>Cumulative referrals over time</CardDescription>
            </CardHeader>
            <CardContent className="pt-2">
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={[
                    {month: 'Jan', cumulative: 15},
                    {month: 'Feb', cumulative: 32},
                    {month: 'Mar', cumulative: 45},
                    {month: 'Apr', cumulative: 68},
                    {month: 'May', cumulative: 85}
                  ]} margin={{ top: 20, right: 30, left: 0, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="cumulative" name="Total Referrals" stroke="#8b5cf6" strokeWidth={2} dot={{ r: 4 }} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="referrals" className="space-y-6">
          <Card className="metrics-card">
            <CardHeader>
              <CardTitle>Recent Referrals</CardTitle>
              <CardDescription>Your most recently referred users and their status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left border-b border-gray-200">
                      <th className="pb-3 font-medium">Name</th>
                      <th className="pb-3 font-medium">Date</th>
                      <th className="pb-3 font-medium">Status</th>
                      <th className="pb-3 font-medium">Earnings</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentReferrals.map((referral) => (
                      <tr key={referral.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3">{referral.name}</td>
                        <td className="py-3">{formatDate(referral.date)}</td>
                        <td className="py-3">
                          <Badge className={STATUS_COLORS[referral.status as keyof typeof STATUS_COLORS]}>
                            {referral.status.charAt(0).toUpperCase() + referral.status.slice(1)}
                          </Badge>
                        </td>
                        <td className="py-3">
                          {referral.earnings > 0 ? `KES ${referral.earnings}` : '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="metrics-card">
              <CardHeader>
                <CardTitle>Top Performing Referrals</CardTitle>
                <CardDescription>Referrals that have generated the most earnings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                        <Users className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <p className="font-medium">John Smith</p>
                        <p className="text-sm text-gray-500">Referred Mar 15</p>
                      </div>
                    </div>
                    <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                      KES 350
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                        <Users className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <p className="font-medium">Mary Johnson</p>
                        <p className="text-sm text-gray-500">Referred Feb 28</p>
                      </div>
                    </div>
                    <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                      KES 275
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                        <Users className="h-5 w-5 text-purple-600" />
                      </div>
                      <div>
                        <p className="font-medium">David Williams</p>
                        <p className="text-sm text-gray-500">Referred Apr 10</p>
                      </div>
                    </div>
                    <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                      KES 225
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="metrics-card">
              <CardHeader>
                <CardTitle>Referral Link Performance</CardTitle>
                <CardDescription>How your different referral links are performing</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'Facebook', value: 35 },
                          { name: 'Twitter', value: 25 },
                          { name: 'WhatsApp', value: 20 },
                          { name: 'Email', value: 15 },
                          { name: 'Other', value: 5 }
                        ]}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        <Cell fill="#1877f2" /> {/* Facebook blue */}
                        <Cell fill="#1da1f2" /> {/* Twitter blue */}
                        <Cell fill="#25d366" /> {/* WhatsApp green */}
                        <Cell fill="#ea4335" /> {/* Email red */}
                        <Cell fill="#6b7280" /> {/* Gray for Other */}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
      
      <div className="mt-12 bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">Tips to Increase Your Referrals</h3>
        <ul className="space-y-3">
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Check className="h-4 w-4 text-white" />
            </div>
            <span>Share your unique referral link on social media platforms regularly.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Check className="h-4 w-4 text-white" />
            </div>
            <span>Create content explaining how EarnHub works and the benefits it offers.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Check className="h-4 w-4 text-white" />
            </div>
            <span>Follow up with people you've referred to ensure they complete the registration.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Check className="h-4 w-4 text-white" />
            </div>
            <span>Join the Ambassador program to earn higher commission rates and gain special tools.</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default ReferralMetricsPage;
