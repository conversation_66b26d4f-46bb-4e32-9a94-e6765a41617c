
import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu } from 'lucide-react';
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import MobileSidebar from './MobileSidebar';
import NotificationDropdown from './notifications/NotificationDropdown';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();
  
  // Check if we're on the landing page
  const isLandingPage = location.pathname === '/';

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);

  const handleCloseMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled ? 'bg-white shadow-sm' : isLandingPage ? 'bg-transparent' : 'bg-white shadow-sm'
    } md:px-4 px-2 py-3`}>
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center space-x-2">
          {/* Mobile menu trigger - only show on non-landing pages or when scrolled on landing */}
          {(!isLandingPage || isScrolled) && (
            <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild className="block md:hidden">
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Menu />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="p-0 w-64">
                <MobileSidebar onClose={handleCloseMenu} />
              </SheetContent>
            </Sheet>
          )}

          {/* Logo */}
          <Link to="/" className="text-lg md:text-xl font-bold text-earnhub-red">
            EarnHub
          </Link>
        </div>

        {/* Desktop navigation - show on landing page */}
        {isLandingPage && (
          <div className="hidden md:flex space-x-6 items-center">
            <a href="#features" className="hover:text-gray-600">Ways to Earn</a>
            <a href="#membership" className="hover:text-gray-600">Membership</a>
            <a href="#testimonials" className="hover:text-gray-600">Success Stories</a>
          </div>
        )}
        
        {/* Non-landing page navigation */}
        {!isLandingPage && (
          <div className="hidden md:flex space-x-4 items-center">
            <Link to="/tasks" className="hover:text-gray-600">Tasks</Link>
            <Link to="/opportunities" className="hover:text-gray-600">Opportunities</Link>
            <Link to="/competitions" className="hover:text-gray-600">Competitions</Link>
            <Link to="/shop" className="hover:text-gray-600">Shop</Link>
            <Link to="/membership" className="hover:text-gray-600">Membership</Link>
            <Link to="/partners" className="hover:text-gray-600">Partner Program</Link>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center space-x-2">
          {/* Show notifications only on non-landing pages */}
          {!isLandingPage && <NotificationDropdown />}

          {/* Login/Signup buttons on landing page */}
          {isLandingPage ? (
            <>
              <Link to="/login">
                <Button variant="ghost" size="sm" className="hidden md:block">
                  Log In
                </Button>
              </Link>
              <Link to="/signup">
                <Button size="sm">
                  Sign Up Free
                </Button>
              </Link>
            </>
          ) : (
            <>
              <Link to="/login">
                <Button variant="ghost" size="sm" className="hidden md:block">
                  Log In
                </Button>
              </Link>
              <Link to="/signup">
                <Button size="sm" className="hidden md:block">
                  Sign Up
                </Button>
              </Link>
            </>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
