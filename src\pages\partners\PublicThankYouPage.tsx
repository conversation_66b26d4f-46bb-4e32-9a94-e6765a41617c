import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Footer, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";

const PublicThankYouPage = () => {
  const navigate = useNavigate();
  const affiliateRef = localStorage.getItem('earnhub-affiliate-ref');
  
  // Clear the cart data but keep the affiliate ref
  useEffect(() => {
    localStorage.removeItem('earnhub-public-cart');
  }, []);
  
  return (
    <div className="min-h-screen bg-white">
      {/* Simple header for the public page */}
      <header className="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <h1 className="text-xl font-bold text-earnhub-red">EarnHub</h1>
          </div>
          <Button size="sm" onClick={() => window.location.href = "/login"}>Sign In</Button>
        </div>
      </header>
      
      <div className="container mx-auto px-4 py-8 pt-20">
        <Card className="max-w-md mx-auto text-center">
          <CardHeader>
            <div className="flex justify-center mb-4">
              <div className="rounded-full bg-green-100 p-3">
                <CheckCircle className="h-12 w-12 text-green-600" />
              </div>
            </div>
            <CardTitle className="text-2xl">Thank You for Your Order!</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-6">
              Your order has been received and is being processed. We've sent a confirmation email with your order details.
            </p>
            
            {affiliateRef && (
              <Card className="mb-6 border-earnhub-red bg-gray-50">
                <CardContent className="p-4 text-sm">
                  <p>Your purchase was credited to EarnHub Partner: <strong>{affiliateRef}</strong></p>
                </CardContent>
              </Card>
            )}
            
            <div className="space-y-2">
              <p className="text-sm text-gray-500">What happens next?</p>
              <ul className="text-left text-sm space-y-2">
                <li className="flex items-start">
                  <span className="mr-2">1.</span>
                  <span>We'll send you a confirmation email with your order details.</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">2.</span>
                  <span>Our team will process your order within 24 hours.</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">3.</span>
                  <span>You'll receive delivery updates via email.</span>
                </li>
              </ul>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <Button className="w-full" onClick={() => navigate('/p/shop')}>
              Continue Shopping
            </Button>
            <div className="text-center">
              <p className="text-sm font-medium mb-2">Create an account to track your orders</p>
              <Button variant="outline" onClick={() => window.location.href = "/signup"}>
                Sign Up for Free
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>

      {/* Simple Footer */}
      <footer className="bg-gray-100 py-8 mt-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} EarnHub. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PublicThankYouPage;
