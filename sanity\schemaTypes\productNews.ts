
export default {
  name: 'productNews',
  title: 'Product News & Promotions',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text'
    },
    {
      name: 'startDate',
      title: 'Start Date',
      type: 'datetime',
      validation: Rule => Rule.required()
    },
    {
      name: 'endDate',
      title: 'End Date',
      type: 'datetime',
      validation: Rule => Rule.required()
    },
    {
      name: 'discount',
      title: 'Discount (%)',
      type: 'number'
    },
    {
      name: 'image',
      title: 'Image',
      type: 'image',
      options: {
        hotspot: true
      }
    },
    {
      name: 'productId',
      title: 'Related Product',
      type: 'reference',
      to: [{type: 'product'}]
    }
  ]
}
