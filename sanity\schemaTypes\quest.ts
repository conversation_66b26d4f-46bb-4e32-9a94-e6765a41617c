// sanity/schemaTypes/quest.ts
import {Rule} from 'sanity'

export default {
  name: 'quest',
  title: 'Quest / Task',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'category',
      title: 'Category',
      type: 'string',
      options: {
        list: ['survey', 'testing', 'social_media', 'content_creation', 'research', 'other'],
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'difficulty',
      title: 'Difficulty',
      type: 'string',
      options: {
        list: ['easy', 'medium', 'hard', 'expert'],
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'rewardAmount',
      title: 'Reward Amount (KES)',
      type: 'number',
      validation: (Rule: Rule) => Rule.required().min(0),
    },
    {
      name: 'rewardPoints',
      title: 'Reward Points',
      type: 'number',
      validation: (Rule: Rule) => Rule.min(0),
    },
    {
      name: 'requirements',
      title: 'Task Requirements',
      type: 'array',
      of: [{type: 'string'}],
    },
    {
      name: 'estimatedTime',
      title: 'Estimated Time',
      type: 'string',
      description: "e.g., '5-10 minutes', '1 hour'",
    },
    {
      name: 'type',
      title: 'Type',
      type: 'string',
      options: {
        list: ['one_time', 'recurring'],
      },
      initialValue: 'one_time',
    },
    {
      name: 'maxEntries',
      title: 'Max Entries (0 for unlimited)',
      type: 'number',
      initialValue: 0,
    },
    {
      name: 'isActive',
      title: 'Is Active',
      type: 'boolean',
      initialValue: true,
    },
    {
      name: 'startDate',
      title: 'Start Date',
      type: 'datetime',
    },
    {
      name: 'endDate',
      title: 'End Date',
      type: 'datetime',
      description: 'Optional end date for time-sensitive tasks',
    },
    {
      name: 'prerequisites',
      title: 'Prerequisite Quests',
      type: 'array',
      of: [{type: 'reference', to: [{type: 'quest'}]}],
    },
  ],
}
