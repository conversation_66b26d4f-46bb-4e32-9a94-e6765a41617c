
import { client } from '@/lib/sanity';
import { Product, ProductCategory, ProductNews, ProductFilter } from '@/types/shop';

export const fetchFeaturedProducts = async (): Promise<Product[]> => {
  const query = `*[_type == "product" && featured == true] {
    _id,
    title,
    slug,
    description,
    price,
    discountedPrice,
    savings,
    "image": image.asset->url,
    categoryId,
    "category": category->name,
    featured,
    hot,
    badge,
    inStock,
    dateAdded
  }`;
  
  return await client.fetch(query);
};

export const fetchProductNews = async (): Promise<ProductNews[]> => {
  const query = `*[_type == "productNews" && dateTime(endDate) > dateTime(now())] | order(dateTime(startDate)) {
    _id,
    title,
    description,
    startDate,
    endDate,
    discount,
    "image": image.asset->url,
    productId
  }`;
  
  return await client.fetch(query);
};

export const fetchProductCategories = async (): Promise<ProductCategory[]> => {
  const query = `*[_type == "productCategory"] {
    _id,
    name,
    slug,
    description,
    "image": image.asset->url
  }`;
  
  return await client.fetch(query);
};

export const fetchProductsByCategory = async (categoryId: string): Promise<Product[]> => {
  const query = `*[_type == "product" && categoryId == $categoryId] {
    _id,
    title,
    slug,
    description,
    price,
    discountedPrice,
    savings,
    "image": image.asset->url,
    categoryId,
    "category": category->name,
    featured,
    hot,
    badge,
    inStock,
    dateAdded
  }`;
  
  return await client.fetch(query, { categoryId });
};

export const fetchAllProducts = async (filter?: ProductFilter): Promise<Product[]> => {
  let query = `*[_type == "product"`;
  
  // Add filters if provided
  if (filter) {
    const conditions = [];
    
    if (filter.category) {
      conditions.push(`categoryId == "${filter.category}"`);
    }
    
    if (filter.minPrice > 0) {
      conditions.push(`price >= ${filter.minPrice}`);
    }
    
    if (filter.maxPrice > 0) {
      conditions.push(`price <= ${filter.maxPrice}`);
    }
    
    if (conditions.length > 0) {
      query += ` && ${conditions.join(" && ")}`;
    }
  }
  
  query += `] {
    _id,
    title,
    slug,
    description,
    price,
    discountedPrice,
    savings,
    "image": image.asset->url,
    categoryId,
    "category": category->name,
    featured,
    hot,
    badge,
    inStock,
    dateAdded
  }`;
  
  // Add sorting
  if (filter?.sort) {
    switch (filter.sort) {
      case 'latest':
        query += ' | order(dateAdded desc)';
        break;
      case 'price-low':
        query += ' | order(price asc)';
        break;
      case 'price-high':
        query += ' | order(price desc)';
        break;
      case 'popular':
        query += ' | order(popularity desc)';
        break;
    }
  }
  
  return await client.fetch(query);
};
