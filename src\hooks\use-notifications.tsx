
import { useState, useEffect, useCallback } from 'react';
import { Notification, NotificationType, PriorityLevel, NotificationFilterOptions } from '@/types/notification';
import { useToast } from '@/hooks/use-toast';

// Mock data - would be replaced with Sanity CMS integration
const mockNotifications: Notification[] = [
  {
    id: '1',
    title: 'Welcome to EarnHub!',
    message: 'Start earning today by exploring tasks, competitions, and more!',
    type: 'system',
    priority: 'medium',
    isRead: false,
    createdAt: new Date(Date.now() - 300000).toISOString(),
    icon: 'Bell'
  },
  {
    id: '2',
    title: 'New Competition Available',
    message: 'Essay writing competition with KES 5,000 prize is now open for entries!',
    type: 'competition',
    priority: 'high',
    isRead: false,
    createdAt: new Date(Date.now() - 7200000).toISOString(),
    actionUrl: '/competition/essay-2025',
    icon: 'Trophy'
  },
  {
    id: '3',
    title: 'Your Task Submission',
    message: 'Your recent survey task has been approved and KES 400 added to your account.',
    type: 'task',
    priority: 'medium',
    isRead: true,
    createdAt: new Date(Date.now() - ********).toISOString(),
    actionUrl: '/wallet',
    icon: 'CheckCircle'
  },
  {
    id: '4',
    title: 'Limited Time Offer',
    message: '50% bonus points on all referrals this weekend!',
    type: 'promotion',
    priority: 'medium',
    isRead: false,
    createdAt: new Date(Date.now() - *********).toISOString(),
    actionUrl: '/referrals',
    icon: 'Zap'
  },
  {
    id: '5',
    title: 'Account Verification',
    message: 'Please verify your phone number to enable withdrawals.',
    type: 'account',
    priority: 'high',
    isRead: false,
    createdAt: new Date(Date.now() - *********).toISOString(),
    actionUrl: '/settings',
    icon: 'AlertCircle'
  },
  {
    id: '6',
    title: 'Recommended for You',
    message: 'Based on your interests, check out the new mobile testing opportunity.',
    type: 'recommendation',
    priority: 'low',
    isRead: false,
    createdAt: new Date(Date.now() - *********).toISOString(),
    actionUrl: '/opportunities',
    icon: 'ThumbsUp'
  },
  {
    id: '7',
    title: 'Join Partner Program',
    message: 'You qualify for our Partner Program! Earn up to 40% commission on sales.',
    type: 'partner',
    priority: 'medium',
    isRead: false,
    createdAt: new Date(Date.now() - *********).toISOString(),
    actionUrl: '/partners',
    icon: 'Users'
  },
  {
    id: '8',
    title: 'Competition Ending Soon',
    message: 'The weekly trivia competition ends in 24 hours. Don\'t miss out!',
    type: 'competition',
    priority: 'high',
    isRead: false,
    createdAt: new Date(Date.now() - *********).toISOString(),
    actionUrl: '/competition/trivia-weekly',
    icon: 'Clock'
  }
];

const defaultFilters: NotificationFilterOptions = {
  types: ['system', 'account', 'competition', 'task', 'promotion', 'partner', 'recommendation'],
  priority: 'all',
  readStatus: 'all'
};

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [filters, setFilters] = useState<NotificationFilterOptions>(defaultFilters);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const { toast } = useToast();
  
  // Fetch notifications from API (mock for now)
  useEffect(() => {
    // This would be a real API call in production
    setNotifications(mockNotifications);
    const count = mockNotifications.filter(n => !n.isRead).length;
    setUnreadCount(count);
  }, []);
  
  // Apply filters
  useEffect(() => {
    let result = [...notifications];
    
    // Filter by types
    if (filters.types.length > 0) {
      result = result.filter(n => filters.types.includes(n.type));
    }
    
    // Filter by priority
    if (filters.priority !== 'all') {
      result = result.filter(n => n.priority === filters.priority);
    }
    
    // Filter by read status
    if (filters.readStatus === 'read') {
      result = result.filter(n => n.isRead);
    } else if (filters.readStatus === 'unread') {
      result = result.filter(n => !n.isRead);
    }
    
    // Sort by date (newest first)
    result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    setFilteredNotifications(result);
  }, [notifications, filters]);
  
  // Mark notification as read
  const markAsRead = useCallback((id: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === id ? { ...n, isRead: true } : n
      )
    );
    
    // This would call the API in production
    console.log(`Marking notification ${id} as read`);
  }, []);
  
  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
    setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
    setUnreadCount(0);
    
    // This would call the API in production
    console.log('Marking all notifications as read');
    
    toast({
      title: "Notifications Updated",
      description: "All notifications have been marked as read.",
    });
  }, [toast]);
  
  // Delete notification
  const deleteNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
    
    // This would call the API in production
    console.log(`Deleting notification ${id}`);
    
    toast({
      title: "Notification Deleted",
      description: "The notification has been removed.",
    });
  }, [toast]);
  
  // Update filters
  const updateFilters = useCallback((newFilters: Partial<NotificationFilterOptions>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
  }, []);
  
  // Reset filters to default
  const resetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);
  
  return {
    notifications: filteredNotifications,
    unreadCount,
    filters,
    updateFilters,
    resetFilters,
    markAsRead,
    markAllAsRead,
    deleteNotification
  };
};
