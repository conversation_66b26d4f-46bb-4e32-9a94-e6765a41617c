
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';

const membershipTiers = [
  {
    id: 'basic',
    name: 'Starter',
    price: 0,
    description: 'Start your earning journey for free',
    features: [
      '40% referral commission (KES 400 per referral)',
      'Access to basic tasks (KES 50-100)',
      'Enter free competitions',
      'Basic member badge',
      'Minimum withdrawal: KES 1000',
    ],
    popular: false,
    color: 'bg-earnhub-dark',
    cta: 'Start Free',
  },
  {
    id: 'bronze',
    name: 'Bronze',
    price: 1500,
    description: 'Enhanced earnings at an affordable price',
    features: [
      '45% referral commission (KES 675 per Bronze referral)',
      'Access to standard tasks (KES 75-150)',
      'Discounted competition entries',
      'Bronze member badge',
      'Reduced minimum withdrawal: KES 800',
      'Access to special weekly tasks',
    ],
    popular: false,
    color: 'bg-gradient-to-r from-amber-700 to-amber-500',
    cta: 'Upgrade to Bronze',
  },
  {
    id: 'silver',
    name: 'Silver',
    price: 3499,
    originalPrice: 4999,
    description: 'Our most popular tier with premium benefits',
    features: [
      '50% referral commission (KES 1,750 per Silver referral)',
      'Access to premium tasks (KES 100-200)',
      'Priority in competitions',
      'Silver member badge',
      'Reduced fees on withdrawals',
      'Access to exclusive opportunities',
    ],
    popular: true,
    color: 'bg-gradient-to-r from-earnhub-red to-earnhub-dark',
    cta: 'Upgrade to Silver',
  },
  {
    id: 'gold',
    name: 'Gold',
    price: 7500,
    description: 'Maximum benefits and highest earning potential',
    features: [
      '55% referral commission (KES 1,924 per Silver referral)',
      'Access to all tasks including VIP (KES 100-500)',
      'Free entry to all competitions',
      'Gold member badge',
      'No withdrawal fees',
      'Priority support & ambassador eligibility',
      'Custom online store (KES 5,000 value)',
    ],
    popular: false,
    color: 'bg-[#EAB308]',
    cta: 'Upgrade to Gold',
  },
];

const Membership = () => {
  const [userTier, setUserTier] = useState('basic'); // Set this based on user's actual membership

  return (
    <section id="membership" className="py-20 px-6 md:px-10 bg-earnhub-lightGray relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-[400px] h-[400px] bg-gradient-radial from-earnhub-blue/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-[300px] h-[300px] bg-gradient-radial from-earnhub-red/5 to-transparent rounded-full blur-3xl"></div>
      </div>
      
      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 max-w-3xl mx-auto">
          <div className="inline-block px-4 py-1 rounded-full bg-earnhub-red/10 mb-4">
            <p className="text-earnhub-red font-medium text-sm">Upgrade for higher earnings</p>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold text-earnhub-dark mb-4 balance-text">
            Choose Your Path to Success
          </h2>
          
          <p className="text-earnhub-darkGray text-lg balance-text">
            The higher your tier, the more you earn. Gold members make up to <span className="font-bold text-earnhub-red">37.5% more</span> on referrals than Starter members!
          </p>
        </div>
        
        {/* Current Membership (if not on landing page) */}
        {window.location.pathname !== '/' && (
          <div className="mb-10 text-center">
            <p className="text-earnhub-dark font-medium">Your current membership: 
              <span className={cn(
                "ml-2 px-3 py-1 rounded-full text-white",
                userTier === 'basic' ? "bg-earnhub-dark" :
                userTier === 'bronze' ? "bg-amber-600" :
                userTier === 'silver' ? "bg-gradient-to-r from-earnhub-red to-earnhub-dark" :
                "bg-[#EAB308]"
              )}>
                {userTier === 'basic' ? "Starter" : 
                 userTier === 'bronze' ? "Bronze" :
                 userTier === 'silver' ? "Silver" : "Gold"}
              </span>
            </p>
          </div>
        )}
        
        {/* Membership Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {membershipTiers.map((tier, index) => (
            <div 
              key={tier.id}
              className={cn(
                "rounded-2xl overflow-hidden transition-all duration-300 hover:-translate-y-2",
                "bg-white border border-earnhub-gray/20 shadow-card h-full flex flex-col",
                tier.popular && "md:scale-[1.05] z-10",
                userTier === tier.id && "ring-2 ring-earnhub-red ring-offset-2"
              )}
            >
              {/* Card Header */}
              <div 
                className={cn(
                  "p-6 text-white relative",
                  tier.color
                )}
              >
                {tier.popular && (
                  <div className="absolute top-0 right-0 bg-white text-earnhub-red text-xs font-bold px-3 py-1 rounded-bl-lg">
                    MOST POPULAR
                  </div>
                )}
                <h3 className="text-2xl font-bold mb-1">{tier.name}</h3>
                <div className="flex items-end gap-1 mb-3">
                  {tier.originalPrice ? (
                    <>
                      <span className="text-3xl font-bold">KES {tier.price.toLocaleString()}</span>
                      <span className="opacity-80 mb-1 line-through ml-2">KES {tier.originalPrice.toLocaleString()}</span>
                      <span className="bg-white text-earnhub-red text-xs font-bold px-2 py-0.5 rounded-full ml-2">SAVE 30%</span>
                    </>
                  ) : (
                    <span className="text-3xl font-bold">KES {tier.price.toLocaleString()}</span>
                  )}
                </div>
                <p className="text-white/90 text-sm">{tier.description}</p>
              </div>
              
              {/* Card Content */}
              <div className="p-6 flex-1 flex flex-col">
                <ul className="space-y-4 mb-8 flex-1">
                  {tier.features.map((feature, i) => (
                    <li key={i} className="flex items-start gap-3">
                      <span className="mt-0.5 text-earnhub-red">
                        <Check size={18} />
                      </span>
                      <span className="text-earnhub-dark">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button 
                  className={cn(
                    "w-full text-white",
                    tier.id === 'basic' ? "bg-earnhub-dark hover:bg-earnhub-dark/90" :
                    tier.id === 'bronze' ? "bg-gradient-to-r from-amber-700 to-amber-500 hover:from-amber-600 hover:to-amber-400" :
                    tier.id === 'silver' ? "bg-earnhub-red hover:bg-earnhub-red/90" :
                    "bg-[#EAB308] hover:bg-[#EAB308]/90"
                  )}
                  disabled={userTier === tier.id}
                >
                  {userTier === tier.id ? "Current Plan" : tier.cta}
                </Button>
              </div>
            </div>
          ))}
        </div>
        
        {/* Additional Info */}
        <div className="mt-12 text-center">
          <p className="text-earnhub-darkGray">
            All memberships are one-time payments for lifetime access. <br />
            <span className="font-medium text-earnhub-dark">Need help choosing? <a href="#" className="text-earnhub-red underline hover:text-earnhub-red/80">Contact us</a></span>
          </p>
        </div>
      </div>
    </section>
  );
};

export default Membership;
