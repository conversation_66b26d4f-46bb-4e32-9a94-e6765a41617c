
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Briefcase, Users, BarChart2, MessageSquare, ChevronRight } from "lucide-react";
import anime from "animejs";

// Mock opportunity data - would be replaced with Sanity CMS integration
const opportunities = [
  {
    id: "1",
    title: "Content Writer",
    category: "Job",
    type: "Remote",
    company: "Tech Startup",
    pay: "KES 2,500/article",
    requirements: "Good English, Research Skills",
    featured: true
  },
  {
    id: "2",
    title: "QA Testing Community",
    category: "Program",
    type: "Flexible",
    pay: "KES 1,000/test",
    description: "Join our community of app testers",
    badge: "Popular"
  },
  {
    id: "3",
    title: "Ambassador Program",
    category: "Program",
    type: "Part-time",
    pay: "10% Bonus Commission",
    description: "Represent EarnHub on social media",
    badge: "New"
  },
  {
    id: "4",
    title: "Trading Community",
    category: "Program",
    type: "Learn & Earn",
    pay: "Variable",
    description: "Learn trading skills while earning"
  },
  {
    id: "5",
    title: "Health Product Sales",
    category: "Sales",
    type: "Commission-based",
    pay: "30% Commission",
    description: "Sell health supplements to your network"
  }
];

const OpportunitiesPage = () => {
  const [currentTab, setCurrentTab] = useState("all");
  
  useEffect(() => {
    // Animation for the opportunity cards
    anime({
      targets: '.opportunity-card',
      translateY: [20, 0],
      opacity: [0, 1],
      delay: anime.stagger(100),
      easing: 'easeOutExpo'
    });
    
    // Title animation
    anime({
      targets: '.page-title',
      translateX: [-20, 0],
      opacity: [0, 1],
      easing: 'easeOutExpo',
      duration: 800
    });
  }, [currentTab]);

  const getIconForCategory = (category: string) => {
    switch(category) {
      case 'Job': return <Briefcase className="h-5 w-5 text-blue-500" />;
      case 'Program': return <Users className="h-5 w-5 text-earnhub-red" />;
      case 'Sales': return <BarChart2 className="h-5 w-5 text-green-500" />;
      default: return <MessageSquare className="h-5 w-5 text-purple-500" />;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20">
      <div className="mb-8">
        <h1 className="text-2xl md:text-3xl font-bold mb-2 page-title flex items-center">
          <Briefcase className="mr-2 text-earnhub-red" />
          Earning Opportunities
        </h1>
        <p className="text-earnhub-darkGray">Discover ways to boost your income</p>
      </div>
      
      <Tabs defaultValue="all" onValueChange={setCurrentTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="jobs">Jobs</TabsTrigger>
          <TabsTrigger value="programs">Programs</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="space-y-4">
          {opportunities.map((opp) => (
            <Card key={opp.id} className={`opportunity-card ${opp.featured ? 'border-earnhub-red bg-earnhub-red/5' : ''} hover:shadow-md transition-all`}>
              <CardContent className="p-5">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      {getIconForCategory(opp.category)}
                      <h3 className="font-medium text-lg">{opp.title}</h3>
                      {opp.badge && (
                        <Badge variant="outline" className={opp.badge === 'New' ? 'bg-green-50 text-green-700 border-green-100' : 'bg-purple-50 text-purple-700 border-purple-100'}>
                          {opp.badge}
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge>{opp.category}</Badge>
                      <Badge variant="outline">{opp.type}</Badge>
                      {opp.company && <span className="text-sm">{opp.company}</span>}
                    </div>
                    
                    <div className="text-sm text-earnhub-darkGray mb-1">
                      {opp.description || opp.requirements}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-earnhub-red mb-2">{opp.pay}</div>
                    <Button size="sm">
                      Details <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
        
        <TabsContent value="jobs" className="space-y-4">
          {opportunities.filter(opp => opp.category === 'Job').map((opp) => (
            <Card key={opp.id} className={`opportunity-card ${opp.featured ? 'border-earnhub-red bg-earnhub-red/5' : ''} hover:shadow-md transition-all`}>
              <CardContent className="p-5">
                {/* Same content structure as above */}
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      {getIconForCategory(opp.category)}
                      <h3 className="font-medium text-lg">{opp.title}</h3>
                    </div>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge>{opp.category}</Badge>
                      <Badge variant="outline">{opp.type}</Badge>
                      {opp.company && <span className="text-sm">{opp.company}</span>}
                    </div>
                    
                    <div className="text-sm text-earnhub-darkGray mb-1">
                      {opp.description || opp.requirements}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-earnhub-red mb-2">{opp.pay}</div>
                    <Button size="sm">
                      Details <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {opportunities.filter(opp => opp.category === 'Job').length === 0 && (
            <Card>
              <CardContent className="p-5 text-center">
                <p className="text-earnhub-darkGray">No job opportunities available right now.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="programs" className="space-y-4">
          {opportunities.filter(opp => opp.category === 'Program').map((opp) => (
            <Card key={opp.id} className="opportunity-card hover:shadow-md transition-all">
              <CardContent className="p-5">
                {/* Same content structure */}
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      {getIconForCategory(opp.category)}
                      <h3 className="font-medium text-lg">{opp.title}</h3>
                      {opp.badge && (
                        <Badge variant="outline" className={opp.badge === 'New' ? 'bg-green-50 text-green-700 border-green-100' : 'bg-purple-50 text-purple-700 border-purple-100'}>
                          {opp.badge}
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge>{opp.category}</Badge>
                      <Badge variant="outline">{opp.type}</Badge>
                    </div>
                    
                    <div className="text-sm text-earnhub-darkGray mb-1">
                      {opp.description || opp.requirements}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-earnhub-red mb-2">{opp.pay}</div>
                    <Button size="sm">
                      Details <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
        
        <TabsContent value="sales" className="space-y-4">
          {opportunities.filter(opp => opp.category === 'Sales').map((opp) => (
            <Card key={opp.id} className="opportunity-card hover:shadow-md transition-all">
              <CardContent className="p-5">
                {/* Same content structure */}
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      {getIconForCategory(opp.category)}
                      <h3 className="font-medium text-lg">{opp.title}</h3>
                    </div>
                    
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge>{opp.category}</Badge>
                      <Badge variant="outline">{opp.type}</Badge>
                    </div>
                    
                    <div className="text-sm text-earnhub-darkGray mb-1">
                      {opp.description || opp.requirements}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-earnhub-red mb-2">{opp.pay}</div>
                    <Button size="sm">
                      Details <ChevronRight className="ml-1 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {opportunities.filter(opp => opp.category === 'Sales').length === 0 && (
            <Card>
              <CardContent className="p-5 text-center">
                <p className="text-earnhub-darkGray">No sales opportunities available right now.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default OpportunitiesPage;
