// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://flfvacykstsgijwvcnfz.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZsZnZhY3lrc3RzZ2lqd3ZjbmZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMjg2OTIsImV4cCI6MjA2MzkwNDY5Mn0.35eMt5sgXPaDErUzSkXI7gAHbi0fa7pV9vGHNkD8i5w";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);