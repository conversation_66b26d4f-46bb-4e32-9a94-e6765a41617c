
export default {
  name: 'notificationPreference',
  title: 'Notification Preferences',
  type: 'document',
  fields: [
    {
      name: 'userId',
      title: 'User ID',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'emailNotifications',
      title: 'Email Notifications',
      type: 'boolean',
      initialValue: true
    },
    {
      name: 'pushNotifications',
      title: 'Push Notifications',
      type: 'boolean',
      initialValue: true
    },
    {
      name: 'typePreferences',
      title: 'Type Preferences',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'type',
              title: 'Notification Type',
              type: 'string',
              options: {
                list: [
                  { title: 'System', value: 'system' },
                  { title: 'Account', value: 'account' },
                  { title: 'Competition', value: 'competition' },
                  { title: 'Task', value: 'task' },
                  { title: 'Promotion', value: 'promotion' },
                  { title: 'Partner', value: 'partner' },
                  { title: 'Recommendation', value: 'recommendation' }
                ]
              }
            },
            {
              name: 'enabled',
              title: 'Enabled',
              type: 'boolean',
              initialValue: true
            }
          ]
        }
      ]
    },
    {
      name: 'priorityThreshold',
      title: 'Priority Threshold',
      type: 'string',
      options: {
        list: [
          { title: 'All Notifications', value: 'all' },
          { title: 'Medium and High Only', value: 'medium' },
          { title: 'High Priority Only', value: 'high' }
        ]
      },
      initialValue: 'all'
    }
  ]
}
