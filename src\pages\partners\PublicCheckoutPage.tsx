
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, CreditCard, ShoppingCart, Trash2 } from "lucide-react";

const PublicCheckoutPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const affiliateRef = searchParams.get('ref') || localStorage.getItem('earnhub-affiliate-ref');
  
  // Cart state
  const [items, setItems] = useState<any[]>([]);
  const [totalPrice, setTotalPrice] = useState(0);
  
  // Form state
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  
  // Load cart from localStorage on mount
  useEffect(() => {
    const savedCart = localStorage.getItem('earnhub-public-cart');
    if (savedCart) {
      try {
        const parsedCart = JSON.parse(savedCart);
        setItems(parsedCart);
        
        // Calculate total price
        const newTotalPrice = parsedCart.reduce(
          (sum: number, item: any) => sum + (item.price * item.quantity), 0
        );
        setTotalPrice(newTotalPrice);
      } catch (error) {
        console.error('Failed to parse saved cart');
      }
    }
  }, []);
  
  // Remove item from cart
  const removeItem = (productId: string) => {
    const newItems = items.filter(item => item.productId !== productId);
    setItems(newItems);
    
    // Update localStorage
    localStorage.setItem('earnhub-public-cart', JSON.stringify(newItems));
    
    // Recalculate total price
    const newTotalPrice = newItems.reduce(
      (sum: number, item: any) => sum + (item.price * item.quantity), 0
    );
    setTotalPrice(newTotalPrice);
  };
  
  // Update item quantity
  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeItem(productId);
      return;
    }
    
    const newItems = items.map(item => 
      item.productId === productId 
        ? { ...item, quantity: newQuantity }
        : item
    );
    
    setItems(newItems);
    
    // Update localStorage
    localStorage.setItem('earnhub-public-cart', JSON.stringify(newItems));
    
    // Recalculate total price
    const newTotalPrice = newItems.reduce(
      (sum: number, item: any) => sum + (item.price * item.quantity), 0
    );
    setTotalPrice(newTotalPrice);
  };
  
  // Handle checkout
  const handleCheckout = (e: React.FormEvent) => {
    e.preventDefault();
    
    // In a real app, you would submit the order to an API
    // Include the affiliateRef in the order data
    
    // Show success message
    alert("Order placed successfully!");
    
    // Clear cart
    localStorage.removeItem('earnhub-public-cart');
    
    // Redirect to a thank you page
    navigate('/p/thank-you');
  };
  
  // Format price helper
  const formatPrice = (price: number) => {
    return `KES ${price.toLocaleString()}`;
  };
  
  // Empty cart view
  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-white">
        {/* Simple header for the public page */}
        <header className="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
          <div className="container mx-auto px-4 py-4 flex justify-between items-center">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-earnhub-red">EarnHub</h1>
            </div>
            <Button size="sm" onClick={() => window.location.href = "/login"}>Sign In</Button>
          </div>
        </header>
        
        <div className="container mx-auto px-4 py-8 pt-20">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle>Your cart is empty</CardTitle>
              <CardDescription>Add some items to your cart before checkout</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-center py-8">
                <ShoppingCart className="h-16 w-16 text-gray-300" />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={() => navigate(-1)} className="w-full">
                Continue Shopping
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-white">
      {/* Simple header for the public page */}
      <header className="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <h1 className="text-xl font-bold text-earnhub-red">EarnHub</h1>
          </div>
          <Button size="sm" onClick={() => window.location.href = "/login"}>Sign In</Button>
        </div>
      </header>
      
      <div className="container mx-auto px-4 py-8 pt-20">
        {/* Affiliate banner if referred */}
        {affiliateRef && (
          <Card className="mb-6 border-earnhub-red">
            <CardContent className="p-4 flex items-center justify-center text-center">
              <p>You're shopping through an <strong>EarnHub Partner</strong> with code: <strong>{affiliateRef}</strong></p>
            </CardContent>
          </Card>
        )}
        
        <Button 
          variant="ghost" 
          onClick={() => navigate(-1)}
          className="mb-6 flex items-center"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Continue Shopping
        </Button>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Your Cart</CardTitle>
                <CardDescription>Review your items before checkout</CardDescription>
              </CardHeader>
              <CardContent>
                {items.map((item) => (
                  <div key={item.productId} className="flex gap-4 py-4 border-b last:border-0">
                    <div className="w-16 h-16 bg-gray-100 rounded flex-shrink-0">
                      {item.image && (
                        <img
                          src={item.image}
                          alt={item.title}
                          className="w-full h-full object-cover rounded"
                        />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <h3 className="font-medium">{item.title}</h3>
                        <span>{formatPrice(item.price * item.quantity)}</span>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <div className="flex items-center">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateQuantity(item.productId, item.quantity - 1)}
                            className="h-7 w-7 p-0"
                          >
                            -
                          </Button>
                          <span className="mx-2">{item.quantity}</span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateQuantity(item.productId, item.quantity + 1)}
                            className="h-7 w-7 p-0"
                          >
                            +
                          </Button>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeItem(item.productId)}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
          
          {/* Checkout Form */}
          <div className="md:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>{formatPrice(totalPrice)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Shipping</span>
                    <span>Free</span>
                  </div>
                  {affiliateRef && (
                    <div className="flex items-center justify-between border rounded p-2 bg-gray-50">
                      <span className="text-sm">Affiliate Code</span>
                      <span className="font-mono text-sm">{affiliateRef}</span>
                    </div>
                  )}
                  <Separator />
                  <div className="flex justify-between font-bold">
                    <span>Total</span>
                    <span>{formatPrice(totalPrice)}</span>
                  </div>
                </div>
                
                <form onSubmit={handleCheckout} className="mt-6 space-y-4">
                  <div>
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      required
                    />
                  </div>
                  
                  <Button type="submit" className="w-full" size="lg">
                    <CreditCard className="mr-2 h-4 w-4" />
                    Complete Purchase
                  </Button>
                </form>
              </CardContent>
              <CardFooter className="flex flex-col">
                <p className="text-sm text-gray-500 text-center">
                  By placing your order, you agree to EarnHub's terms and conditions.
                </p>
              </CardFooter>
            </Card>
          </div>
        </div>
        
        {/* EarnHub Promotion */}
        <div className="text-center mt-16 mb-8">
          <h2 className="text-xl font-bold mb-4">Join EarnHub Today</h2>
          <p className="max-w-2xl mx-auto mb-6 text-gray-600">
            Create an account to track your orders, access exclusive products, and earn rewards.
          </p>
          <Button variant="outline" onClick={() => window.location.href = "/signup"}>
            Sign Up for Free
          </Button>
        </div>
      </div>

      {/* Simple Footer */}
      <footer className="bg-gray-100 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} EarnHub. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PublicCheckoutPage;
