import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useIsMobile } from "@/hooks/use-mobile";
import { 
  TrendingUp, Briefcase, Zap, User, Award, 
  Users, Ticket, ShoppingCart, Medal, Wallet,
  Clock, Calendar, Bell, ChevronRight, ArrowRight,
  Eye, Copy, ExternalLink
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import TrustBadges from "@/components/TrustBadges";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import anime from "animejs";
import { LiveUpdate, NewsItem } from "@/types/competitions";
import LiveUpdatesScroller from "@/components/LiveUpdatesScroller";
import QuickAccessGrid from "@/components/QuickAccessGrid";
import { Link } from "react-router-dom";

// Mock data - would be replaced with Sanity CMS integration
const newsItems = [
  {
    id: "1",
    title: "New Referral Program",
    description: "Earn 2x points for each referral this week!",
    date: "2025-05-09",
    image: "/placeholder.svg",
    category: "Promotion"
  },
  {
    id: "2",
    title: "Essay Competition",
    description: "Join our monthly writing contest and win up to KES 5,000",
    date: "2025-05-10",
    image: "/placeholder.svg",
    category: "Competition"
  },
  {
    id: "3",
    title: "App Update",
    description: "We've added new features to make earning easier",
    date: "2025-05-11",
    image: "/placeholder.svg",
    category: "Update"
  },
  {
    id: "4",
    title: "New Partner: Tech Company",
    description: "Apply for remote testing jobs starting at KES 2,000",
    date: "2025-05-12",
    image: "/placeholder.svg",
    category: "Partnership"
  },
  {
    id: "5",
    title: "Limited Time Offer",
    description: "50% off Silver membership upgrade until May 20th",
    date: "2025-05-13",
    image: "/placeholder.svg",
    category: "Promotion"
  }
];

// Live updates data - would be replaced with real-time Sanity CMS data
const liveUpdates: LiveUpdate[] = [
  { id: "1", type: "withdrawal", user: "John K.", action: "withdrew KES 5,000", timestamp: "2 minutes ago" },
  { id: "2", type: "competition", user: "Mary W.", action: "won 1st place in Essay Contest", timestamp: "15 minutes ago" },
  { id: "3", type: "achievement", user: "David M.", action: "reached 50 referrals", timestamp: "1 hour ago" },
  { id: "4", type: "referral", user: "Susan T.", action: "referred 3 new members today", timestamp: "2 hours ago" },
  { id: "5", type: "task", user: "James O.", action: "completed 10 tasks this week", timestamp: "5 hours ago" },
  { id: "6", type: "withdrawal", user: "Patricia L.", action: "withdrew KES 10,000", timestamp: "6 hours ago" },
  { id: "7", type: "achievement", user: "Michael K.", action: "upgraded to Gold membership", timestamp: "1 day ago" },
];

const featuredItems = [
  { 
    id: 1, 
    title: "Mobile App Survey", 
    reward: "KES 1,200",
    expiresIn: "2 days",
    type: "task"
  },
  { 
    id: 2, 
    title: "Weekly Trivia Challenge", 
    reward: "KES 800",
    expiresIn: "5 hours",
    type: "competition" 
  },
  {
    id: 3,
    title: "Referral Bonus Weekend",
    reward: "55% Commission",
    expiresIn: "3 days",
    type: "promotion"
  },
  {
    id: 4,
    title: "Content Writer Needed",
    reward: "KES 2,500",
    expiresIn: "1 day",
    type: "opportunity"
  }
];

const recentReferrals = [
  { name: "James K.", status: "Joined", date: "2025-05-11" },
  { name: "Mary W.", status: "Pending", date: "2025-05-10" }
];

const Dashboard = () => {
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const animationRef = useRef(null);
  
  // Animation for the welcome section
  useEffect(() => {
    anime({
      targets: '.animate-welcome',
      translateY: [20, 0],
      opacity: [0, 1],
      easing: 'easeOutExpo',
      duration: 1200,
      delay: (el, i) => 300 + 100 * i
    });
    
    anime({
      targets: '.animate-card',
      scale: [0.95, 1],
      opacity: [0, 1],
      easing: 'easeOutExpo',
      duration: 800,
      delay: (el, i) => 600 + 100 * i
    });
  }, []);

  const showRaffleToast = () => {
    toast({
      title: "Entered daily raffle!",
      description: "You've successfully entered today's raffle. Good luck!",
    });
  };

  const copyReferralLink = () => {
    navigator.clipboard.writeText(`earnhub.com/ref/${referralCode}`);
    toast({
      title: "Copied!",
      description: "Referral link copied to clipboard.",
    });
  };

  // Mock data
  const earningsSummary = {
    totalEarnings: "10,500",
    pendingWithdrawals: "2,000",
    todayEarnings: "500",
    availableBalance: "8,500",
  };

  const referralCode = "EARN123456";
  const loginStreak = 5;

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20">
      <div className="mb-8 animate-welcome">
        <h1 className="text-2xl md:text-3xl font-bold mb-2">Welcome Back, User!</h1>
        <p className="text-earnhub-darkGray">Here's your earning summary today</p>
        
        {/* Login Streak */}
        <div className="mt-2 flex items-center">
          <span className="text-sm font-medium flex items-center">
            <Calendar className="h-4 w-4 mr-1 text-earnhub-red" />
            Login streak: <span className="font-bold ml-1">{loginStreak} days</span>
          </span>
          <Badge variant="outline" className="ml-2 bg-yellow-100 text-yellow-800 border-yellow-200">
            Keep it up!
          </Badge>
        </div>
      </div>

      {/* Enhanced Earnings Summary with functional buttons */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <Card className="animate-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-earnhub-darkGray">Total Earnings</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-xl md:text-2xl font-bold text-earnhub-dark">KES {earningsSummary.totalEarnings}</p>
            <Link to="/wallet">
              <Button variant="ghost" size="sm" className="mt-2 text-xs text-earnhub-red hover:text-earnhub-red/80 p-0 h-auto">
                View Details <ChevronRight className="h-3 w-3 ml-1" />
              </Button>
            </Link>
          </CardContent>
        </Card>
        
        <Card className="animate-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-earnhub-darkGray">Pending Withdrawals</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-xl md:text-2xl font-bold text-earnhub-dark">KES {earningsSummary.pendingWithdrawals}</p>
            <Link to="/wallet">
              <Button variant="ghost" size="sm" className="mt-2 text-xs text-earnhub-red hover:text-earnhub-red/80 p-0 h-auto">
                Manage <ChevronRight className="h-3 w-3 ml-1" />
              </Button>
            </Link>
          </CardContent>
        </Card>
        
        <Card className="animate-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-earnhub-darkGray">Today's Earnings</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-xl md:text-2xl font-bold text-green-600">KES {earningsSummary.todayEarnings}</p>
            <Link to="/tasks">
              <Button variant="ghost" size="sm" className="mt-2 text-xs text-green-600 hover:text-green-500 p-0 h-auto">
                Earn More <ChevronRight className="h-3 w-3 ml-1" />
              </Button>
            </Link>
          </CardContent>
        </Card>
        
        <Card className="animate-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-earnhub-darkGray">Available Balance</CardTitle>
          </CardHeader>
          <CardContent className="pt-0 flex flex-col">
            <p className="text-xl md:text-2xl font-bold text-earnhub-red">KES {earningsSummary.availableBalance}</p>
            <Link to="/wallet">
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-2 text-xs border-earnhub-red text-earnhub-red hover:bg-earnhub-red hover:text-white"
              >
                <Wallet className="h-3 w-3 mr-1" /> Withdraw
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      {/* Quick Access / Feature Navigation */}
      <Card className="mb-8 animate-card">
        <CardHeader>
          <CardTitle className="text-lg">Quick Access</CardTitle>
          <CardDescription>Access your favorite features quickly</CardDescription>
        </CardHeader>
        <CardContent>
          <QuickAccessGrid />
        </CardContent>
      </Card>

      {/* Live Updates Continuous Scroller */}
      <Card className="mb-8 animate-card overflow-hidden">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center text-lg">
            <TrendingUp className="mr-2 text-earnhub-red" /> 
            Live Updates
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <LiveUpdatesScroller updates={liveUpdates} />
        </CardContent>
      </Card>

      {/* Enhanced News Updates with functional View All */}
      <Card className="mb-8 animate-card">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="flex items-center">
            <Bell className="mr-2 text-earnhub-red" /> 
            News & Updates
          </CardTitle>
          <Link to="/news">
            <Button variant="ghost" size="sm" className="text-earnhub-red hover:text-earnhub-red/80">
              View All <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </Link>
        </CardHeader>
        <CardContent className="pt-0">
          <Carousel className="w-full">
            <CarouselContent className="-ml-4">
              {newsItems.map((item) => (
                <CarouselItem key={item.id} className="pl-4 md:basis-1/2 lg:basis-1/3">
                  <div className="p-1">
                    <Card>
                      <CardContent className="flex p-4">
                        <div className="w-12 h-12 rounded-md bg-gray-100 flex-shrink-0 mr-3">
                          <img src={item.image} alt="" className="w-full h-full object-cover rounded-md" />
                        </div>
                        <div>
                          <div className="flex items-center mb-1">
                            <h3 className="font-medium text-sm">{item.title}</h3>
                            <Badge 
                              className="ml-2" 
                              variant={item.category === "Promotion" ? "default" : 
                                     (item.category === "Competition" ? "secondary" : "outline")}
                            >
                              {item.category}
                            </Badge>
                          </div>
                          <p className="text-earnhub-darkGray text-xs mb-1">{item.description}</p>
                          <span className="text-xs text-gray-400">{item.date}</span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="absolute right-0 top-1/2 -translate-y-1/2 flex space-x-2">
              <CarouselPrevious className="relative -left-4" />
              <CarouselNext className="relative -left-4" />
            </div>
          </Carousel>
        </CardContent>
      </Card>

      {/* Enhanced Featured Section with functional View All */}
      <Card className="mb-8 animate-card">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="flex items-center">
            <Award className="mr-2 text-earnhub-red" /> 
            Featured Opportunities
          </CardTitle>
          <Link to="/opportunities">
            <Button variant="ghost" size="sm" className="text-earnhub-red hover:text-earnhub-red/80">
              View All <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </Link>
        </CardHeader>
        <CardContent className="pt-0">
          <Carousel className="w-full">
            <CarouselContent className="-ml-4">
              {featuredItems.map((item) => (
                <CarouselItem key={item.id} className="pl-4 md:basis-1/2 lg:basis-1/3">
                  <div className="p-1">
                    <Card className="border border-gray-100 hover:border-earnhub-red transition-colors hover:shadow-md">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h3 className="font-medium">{item.title}</h3>
                            <div className="mt-1 flex items-center text-xs text-gray-500">
                              <Clock className="w-3 h-3 mr-1" /> 
                              Expires in: <span className="font-medium ml-1 text-earnhub-red">{item.expiresIn}</span>
                            </div>
                          </div>
                          <Badge className="bg-green-100 text-green-800 border-green-200">{item.reward}</Badge>
                        </div>
                        <div className="mt-3 text-right">
                          <Button size="sm" className="bg-earnhub-red hover:bg-earnhub-red/90">
                            View Details
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="absolute right-0 top-1/2 -translate-y-1/2 flex space-x-2">
              <CarouselPrevious className="relative -left-4" />
              <CarouselNext className="relative -left-4" />
            </div>
          </Carousel>
        </CardContent>
      </Card>

      {/* Two-column layout for Referrals and Hot Topics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* Referral Section */}
        <Card className="animate-card">
          <CardHeader>
            <CardTitle>Your Referral Link</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-100 p-3 rounded-md flex items-center justify-between mb-4">
              <code className="text-sm md:text-base overflow-x-auto whitespace-nowrap flex-1 mr-2">{`earnhub.com/ref/${referralCode}`}</code>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={copyReferralLink}
                >
                  <Copy className="h-3 w-3 mr-1" />
                  Copy
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    if (navigator.share) {
                      navigator.share({
                        title: 'Join EarnHub',
                        text: 'Start earning with EarnHub!',
                        url: `earnhub.com/ref/${referralCode}`
                      });
                    } else {
                      copyReferralLink();
                    }
                  }}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Share
                </Button>
              </div>
            </div>
            <div className="text-earnhub-darkGray text-sm mb-4">
              <p>Earn <span className="font-bold text-earnhub-red">KES 400</span> for every friend that joins with your referral link! <span className="underline">Limited time: 55% commissions!</span></p>
            </div>
            
            {/* Recent Referrals */}
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-medium text-sm">Recent Referrals</h3>
              <Link to="/referrals">
                <Button variant="ghost" size="sm" className="text-xs text-earnhub-red hover:text-earnhub-red/80 p-0 h-auto">
                  View All <ChevronRight className="h-3 w-3 ml-1" />
                </Button>
              </Link>
            </div>
            
            {recentReferrals.length > 0 ? (
              <div className="space-y-2">
                {recentReferrals.slice(0, 3).map((referral, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-earnhub-red/10 text-earnhub-red flex items-center justify-center mr-2">
                        {referral.name.charAt(0)}
                      </div>
                      <span className="text-sm">{referral.name}</span>
                    </div>
                    <div className="text-xs">
                      <Badge variant={referral.status === "Joined" ? "default" : "outline"}>
                        {referral.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No recent referrals</p>
            )}
          </CardContent>
        </Card>

        {/* Trust Badges in dashboard */}
        <Card className="animate-card">
          <CardHeader>
            <CardTitle>Why EarnHub?</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {[
                {
                  icon: "🔒",
                  title: "Secure Payments",
                  description: "Fast & protected M-Pesa withdrawals"
                },
                {
                  icon: "✅",
                  title: "Verified Tasks",
                  description: "All earning methods pre-screened"
                },
                {
                  icon: "💰",
                  title: "Multiple Ways to Earn",
                  description: "Tasks, referrals, competitions & more"
                },
                {
                  icon: "🚀",
                  title: "Growing Community",
                  description: "Join 10,000+ earning members"
                }
              ].map((badge, index) => (
                <div key={index} className="flex flex-col items-center text-center p-3 rounded-lg bg-gray-50">
                  <div className="text-3xl mb-2">{badge.icon}</div>
                  <h3 className="text-earnhub-dark font-medium mb-1 text-sm">{badge.title}</h3>
                  <p className="text-earnhub-darkGray text-xs">{badge.description}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Floating Action Button - Join Raffle */}
      <Link to="/raffle">
        <div className="fixed bottom-20 right-5 z-40">
          <Button 
            className="h-16 w-16 rounded-full bg-yellow-500 hover:bg-yellow-600 shadow-lg transition-all duration-300 hover:scale-110" 
            onClick={(e) => {
              e.preventDefault();
              showRaffleToast();
            }}
          >
            <span className="animate-pulse text-2xl">🎲</span>
          </Button>
        </div>
      </Link>
    </div>
  );
};

export default Dashboard;
