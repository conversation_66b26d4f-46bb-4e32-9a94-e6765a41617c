// sanity/schemaTypes/newsAndUpdate.ts
import {Rule} from 'sanity'

export default {
  name: 'newsAndUpdate',
  title: 'News & Update',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'content',
      title: 'Full Content',
      type: 'array',
      of: [{type: 'block'}], // Rich text editor
    },
    {
      name: 'excerpt',
      title: 'Short Excerpt',
      type: 'text',
    },
    {
      name: 'category',
      title: 'Category',
      type: 'string',
      options: {
        list: ['platform_update', 'new_feature', 'announcement', 'maintenance', 'community'],
      },
    },
    {
      name: 'publishDate',
      title: 'Publish Date',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    },
    {
      name: 'author',
      title: 'Author Name',
      type: 'string',
      initialValue: 'EarnHub Team',
    },
    {
      name: 'featuredImage',
      title: 'Featured Image',
      type: 'image',
      options: {
        hotspot: true,
      },
    },
    {
      name: 'tags',
      title: 'Tags',
      type: 'array',
      of: [{type: 'string'}],
    },
    {
      name: 'isPinned',
      title: 'Pin to Top',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: ['draft', 'published', 'archived'],
      },
      initialValue: 'draft',
    },
  ],
}
