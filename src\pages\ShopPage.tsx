
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useQuery } from '@tanstack/react-query';
import { fetchAllProducts, fetchProductCategories } from '@/services/shopService';
import { ProductFilter } from '@/types/shop';
import { FeaturedProducts } from '@/components/shop/FeaturedProducts';
import { NewsCarousel } from '@/components/shop/NewsCarousel';
import { CategoryList } from '@/components/shop/CategoryList';
import { ProductGrid } from '@/components/shop/ProductGrid';
import { CartDrawer } from '@/components/shop/CartDrawer';

// Add CSS for the hidden scrollbar
const hiddenScrollbarStyles = `
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
`;

const ShopPage = () => {
  const [filter, setFilter] = useState<ProductFilter>({
    category: '',
    minPrice: 0,
    maxPrice: 100000,
    sort: 'latest'
  });
  
  const { data: products, isLoading } = useQuery({
    queryKey: ['allProducts', filter],
    queryFn: () => fetchAllProducts(filter)
  });
  
  const { data: categories } = useQuery({
    queryKey: ['productCategories'],
    queryFn: fetchProductCategories
  });

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20">
      <style>{hiddenScrollbarStyles}</style>
      
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl md:text-3xl font-bold flex items-center">
          EarnHub Shop
        </h1>
        <CartDrawer />
      </div>
      
      {/* News Section */}
      <NewsCarousel />
      
      {/* Featured Products */}
      <FeaturedProducts />
      
      {/* Categories */}
      <CategoryList />
      
      {/* All Products with Tabs */}
      <div className="mt-8">
        <h2 className="text-xl font-bold mb-4">Browse Products</h2>
        
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="mb-6 bg-gray-100 p-1">
            <TabsTrigger value="all" className="data-[state=active]:bg-earnhub-red data-[state=active]:text-white">
              All Products
            </TabsTrigger>
            {categories?.map((category) => (
              <TabsTrigger 
                key={category._id} 
                value={category._id}
                className="data-[state=active]:bg-earnhub-red data-[state=active]:text-white"
              >
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>
          
          <TabsContent value="all" className="mt-0">
            <ProductGrid 
              products={products || []} 
              isLoading={isLoading}
              onFilterChange={setFilter}
              initialFilter={filter}
            />
          </TabsContent>
          
          {categories?.map((category) => (
            <TabsContent key={category._id} value={category._id} className="mt-0">
              <ProductGrid 
                products={(products || []).filter(p => p.categoryId === category._id)} 
                isLoading={isLoading}
                onFilterChange={(newFilter) => {
                  setFilter({...newFilter, category: category._id});
                }}
                initialFilter={{...filter, category: category._id}}
              />
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
};

export default ShopPage;
