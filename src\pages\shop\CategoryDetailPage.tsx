
import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery } from '@tanstack/react-query';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { fetchProductsByCategory, fetchProductCategories } from '@/services/shopService';
import { ProductGrid } from '@/components/shop/ProductGrid';
import { ProductFilter } from '@/types/shop';
import { CartDrawer } from '@/components/shop/CartDrawer';

const CategoryDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [filter, setFilter] = useState<ProductFilter>({
    category: id || '',
    minPrice: 0,
    maxPrice: 100000,
    sort: 'latest'
  });
  
  const { data: category } = useQuery({
    queryKey: ['category', id],
    queryFn: async () => {
      const categories = await fetchProductCategories();
      return categories.find(cat => cat._id === id);
    }
  });
  
  const { data: products, isLoading } = useQuery({
    queryKey: ['categoryProducts', id, filter],
    queryFn: () => fetchProductsByCategory(id || ''),
    enabled: !!id
  });

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => navigate('/shop')} className="mr-2">
            <ArrowLeft size={20} />
          </Button>
          <h1 className="text-2xl md:text-3xl font-bold">{category?.name || 'Category'}</h1>
        </div>
        <CartDrawer />
      </div>
      
      {category && (
        <Card className="mb-8">
          <div className="h-48 md:h-64 relative">
            <img 
              src={category.image || "/placeholder.svg"} 
              alt={category.name} 
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
              <CardContent className="text-white p-6">
                <h2 className="text-2xl font-bold mb-2">{category.name}</h2>
                <p>{category.description}</p>
              </CardContent>
            </div>
          </div>
        </Card>
      )}
      
      <ProductGrid 
        products={products || []} 
        isLoading={isLoading}
        onFilterChange={setFilter}
        initialFilter={filter}
      />
    </div>
  );
};

export default CategoryDetailPage;
