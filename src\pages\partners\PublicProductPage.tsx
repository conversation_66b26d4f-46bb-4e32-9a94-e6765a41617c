
import { useEffect, useState } from "react";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { client } from '@/lib/sanity';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Minus, Plus, ShoppingCart } from "lucide-react";

// A simplified version of CartContext just for this public flow
const useSimplifiedCart = () => {
  const [items, setItems] = useState<any[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPrice, setTotalPrice] = useState(0);
  
  useEffect(() => {
    // Recalculate totals whenever items change
    const newTotalItems = items.reduce((sum, item) => sum + item.quantity, 0);
    const newTotalPrice = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    setTotalItems(newTotalItems);
    setTotalPrice(newTotalPrice);
    
    // Save cart to localStorage
    localStorage.setItem('earnhub-public-cart', JSON.stringify(items));
  }, [items]);
  
  useEffect(() => {
    // Load cart from localStorage on initial load
    const savedCart = localStorage.getItem('earnhub-public-cart');
    if (savedCart) {
      try {
        setItems(JSON.parse(savedCart));
      } catch (error) {
        console.error('Failed to parse saved cart');
      }
    }
  }, []);
  
  const addToCart = (product: any) => {
    setItems(prevItems => {
      const existingItem = prevItems.find(item => item.productId === product.productId);
      if (existingItem) {
        return prevItems.map(item => 
          item.productId === product.productId 
            ? { ...item, quantity: item.quantity + product.quantity } 
            : item
        );
      } else {
        return [...prevItems, product];
      }
    });
  };
  
  return { items, addToCart, totalItems, totalPrice };
};

const PublicProductPage = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const affiliateRef = searchParams.get('ref');
  const navigate = useNavigate();
  
  const [quantity, setQuantity] = useState(1);
  const { addToCart, totalItems } = useSimplifiedCart();
  
  // Store the affiliate ref in localStorage to preserve it through the checkout flow
  useEffect(() => {
    if (affiliateRef) {
      localStorage.setItem('earnhub-affiliate-ref', affiliateRef);
    }
  }, [affiliateRef]);
  
  // Fetch product data from Sanity
  const { data: product, isLoading, error } = useQuery({
    queryKey: ['public-product', id],
    queryFn: async () => {
      const query = `*[_type == "product" && _id == $id][0] {
        _id,
        title,
        slug,
        description,
        price,
        discountedPrice,
        savings,
        "image": image.asset->url,
        categoryId,
        "category": category->name,
        featured,
        hot,
        badge,
        inStock,
        dateAdded,
        details
      }`;
      
      return await client.fetch(query, { id });
    },
    enabled: !!id
  });
  
  // Handle quantity change
  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity < 1) return;
    setQuantity(newQuantity);
  };
  
  // Handle add to cart
  const handleAddToCart = () => {
    if (!product) return;
    
    addToCart({
      productId: product._id,
      title: product.title,
      price: product.discountedPrice || product.price,
      quantity: quantity,
      image: product.image,
      savings: product.savings
    });
    
    navigate('/p/checkout');
  };
  
  // Format price helper
  const formatPrice = (price: number) => {
    return `KES ${price.toLocaleString()}`;
  };
  
  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 pt-20">
        <div className="animate-pulse space-y-4">
          <div className="h-8 w-48 bg-gray-200 rounded"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="h-80 bg-gray-200 rounded"></div>
            <div className="space-y-4">
              <div className="h-8 w-full bg-gray-200 rounded"></div>
              <div className="h-6 w-32 bg-gray-200 rounded"></div>
              <div className="h-24 w-full bg-gray-200 rounded"></div>
              <div className="h-12 w-full bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // Error state
  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8 pt-20">
        <Card className="p-8 text-center">
          <p className="text-earnhub-darkGray mb-4">Product not found or an error occurred.</p>
          <Button onClick={() => navigate('/p/shop')}>Browse Other Products</Button>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-white">
      {/* Simple header for the public page */}
      <header className="bg-white shadow-sm fixed top-0 left-0 right-0 z-50">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <h1 className="text-xl font-bold text-earnhub-red">EarnHub</h1>
          </div>
          <div className="flex items-center">
            <Button variant="outline" size="sm" className="mr-2" onClick={() => navigate('/p/checkout')}>
              <ShoppingCart className="h-4 w-4 mr-1" />
              <span>{totalItems}</span>
            </Button>
            <Button size="sm" onClick={() => window.location.href = "/login"}>Sign In</Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 pt-20">
        {/* Affiliate banner if referred */}
        {affiliateRef && (
          <Card className="mb-6 border-earnhub-red">
            <CardContent className="p-4 flex items-center justify-center text-center">
              <p>You're shopping through an <strong>EarnHub Partner</strong>. Thank you for supporting our community!</p>
            </CardContent>
          </Card>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {/* Product Image */}
          <div className="relative">
            <div className="bg-gray-100 rounded-lg overflow-hidden aspect-square">
              <img 
                src={product.image || "/placeholder.svg"} 
                alt={product.title} 
                className="w-full h-full object-contain"
              />
            </div>
            {product.hot && (
              <Badge className="absolute top-4 right-4 bg-orange-500">Hot Deal</Badge>
            )}
            {product.badge && (
              <Badge className="absolute top-4 right-4 bg-earnhub-red">{product.badge}</Badge>
            )}
          </div>
          
          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <Badge variant="outline" className="mb-2">{product.category}</Badge>
              <h1 className="text-2xl md:text-4xl font-bold">{product.title}</h1>
              <div className="mt-2">
                {product.discountedPrice ? (
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold text-earnhub-red">
                      {formatPrice(product.discountedPrice)}
                    </span>
                    <span className="text-lg text-gray-400 line-through">
                      {formatPrice(product.price)}
                    </span>
                    {product.savings && (
                      <Badge className="bg-green-500 ml-2">Save {product.savings}</Badge>
                    )}
                  </div>
                ) : (
                  <span className="text-2xl font-bold text-earnhub-red">
                    {formatPrice(product.price)}
                  </span>
                )}
              </div>
            </div>
            
            <p className="text-gray-700">{product.description}</p>
            
            <div className="p-4 bg-gray-50 rounded-md">
              <span className={product.inStock ? 'text-green-600' : 'text-red-500'}>
                {product.inStock ? 'In Stock' : 'Out of Stock'}
              </span>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="flex items-center border rounded-md">
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-10 w-10 rounded-none"
                  onClick={() => handleQuantityChange(quantity - 1)}
                  disabled={quantity <= 1}
                >
                  <Minus size={16} />
                </Button>
                <div className="w-12 text-center">{quantity}</div>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-10 w-10 rounded-none"
                  onClick={() => handleQuantityChange(quantity + 1)}
                >
                  <Plus size={16} />
                </Button>
              </div>
              
              <Button 
                className="flex-1"
                size="lg"
                onClick={handleAddToCart}
                disabled={!product.inStock}
              >
                <ShoppingCart className="mr-2" size={16} />
                Add to Cart
              </Button>
            </div>
            
            {product.details && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-2">Product Details</h3>
                <div className="prose max-w-none">
                  <p>{product.details}</p>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* EarnHub Promotion */}
        <Separator className="my-12" />
        <div className="text-center mb-12">
          <h2 className="text-2xl font-bold mb-4">Join EarnHub Today</h2>
          <p className="max-w-2xl mx-auto mb-6">
            Get access to exclusive products, earn rewards, and join a community of like-minded individuals.
          </p>
          <Button size="lg" onClick={() => window.location.href = "/signup"}>
            Sign Up for Free
          </Button>
        </div>
      </div>

      {/* Simple Footer */}
      <footer className="bg-gray-100 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-sm text-gray-600">
              &copy; {new Date().getFullYear()} EarnHub. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PublicProductPage;
