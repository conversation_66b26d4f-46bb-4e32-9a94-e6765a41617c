
import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Check, Star, Award, User, Heart } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";

// Step content for each onboarding screen
const stepContent = [
  {
    title: "Welcome to EarnHub!",
    description: "Your journey to earning online starts here. Discover how our platform can help you unlock new income opportunities.",
    image: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?auto=format&fit=crop&w=800&h=500",
    imageAlt: "Person using a laptop with financial graphs",
    points: [
      { icon: Star, text: "Complete tasks and earn real money" },
      { icon: Award, text: "Win competitions with your skills" },
      { icon: Heart, text: "Join a community of earners" }
    ]
  },
  {
    title: "Multiple Ways to Earn",
    description: "EarnHub offers diverse opportunities to match your skills and schedule.",
    image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?auto=format&fit=crop&w=800&h=500",
    imageAlt: "Person working on computer",
    points: [
      { icon: Check, text: "QA Testing: Test apps and websites" },
      { icon: Check, text: "Content Creation: Write, design, and create" },
      { icon: Check, text: "Surveys & Tasks: Quick ways to earn daily" }
    ]
  },
  {
    title: "Competitions & Rewards",
    description: "Showcase your skills in challenges and win exclusive prizes.",
    image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&w=800&h=500",
    imageAlt: "Trophy and rewards concept",
    points: [
      { icon: Check, text: "Weekly competitions with cash prizes" },
      { icon: Check, text: "Leaderboards to track your progress" },
      { icon: Check, text: "Special rewards for top performers" }
    ]
  },
  {
    title: "Complete Your Profile",
    description: "Personalize your experience and get matched with the right opportunities.",
    image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=800&h=500",
    imageAlt: "Person editing profile information",
    points: [
      { icon: User, text: "Profile completion unlocks exclusive tasks" },
      { icon: Check, text: "Earn KES 500 bonus for 100% completion" },
      { icon: Check, text: "Receive personalized opportunity matches" }
    ]
  }
];

interface OnboardingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const OnboardingModal = ({ open, onOpenChange }: OnboardingModalProps) => {
  const [currentStep, setCurrentStep] = useState(0);
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const handleNext = () => {
    if (currentStep < stepContent.length - 1) {
      setCurrentStep(prevStep => prevStep + 1);
    }
  };
  
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prevStep => prevStep - 1);
    }
  };
  
  const handleComplete = () => {
    onOpenChange(false);
    toast({
      title: "Onboarding completed!",
      description: "Now complete your profile to unlock more earning opportunities.",
    });
    navigate("/profile");
  };

  const handleExplore = () => {
    onOpenChange(false);
    toast({
      title: "Welcome to EarnHub!",
      description: "Explore the platform and discover earning opportunities.",
    });
    navigate("/dashboard");
  };
  
  const currentContent = stepContent[currentStep];
  const isLastStep = currentStep === stepContent.length - 1;
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl p-0 overflow-hidden">
        <div className="flex flex-col md:flex-row">
          {/* Left side - Image */}
          <div className="w-full md:w-1/2 bg-gray-100">
            <AspectRatio ratio={16 / 10} className="h-full">
              <img 
                src={currentContent.image} 
                alt={currentContent.imageAlt}
                className="object-cover w-full h-full"
              />
            </AspectRatio>
          </div>
          
          {/* Right side - Content */}
          <div className="w-full md:w-1/2 p-6">
            <DialogHeader className="mb-4">
              <DialogTitle className="text-2xl">{currentContent.title}</DialogTitle>
              <DialogDescription className="text-base">
                {currentContent.description}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4 mb-6">
              {currentContent.points.map((point, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="mt-0.5 text-earnhub-red">
                    <point.icon size={18} />
                  </div>
                  <p>{point.text}</p>
                </div>
              ))}
            </div>
            
            <div className="flex flex-col space-y-3">
              {isLastStep ? (
                <>
                  <Button 
                    onClick={handleComplete} 
                    className="w-full"
                  >
                    Get Premium Membership
                  </Button>
                  <Button 
                    onClick={handleExplore} 
                    variant="outline" 
                    className="w-full"
                  >
                    Explore First
                  </Button>
                </>
              ) : (
                <Button 
                  onClick={handleNext} 
                  className="w-full"
                >
                  Next
                </Button>
              )}
              
              {currentStep > 0 && !isLastStep && (
                <Button 
                  onClick={handlePrevious} 
                  variant="outline" 
                  className="w-full"
                >
                  Previous
                </Button>
              )}
            </div>
            
            {/* Progress indicators */}
            <div className="flex justify-center space-x-2 mt-6">
              {stepContent.map((_, index) => (
                <div 
                  key={index} 
                  className={`h-2 w-10 rounded-full transition-colors ${
                    index === currentStep ? 'bg-earnhub-red' : 'bg-gray-200'
                  }`} 
                />
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default OnboardingModal;
