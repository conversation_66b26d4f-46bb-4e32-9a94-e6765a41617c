// sanity/schemaTypes/liveActivityDefinition.ts
import {Rule} from 'sanity'

export default {
  name: 'liveActivityDefinition',
  title: 'Live Activity Definition',
  type: 'document',
  fields: [
    {
      name: 'activityKey',
      title: 'Activity Key',
      type: 'string',
      description: "Unique key, e.g., 'user_completed_task', 'user_joined_competition'",
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'template',
      title: 'Activity Template',
      type: 'string',
      description: "e.g., '{userName} just completed {taskName} and earned {rewardAmount} KES!'",
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'icon',
      title: 'Icon',
      type: 'string',
      description: 'Lucide icon name associated with this activity type',
    },
    {
      name: 'category',
      title: 'Category',
      type: 'string',
      options: {
        list: ['task', 'competition', 'achievement', 'referral', 'system', 'social'],
      },
    },
  ],
}
