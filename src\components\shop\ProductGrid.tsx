
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { FilterX, SlidersHorizontal } from 'lucide-react';
import { Product, ProductFilter } from '@/types/shop';
import { useCart } from '@/contexts/CartContext';

interface ProductGridProps {
  products: Product[];
  isLoading: boolean;
  onFilterChange?: (filter: ProductFilter) => void;
  initialFilter?: ProductFilter;
}

export const ProductGrid = ({ 
  products, 
  isLoading,
  onFilterChange,
  initialFilter = { category: '', minPrice: 0, maxPrice: 100000, sort: 'latest' } 
}: ProductGridProps) => {
  const { addToCart } = useCart();
  const [filter, setFilter] = useState<ProductFilter>(initialFilter);
  const [showFilters, setShowFilters] = useState(false);
  
  // Find min/max price in products
  const maxPriceInProducts = products?.length 
    ? Math.max(...products.map(p => p.price)) 
    : 100000;
  
  const handleFilterChange = (updatedFilter: Partial<ProductFilter>) => {
    const newFilter = { ...filter, ...updatedFilter } as ProductFilter;
    setFilter(newFilter);
    onFilterChange?.(newFilter);
  };
  
  const resetFilters = () => {
    const resetFilter: ProductFilter = { 
      category: '', 
      minPrice: 0, 
      maxPrice: maxPriceInProducts, 
      sort: 'latest' 
    };
    setFilter(resetFilter);
    onFilterChange?.(resetFilter);
  };
  
  const handleAddToCart = (product: Product) => {
    addToCart({
      productId: product._id,
      title: product.title,
      price: product.discountedPrice || product.price,
      quantity: 1,
      image: product.image,
      savings: product.savings
    });
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 animate-pulse">
        {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
          <div key={i} className="h-64 bg-gray-200 rounded-lg"></div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center gap-2"
        >
          <SlidersHorizontal size={16} />
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </Button>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">Sort by:</span>
          <Select
            value={filter.sort}
            onValueChange={(value: "latest" | "price-low" | "price-high" | "popular") => 
              handleFilterChange({ sort: value })
            }
          >
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="latest">Latest</SelectItem>
              <SelectItem value="price-low">Price: Low to High</SelectItem>
              <SelectItem value="price-high">Price: High to Low</SelectItem>
              <SelectItem value="popular">Most Popular</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {showFilters && (
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium">Filters</h3>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={resetFilters}
                className="text-earnhub-red flex items-center gap-1"
              >
                <FilterX size={14} />
                Reset
              </Button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-1 block">Price Range</label>
                <div className="px-2">
                  <Slider
                    defaultValue={[filter.minPrice, filter.maxPrice]}
                    max={maxPriceInProducts}
                    step={1000}
                    onValueChange={(value) => {
                      handleFilterChange({ 
                        minPrice: value[0], 
                        maxPrice: value[1] 
                      });
                    }}
                    className="mb-6"
                  />
                </div>
                <div className="flex justify-between text-sm">
                  <span>KES {filter.minPrice.toLocaleString()}</span>
                  <span>KES {filter.maxPrice.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {products.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No products found matching your criteria.</p>
          <Button onClick={resetFilters} variant="outline" className="mt-2">
            Reset Filters
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {products.map((product) => (
            <Card key={product._id} className="overflow-hidden h-full hover:shadow-md transition-all">
              <div className="h-40 bg-gray-100 relative">
                <img 
                  src={product.image || "/placeholder.svg"} 
                  alt={product.title} 
                  className="w-full h-full object-cover"
                />
                {product.hot && (
                  <div className="absolute top-2 right-2">
                    <Badge className="bg-orange-500">Hot Deal</Badge>
                  </div>
                )}
                {product.badge && (
                  <div className="absolute top-2 right-2">
                    <Badge className="bg-earnhub-red">{product.badge}</Badge>
                  </div>
                )}
              </div>
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="font-medium text-lg">{product.title}</h3>
                    <Badge variant="outline">{product.category}</Badge>
                  </div>
                  <div className="text-right">
                    {product.discountedPrice && (
                      <div className="text-sm text-gray-400 line-through">KES {product.price.toLocaleString()}</div>
                    )}
                    <div className="text-lg font-bold text-earnhub-red">
                      KES {(product.discountedPrice || product.price).toLocaleString()}
                    </div>
                    {product.savings && (
                      <div className="text-xs text-green-600 font-medium">Save {product.savings}</div>
                    )}
                  </div>
                </div>
                <Button 
                  className="w-full mt-2"
                  onClick={() => handleAddToCart(product)}
                >
                  Add to Cart
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
