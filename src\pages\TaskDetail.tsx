
import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { <PERSON>, CardContent, Card<PERSON>eader, Card<PERSON>itle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ChevronLeft, Clock, Calendar, CheckCircle, AlertCircle, Zap } from "lucide-react";
import anime from "animejs";
import { useToast } from "@/hooks/use-toast";

// This would come from your Sanity CMS in real implementation
interface Task {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: string;
  reward: string;
  estimatedTime: string;
  steps: string[];
  requirements: string[];
  deadline?: string;
  status: "available" | "premium" | "completed";
}

const TaskDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    // Animation for page elements
    anime({
      targets: '.animate-item',
      translateY: [20, 0],
      opacity: [0, 1],
      delay: anime.stagger(100),
      easing: 'easeOutExpo',
      duration: 800
    });
    
    // Simulated data fetch - replace with actual Sanity client fetch
    setTimeout(() => {
      setTask({
        id: id || "1",
        title: "Complete Mobile App Survey",
        description: "We need your feedback on a new mobile app design. The survey includes questions about user interface, functionality, and overall experience. Your insights will help shape the final product.",
        category: "Survey",
        difficulty: "Easy",
        reward: "KES 200",
        estimatedTime: "10 mins",
        steps: [
          "Review the app screenshots provided",
          "Answer 10 multiple-choice questions",
          "Provide written feedback (min. 50 words)",
          "Rate your overall experience",
          "Submit your responses"
        ],
        requirements: [
          "Smartphone user (Android or iOS)",
          "Age between 18-45",
          "Must complete all questions"
        ],
        deadline: "2025-05-30",
        status: "available"
      });
      
      setLoading(false);
    }, 800);
  }, [id]);

  const handleStartTask = () => {
    toast({
      title: "Task Started!",
      description: "You can now begin working on this task.",
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20 flex justify-center items-center">
        <div className="animate-spin h-8 w-8 border-4 border-earnhub-red border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
            <h2 className="text-xl font-bold mb-2">Task Not Found</h2>
            <p className="text-earnhub-darkGray mb-4">The task you're looking for doesn't exist or has been removed.</p>
            <Button asChild>
              <Link to="/tasks">View All Tasks</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20">
      <div className="mb-6 flex items-center animate-item">
        <Link to="/tasks" className="text-earnhub-darkGray flex items-center hover:text-earnhub-red">
          <Zap className="w-4 h-4 mr-1" /> Tasks
        </Link>
        <ChevronLeft className="w-4 h-4 mx-1 text-gray-400" />
        <span className="font-medium text-earnhub-dark">{task.title}</span>
      </div>

      <Card className="mb-6 animate-item">
        <CardHeader>
          <div className="flex flex-wrap justify-between items-start gap-2">
            <div>
              <CardTitle className="text-2xl">{task.title}</CardTitle>
              <CardDescription className="flex items-center mt-1">
                Category: {task.category}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge className="bg-blue-100 text-blue-700 border-blue-200">{task.difficulty}</Badge>
              {task.status === "premium" && <Badge className="bg-yellow-500">Premium</Badge>}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="animate-item">
            <h3 className="font-medium mb-2">Description</h3>
            <p className="text-earnhub-darkGray">{task.description}</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 animate-item">
            <div className="p-4 bg-gray-50 rounded-md flex items-center">
              <Clock className="h-5 w-5 mr-2 text-earnhub-darkGray" />
              <div>
                <h4 className="text-sm font-medium text-gray-500">Estimated Time</h4>
                <p className="font-medium">{task.estimatedTime}</p>
              </div>
            </div>
            <div className="p-4 bg-gray-50 rounded-md flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-earnhub-darkGray" />
              <div>
                <h4 className="text-sm font-medium text-gray-500">Deadline</h4>
                <p className="font-medium">{task.deadline || "No deadline"}</p>
              </div>
            </div>
          </div>
          
          <div className="animate-item">
            <h3 className="font-medium mb-2">Task Steps</h3>
            <ol className="list-decimal pl-5 space-y-1">
              {task.steps.map((step, index) => (
                <li key={index} className="text-earnhub-darkGray">{step}</li>
              ))}
            </ol>
          </div>
          
          <div className="animate-item">
            <h3 className="font-medium mb-2">Requirements</h3>
            <ul className="list-disc pl-5 space-y-1">
              {task.requirements.map((req, index) => (
                <li key={index} className="text-earnhub-darkGray">{req}</li>
              ))}
            </ul>
          </div>
          
          <div className="flex justify-between items-center border-t pt-4 border-gray-100 animate-item">
            <div className="font-bold text-xl text-earnhub-red">
              {task.reward}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-100 animate-item">
          <Button className="w-full sm:w-auto bg-earnhub-red hover:bg-earnhub-red/90" onClick={handleStartTask}>
            Start Task
          </Button>
          <Button variant="outline" className="w-full sm:w-auto" asChild>
            <Link to="/tasks">View All Tasks</Link>
          </Button>
        </CardFooter>
      </Card>
      
      <Card className="animate-item">
        <CardHeader>
          <CardTitle>Similar Tasks</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2].map((item) => (
              <Link key={item} to={`/task/${item+1}`}>
                <Card className="hover:border-earnhub-red transition-colors cursor-pointer">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{item === 1 ? "Website Testing & Feedback" : "Create Social Media Post"}</h3>
                        <p className="text-sm text-earnhub-darkGray">{item === 1 ? "Testing" : "Social"}</p>
                      </div>
                      <Badge>{item === 1 ? "KES 500" : "KES 150"}</Badge>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TaskDetail;
