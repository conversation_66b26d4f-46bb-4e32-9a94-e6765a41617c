
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import Membership from '@/components/Membership';
import { Button } from '@/components/ui/button';
import { ArrowRight, CheckCircle } from 'lucide-react';

const MembershipPage = () => {
  const [currentTier, setCurrentTier] = useState('basic'); // In a real app, fetch from user profile
  
  const membershipBenefits = [
    {
      title: "Higher Earnings Per Referral",
      description: "Earn up to 55% commission on each referral. Gold members earn 37.5% more than Starter members!",
      icon: <CheckCircle className="text-earnhub-red h-6 w-6" />
    },
    {
      title: "Access to Premium Tasks",
      description: "Higher tier memberships unlock higher-paying tasks worth up to KES 500 each.",
      icon: <CheckCircle className="text-earnhub-red h-6 w-6" />
    },
    {
      title: "Reduced Withdrawal Fees",
      description: "Silver members pay lower fees while Gold members withdraw for free.",
      icon: <CheckCircle className="text-earnhub-red h-6 w-6" />
    },
    {
      title: "Competition Advantages",
      description: "Silver and Gold members get priority entries and free access to exclusive competitions.",
      icon: <CheckCircle className="text-earnhub-red h-6 w-6" />
    }
  ];
  
  return (
    <div className="min-h-screen bg-white pt-16">
      <Helmet>
        <title>Membership Plans | EarnHub</title>
      </Helmet>
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-earnhub-red to-earnhub-dark text-white py-12 px-6">
        <div className="max-w-5xl mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-8 md:mb-0 md:mr-8">
              <h1 className="text-3xl md:text-4xl font-bold mb-4">Upgrade Your Earning Potential</h1>
              <p className="text-white/90 text-lg max-w-lg">
                Choose the right membership tier to maximize your earnings. Higher tiers unlock better commission rates and exclusive earning opportunities.
              </p>
              
              <div className="mt-6 flex items-center">
                <Button className="bg-white text-earnhub-red hover:bg-white/90 flex items-center">
                  <span>Upgrade Now</span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="bg-white/10 p-6 rounded-xl backdrop-blur-sm border border-white/20">
              <h3 className="text-xl font-bold mb-2">Your Current Membership</h3>
              <div className="flex items-center mb-4">
                <div className={`h-10 w-10 rounded-full flex items-center justify-center mr-3 ${
                  currentTier === 'basic' ? 'bg-earnhub-dark' : 
                  currentTier === 'bronze' ? 'bg-amber-600' :
                  currentTier === 'silver' ? 'bg-gradient-to-r from-earnhub-red to-earnhub-dark' : 
                  'bg-[#EAB308]'
                }`}>
                  <span className="text-white font-bold text-sm">
                    {currentTier === 'basic' ? 'S' : 
                     currentTier === 'bronze' ? 'B' :
                     currentTier === 'silver' ? 'S' : 'G'}
                  </span>
                </div>
                <div>
                  <p className="font-bold">
                    {currentTier === 'basic' ? 'Starter' : 
                     currentTier === 'bronze' ? 'Bronze' :
                     currentTier === 'silver' ? 'Silver' : 'Gold'} Membership
                  </p>
                  <p className="text-sm text-white/80">
                    {currentTier === 'basic' ? '40% referral commission' : 
                     currentTier === 'bronze' ? '45% referral commission' :
                     currentTier === 'silver' ? '50% referral commission' : '55% referral commission'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Benefits Section */}
      <section className="py-12 px-6 bg-earnhub-lightGray">
        <div className="max-w-5xl mx-auto">
          <h2 className="text-2xl font-bold text-center mb-10">Key Membership Benefits</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {membershipBenefits.map((benefit, index) => (
              <div key={index} className="bg-white p-6 rounded-xl shadow-sm flex items-start">
                <div className="mr-4 mt-1">{benefit.icon}</div>
                <div>
                  <h3 className="font-bold text-lg mb-1">{benefit.title}</h3>
                  <p className="text-earnhub-darkGray">{benefit.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Membership Comparison */}
      <Membership />
      
      {/* FAQ Section */}
      <section className="py-12 px-6 bg-white">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-2xl font-bold text-center mb-10">Frequently Asked Questions</h2>
          
          <div className="space-y-6">
            {[
              {
                q: "How long does my membership last?",
                a: "All EarnHub memberships are one-time payments for lifetime access. Once you upgrade, you keep that membership tier forever."
              },
              {
                q: "Can I upgrade my membership tier later?",
                a: "Yes, you can upgrade from any tier to a higher tier at any time. You'll only pay the difference between your current tier and the new tier."
              },
              {
                q: "How do higher commission rates work?",
                a: "Your commission rate applies to all referrals you make. For example, at the 50% Silver tier rate, you earn KES 1,750 for each Silver membership referral."
              },
              {
                q: "When do I get access to premium tasks?",
                a: "Immediately after upgrading your membership. You'll see new task categories appear in your dashboard within minutes."
              }
            ].map((item, index) => (
              <div key={index} className="border border-earnhub-gray/20 rounded-lg p-6">
                <h3 className="font-bold text-lg mb-2">{item.q}</h3>
                <p className="text-earnhub-darkGray">{item.a}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default MembershipPage;
