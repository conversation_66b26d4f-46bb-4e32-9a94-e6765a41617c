
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Zap, ChevronRight } from "lucide-react";
import anime from "animejs";
import { Link } from "react-router-dom";

// Mock task data - would be replaced with Sanity CMS integration
const tasks = [
  {
    id: "1",
    title: "Complete Mobile App Survey",
    category: "Survey",
    difficulty: "Easy",
    reward: "KES 200",
    estimatedTime: "10 mins",
    status: "available"
  },
  {
    id: "2",
    title: "Website Testing & Feedback",
    category: "Testing",
    difficulty: "Medium",
    reward: "KES 500",
    estimatedTime: "30 mins",
    status: "available"
  },
  {
    id: "3",
    title: "Create Social Media Post",
    category: "Social",
    difficulty: "Easy",
    reward: "KES 150",
    estimatedTime: "5 mins",
    status: "available"
  },
  {
    id: "4",
    title: "Product Review - Headphones",
    category: "Review",
    difficulty: "Medium",
    reward: "KES 350",
    estimatedTime: "20 mins",
    status: "available"
  },
  {
    id: "5",
    title: "Market Research Call",
    category: "Research",
    difficulty: "Hard",
    reward: "KES 1,000",
    estimatedTime: "45 mins",
    status: "premium"
  }
];

const TasksPage = () => {
  const [currentTab, setCurrentTab] = useState("available");
  
  useEffect(() => {
    // Animation for the task cards
    anime({
      targets: '.task-card',
      translateY: [20, 0],
      opacity: [0, 1],
      delay: anime.stagger(100),
      easing: 'easeOutExpo'
    });
    
    // Title animation
    anime({
      targets: '.page-title',
      translateX: [-20, 0],
      opacity: [0, 1],
      easing: 'easeOutExpo',
      duration: 800
    });
  }, [currentTab]);

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20">
      <div className="mb-8">
        <h1 className="text-2xl md:text-3xl font-bold mb-2 page-title flex items-center">
          <Zap className="mr-2 text-earnhub-red" />
          Available Tasks
        </h1>
        <p className="text-earnhub-darkGray">Complete tasks to earn rewards</p>
      </div>
      
      <Tabs defaultValue="available" onValueChange={setCurrentTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="available">Available</TabsTrigger>
          <TabsTrigger value="premium">Premium</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
        </TabsList>
        
        <TabsContent value="available" className="space-y-4">
          {tasks.filter(task => task.status === 'available').map((task) => (
            <Card key={task.id} className="task-card hover:border-earnhub-red transition-all">
              <CardContent className="p-5">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-lg mb-1">{task.title}</h3>
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge>{task.category}</Badge>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-100">
                        {task.difficulty}
                      </Badge>
                    </div>
                    <div className="text-sm text-earnhub-darkGray">
                      Est. Time: <span className="font-medium">{task.estimatedTime}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-earnhub-red mb-2">{task.reward}</div>
                    <Button size="sm" asChild>
                      <Link to={`/task/${task.id}`}>
                        View Task <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
        
        <TabsContent value="premium" className="space-y-4">
          {tasks.filter(task => task.status === 'premium').map((task) => (
            <Card key={task.id} className="task-card hover:border-earnhub-red transition-all">
              <CardContent className="p-5">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-lg mb-1">{task.title}</h3>
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge>{task.category}</Badge>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-100">
                        {task.difficulty}
                      </Badge>
                      <Badge className="bg-yellow-500">Premium</Badge>
                    </div>
                    <div className="text-sm text-earnhub-darkGray">
                      Est. Time: <span className="font-medium">{task.estimatedTime}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-earnhub-red mb-2">{task.reward}</div>
                    <Button size="sm" asChild>
                      <Link to={`/task/${task.id}`}>
                        View Task <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          <Card>
            <CardContent className="p-5 text-center">
              <p className="text-earnhub-darkGray mb-4">
                Upgrade your membership to access more premium tasks with higher rewards!
              </p>
              <Button>Upgrade Now</Button>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="completed" className="space-y-4">
          <Card>
            <CardContent className="p-5 text-center">
              <p className="text-earnhub-darkGray">You haven't completed any tasks yet.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TasksPage;
