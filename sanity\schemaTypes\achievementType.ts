// sanity/schemaTypes/achievementType.ts
import {Rule} from 'sanity'

export default {
  name: 'achievementType',
  title: 'Achievement Type',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'category',
      title: 'Category',
      type: 'string',
      options: {
        list: ['tasks', 'referrals', 'competitions', 'profile', 'earnings'],
      },
    },
    {
      name: 'difficulty',
      title: 'Difficulty',
      type: 'string',
      options: {
        list: ['easy', 'medium', 'hard', 'expert'],
      },
    },
    {
      name: 'points',
      title: 'Points',
      type: 'number',
      validation: (Rule: Rule) => Rule.required().min(1),
    },
    {
      name: 'reward',
      title: 'Reward',
      type: 'string',
      description: 'e.g., KES 50 bonus, 10 extra points',
    },
    {
      name: 'icon',
      title: 'Icon',
      type: 'string',
      description: 'Name of an icon, e.g., from Lucide icons',
    },
    {
      name: 'criteriaType',
      title: 'Criteria Type',
      type: 'string',
      options: {
        list: ['taskCompletion', 'referralCount', 'profileCompletion', 'earningsAmount', 'competitionParticipation'],
      },
      description: 'The type of criteria to meet',
    },
    {
      name: 'criteriaValue',
      title: 'Criteria Value',
      type: 'number',
      description: 'The target value needed to complete the achievement, e.g., 5 for 5 tasks completed',
    },
    {
      name: 'isEnabled',
      title: 'Is Enabled',
      type: 'boolean',
      initialValue: true,
    },
  ],
}
