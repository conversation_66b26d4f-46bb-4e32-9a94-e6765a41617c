// sanity/schemaTypes/raffle.ts
import {Rule} from 'sanity'

export default {
  name: 'raffle',
  title: 'Raffle',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
    },
    {
      name: 'type',
      title: 'Type',
      type: 'string',
      options: {
        list: ['daily', 'weekly', 'monthly', 'special'],
      },
    },
    {
      name: 'prizeDetails',
      title: 'Prize Details',
      type: 'text',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'prizeAmount',
      title: 'Prize Cash Value (KES if applicable)',
      type: 'number',
    },
    {
      name: 'currency',
      title: 'Currency',
      type: 'string',
      initialValue: 'KES',
    },
    {
      name: 'ticketPrice',
      title: 'Ticket Price (KES)',
      type: 'number',
      initialValue: 0,
    },
    {
      name: 'maxTicketsPerUser',
      title: 'Max Tickets Per User',
      type: 'number',
      initialValue: 1,
    },
    {
      name: 'totalTicketsAvailable',
      title: 'Total Tickets Available (0 for unlimited)',
      type: 'number',
    },
    {
      name: 'startTime',
      title: 'Start Time',
      type: 'datetime',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'endTime',
      title: 'End Time',
      type: 'datetime',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: ['upcoming', 'active', 'drawing', 'completed', 'cancelled'],
      },
      initialValue: 'upcoming',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'winnerSelectionTime',
      title: 'Winner Selection Time',
      type: 'datetime',
    },
    {
      name: 'minimumEntryRequirements',
      title: 'Minimum Entry Requirements',
      type: 'text',
      description: 'Any requirements for users to enter this raffle',
    },
    {
      name: 'image',
      title: 'Image',
      type: 'image',
      options: {
        hotspot: true,
      },
    },
  ],
}
