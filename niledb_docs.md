Nile DB Integration for Earnhub
This guide explains how to integrate Nile DB's authentication and tenant management features into Earnhub, a multi-tenant application built with Vite, TypeScript, React, shadcn-ui, and Tailwind CSS. Nile DB provides a seamless way to handle user authentication, session management, and tenant isolation, making it ideal for Earnhub's scalable architecture.
Overview
Nile DB offers a React SDK (@niledatabase/react) and a server SDK (@niledatabase/server) to manage authentication, user sessions, and tenant-specific data. Key features include:

Authentication: Sign-in, sign-up, and password reset with providers like email, Google, and credentials.
Tenant Management: Create and manage tenants, with hooks for tenant-aware data access.
Session Handling: Built-in components and hooks for authenticated and unauthenticated states.
TypeScript Support: Fully typed APIs for a robust developer experience.

This documentation focuses on integrating Nile DB into Earnhub, with examples tailored to your tech stack.
Installation
Install the Nile React SDK to get started:
npm install @niledatabase/react @niledatabase/server

Ensure your project is configured with Vite, TypeScript, and shadcn-ui/Tailwind CSS for styling. The Nile SDK works seamlessly with these tools.
Setting Up Nile Server
Initialize the Nile server instance in a dedicated file to handle API routes and server-side operations.
src/lib/nile.ts:
import { Nile } from '@niledatabase/server';

export const nile = await Nile({
api: {
routePrefix: '/api/nile', // Custom prefix for Earnhub's API routes
},
});

export const { handlers } = nile.api;

Create a catch-all route to handle Nile API requests:
src/app/api/nile/[...nile]/route.ts:
import { handlers } from '@/lib/nile';

export const { POST, GET, DELETE, PUT } = handlers;

This setup ensures Nile's API routes are accessible under /api/nile.
Authentication Provider
Wrap your application with Nile's SessionProvider and use SignedIn/SignedOut components to conditionally render content based on authentication status.
src/app/layout.tsx:
import { SessionProvider, SignedIn, SignedOut } from '@niledatabase/react';
import '@niledatabase/react/styles.css';
import { Button } from '@/components/ui/button'; // shadcn-ui
import './globals.css';

export default function RootLayout({ children }: { children: React.ReactNode }) {
return (

<html lang="en">
<body className="min-h-screen bg-gray-50">
<SessionProvider basePath="/api/nile">
<SignedIn>
<div className="container mx-auto p-4">
<nav className="flex justify-between items-center mb-4">
<h1 className="text-2xl font-bold">Earnhub</h1>
<Button variant="outline" asChild>
<a href="/api/nile/auth/signout">Sign Out</a>
</Button>
</nav>
{children}
</div>
</SignedIn>
<SignedOut>
<div className="flex flex-col items-center justify-center min-h-screen">
<h1 className="text-3xl font-bold mb-4">Welcome to Earnhub</h1>
<Button asChild>
<a href="/login">Sign In</a>
</Button>
</div>
</SignedOut>
</SessionProvider>
</body>
</html>
);
}

This layout uses shadcn-ui's Button component and Tailwind CSS for styling, providing a clean UI for authenticated and unauthenticated users.
Sign-In and Sign-Up Forms
Create reusable forms for sign-in and sign-up using Nile's hooks and shadcn-ui components.
src/app/login/page.tsx:
'use client';
import { useSignIn } from '@niledatabase/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState } from 'react';

export default function SignInPage() {
const [email, setEmail] = useState('');
const [password, setPassword] = useState('');
const signIn = useSignIn({
onSuccess: () => alert('Login successful!'),
onError: (err) => alert(`Login failed: ${err.message}`),
});

const handleSubmit = (e: React.FormEvent) => {
e.preventDefault();
signIn({ email, password });
};

return (

<div className="flex items-center justify-center min-h-screen">
<form onSubmit={handleSubmit} className="w-full max-w-md space-y-4 p-6 bg-white rounded-lg shadow">
<h2 className="text-2xl font-bold text-center">Sign In to Earnhub</h2>
<div>
<Label htmlFor="email">Email</Label>
<Input
id="email"
type="email"
value={email}
onChange={(e) => setEmail(e.target.value)}
required
className="w-full"
/>
</div>
<div>
<Label htmlFor="password">Password</Label>
<Input
id="password"
type="password"
value={password}
onChange={(e) => setPassword(e.target.value)}
required
className="w-full"
/>
</div>
<Button type="submit" className="w-full">
Sign In
</Button>
</form>
</div>
);
}

src/app/signup/page.tsx:
'use client';
import { useSignUp } from '@niledatabase/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState } from 'react';

export default function SignUpPage() {
const [email, setEmail] = useState('');
const [password, setPassword] = useState('');
const signUp = useSignUp({
onSuccess: () => alert('Sign-up successful!'),
onError: (err) => alert(`Sign-up failed: ${err.message}`),
createTenant: true, // Create a tenant with the user's email
});

const handleSubmit = (e: React.FormEvent) => {
e.preventDefault();
signUp({ email, password });
};

return (

<div className="flex items-center justify-center min-h-screen">
<form onSubmit={handleSubmit} className="w-full max-w-md space-y-4 p-6 bg-white rounded-lg shadow">
<h2 className="text-2xl font-bold text-center">Sign Up for Earnhub</h2>
<div>
<Label htmlFor="email">Email</Label>
<Input
id="email"
type="email"
value={email}
onChange={(e) => setEmail(e.target.value)}
required
className="w-full"
/>
</div>
<div>
<Label htmlFor="password">Password</Label>
<Input
id="password"
type="password"
value={password}
onChange={(e) => setPassword(e.target.value)}
required
className="w-full"
/>
</div>
<Button type="submit" className="w-full">
Sign Up
</Button>
</form>
</div>
);
}

These forms leverage shadcn-ui components (Button, Input, Label) and Tailwind CSS for a polished, responsive UI. The useSignIn and useSignUp hooks handle authentication logic.
Tenant Management
Manage tenants using the useTenantId and useTenants hooks to support Earnhub's multi-tenant architecture.
src/components/TenantSelector.tsx:
'use client';
import { useTenantId, useTenants } from '@niledatabase/react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export default function TenantSelector() {
const [tenantId, setTenantId] = useTenantId();
const { data: tenants, isLoading } = useTenants();

if (isLoading) return <p className="text-gray-500">Loading tenants...</p>;

return (

<div className="space-y-2">
<p className="text-sm text-gray-600">Current Tenant: {tenantId ?? 'None'}</p>
<Select onValueChange={setTenantId} value={tenantId ?? ''}>
<SelectTrigger className="w-[200px]">
<SelectValue placeholder="Select a tenant" />
</SelectTrigger>
<SelectContent>
{tenants?.map((tenant) => (
<SelectItem key={tenant.id} value={tenant.id}>
{tenant.name}
</SelectItem>
))}
</SelectContent>
</Select>
</div>
);
}

This component uses shadcn-ui's Select component to allow users to switch between tenants, styled with Tailwind CSS.
Key Hooks
useSession
Retrieve the current session within a SignedIn or SignedOut provider:
import { useSession, UserInfo } from '@niledatabase/react';

function Profile() {
const session = useSession();

if (session.status !== 'authenticated') {
return <p>Loading...</p>;
}

return <UserInfo />;
}

useMe
Fetch the current authenticated user's details:
'use client';
import { useMe } from '@niledatabase/react';

function Profile() {
const user = useMe();

if (!user) return <p>Loading...</p>;

return <div className="text-lg">Welcome, {user.name ?? user.email}!</div>;
}

useResetPassword
Handle password reset requests:
'use client';
import { useResetPassword } from '@niledatabase/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function ResetPasswordForm() {
const resetPassword = useResetPassword({
onSuccess: () => alert('Password reset email sent!'),
onError: (err) => alert(`Error: ${err.message}`),
});

const handleSubmit = (e: React.FormEvent<HTMLFormElement>) {
e.preventDefault();
const formData = new FormData(e.target);
resetPassword({ email: formData.get('email') as string });
};

return (

<form onSubmit={handleSubmit} className="w-full max-w-md space-y-4 p-6 bg-white rounded-lg shadow">
<h2 className="text-2xl font-bold text-center">Reset Password</h2>
<div>
<Label htmlFor="email">Email</Label>
<Input id="email" type="email" name="email" required className="w-full" />
</div>
<Button type="submit" className="w-full">
Send Reset Email
</Button>
</form>
);
}

Customizing API Routes
Override Nile's default API routes to match Earnhub's structure:
src/lib/nile.ts:
import { Nile } from '@niledatabase/server';

export const nile = await Nile({
api: {
routePrefix: '/api/nile',
routes: {
SIGNIN: '/login',
SIGNOUT: '/logout',
ME: '/profile',
},
},
});

export const { handlers } = nile.api;

Update route handlers:
src/app/api/login/route.ts:
import { handlers } from '@/lib/nile';
export const { POST } = handlers;

src/app/api/logout/route.ts:
import { handlers } from '@/lib/nile';
export const { POST } = handlers;

Ensure components use the updated routes:
<SignOutButton fetchUrl="/logout" />

Database Queries
Use the nile.db object for tenant-aware database queries:
import { nile } from '@/lib/nile';

async function fetchData() {
nile.tenantId = '019612d7-56e7-7e87-8f30-ad6b05d85645';
const result = await nile.db.query('SELECT \* FROM tenant_table');
console.log(result);
}

For transactions:
const client = await nile.db.client();
try {
await client.query('BEGIN');
await client.query('INSERT INTO tenant_table (id, name, tenant_id) VALUES (1, $1, $2)', ['John Doe', nile.tenantId]);
await client.query('COMMIT');
} catch (error) {
await client.query('ROLLBACK');
throw error;
} finally {
await client.release();
}

Email Verification
To enable email verification:

Configure an SMTP server in Nile Auth UI.
Use the EmailSignInButton component to trigger verification emails:

import { EmailSignInButton } from '@niledatabase/react';

function VerifyButton({ email }: { email: string }) {
return <EmailSignInButton email={email} />;
}

Best Practices

Type Safety: Always use TypeScript types provided by @niledatabase/react and @niledatabase/server.
Styling: Leverage shadcn-ui and Tailwind CSS for consistent, responsive UI.
Error Handling: Implement onError callbacks in hooks to handle failures gracefully.
Tenant Isolation: Set nile.tenantId before tenant-aware operations to ensure data isolation.
Security: Use CSRF tokens and secure cookies for authentication requests.

Example: Dashboard Component
A sample dashboard integrating Nile's features with Earnhub's styling:
src/app/dashboard/page.tsx:
'use client';
import { SignedIn, UserInfo, useMe, useTenantId } from '@niledatabase/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import TenantSelector from '@/components/TenantSelector';

export default function Dashboard() {
const user = useMe();
const [tenantId] = useTenantId();

return (
<SignedIn>

<div className="container mx-auto p-4 space-y-6">
<Card>
<CardHeader>
<CardTitle>Welcome, {user?.name ?? user?.email}</CardTitle>
</CardHeader>
<CardContent className="space-y-4">
<UserInfo />
<TenantSelector />
<p className="text-gray-600">Current Tenant ID: {tenantId ?? 'None'}</p>
<Button asChild>
<a href="/api/nile/auth/signout">Sign Out</a>
</Button>
</CardContent>
</Card>
</div>
</SignedIn>
);
}

This dashboard uses shadcn-ui's Card component and integrates Nile's UserInfo and TenantSelector for a cohesive user experience.

google.ai summary:
Okay, this is a great starting point! Let's "neatify" this documentation, focusing on clarity, structure, and readability, keeping your Earnhub app (Vite, TypeScript, React, shadcn-ui, Tailwind CSS) in mind.

Here's a revised version:

---

# Nile DB Integration for Earnhub (React & Vite)

This guide details how to integrate Nile DB, particularly its authentication and data management features, into your Earnhub application, built with Vite, TypeScript, React, shadcn-ui, and Tailwind CSS.

## Table of Contents

1.  **React SDK (`@niledatabase/react`)**
    - Installation
    - Setting up the Auth Provider
    - Auth Components (`<SignedIn />`, `<SignedOut />`)
    - Core Functions (`signIn`, `signOut`)
    - React Hooks
      - `useSession`
      - `useTenantId`
      - `useTenants`
      - `useEmailSignIn`
      - `useMe`
      - `useResetPassword`
      - `useSignUp`
      - `useSignIn`
2.  **Server SDK (`@niledatabase/server`)**
    - Server Setup & Route Customization
    - Auth Module (Server-Side)
    - Users Module (Server-Side)
    - Tenants Module (Server-Side)
    - DB Module (Server-Side)
3.  **Feature Guides**
    - Email Verification Flow

---

## 1. React SDK (`@niledatabase/react`)

The `@niledatabase/react` package provides components and hooks for seamless integration with the Nile API in your React application. It handles session management, cookies, data fetching, and includes pre-built UI components for common user tasks. It's designed to work in tandem with `@niledatabase/server` for backend API calls.

### Installation

```bash
npm install @niledatabase/react
# or
yarn add @niledatabase/react
```

### Setting up the Auth Provider

`@niledatabase/react` includes `<SignedIn />` and `<SignedOut />` components, which internally wrap a central `<SessionProvider />`. By default, this setup automatically fetches and manages the user's session.

- `<SignedIn />`: Renders its children only if a user is authenticated.
- `<SignedOut />`: Renders its children only if no user is authenticated.

**Basic Usage:**

```tsx
// Example: App.tsx or a layout component
import { SignedIn, SignedOut, SessionProvider } from "@niledatabase/react";
import "@niledatabase/react/styles.css"; // Optional default styles

function MyApp() {
  return (
    <SessionProvider>
      {" "}
      {/* Recommended to wrap your app or relevant auth section */}
      <SignedIn>
        <p>Welcome back, you're signed in!</p>
        {/* Place your authenticated app components here */}
      </SignedIn>
      <SignedOut>
        <p>Please sign in to continue.</p>
        {/* Place your sign-in/sign-up forms here */}
      </SignedOut>
    </SessionProvider>
  );
}

export default MyApp;
```

**Server-Side Session Fetching (SSR/Pre-rendering):**

For scenarios like Next.js or server-side rendering with Vite, you can fetch the session on the server and pass it to the `<SignedIn />` component.

```tsx
// Example: pages/protected-route.tsx (if using a framework like Next.js)
// Or a server-side handler that pre-renders HTML in a Vite setup
import { SignedIn } from "@niledatabase/react";
import { nile } from "@/lib/nile"; // Your Nile server instance
// Assuming 'req' is available (e.g., from Next.js getServerSideProps or an Express handler)

export default async function ProtectedPage({
  sessionFromServer,
}: {
  sessionFromServer: any;
}) {
  // This part would be in getServerSideProps or similar server logic
  // const nileInstance = nile; // Your initialized Nile server SDK
  // nileInstance.api.headers = req.headers; // Pass incoming request headers
  // const session = await nileInstance.api.auth.getSession();
  //
  // if (session.status === 'unauthenticated') {
  //   return { redirect: { destination: '/login', permanent: false } };
  // }
  // return { props: { sessionFromServer: session } };

  // In your React component:
  if (!sessionFromServer || sessionFromServer.status === "unauthenticated") {
    return <div>No soup for you! (Not authenticated)</div>;
  }

  return (
    <SignedIn session={sessionFromServer}>
      Jerry, hello! (Authenticated)
    </SignedIn>
  );
}
```

**Note:** For Vite with a custom server (e.g., Express), you'll adapt the server-side logic accordingly. The `nile.api.headers = req.headers;` part is crucial if your server handles the initial request.

### Auth Components

#### `<SignedIn />`

Renders children only if the user is logged in.

```tsx
import { SignedIn } from "@niledatabase/react";

function UserDashboard() {
  return (
    <SignedIn>
      <p>Welcome to your Dashboard!</p>
    </SignedIn>
  );
}
```

#### `<SignedOut />`

Renders children only if the user is not logged in.

```tsx
import { SignedOut } from "@niledatabase/react";

function LoginPage() {
  return (
    <SignedOut>
      <p>Please log in to access Earnhub.</p>
      {/* Your login form component */}
    </SignedOut>
  );
}
```

### Core Functions

These functions are typically used to trigger authentication actions.

#### `signIn`

Initiates a sign-in process. It makes a POST request to the sign-in endpoint.

- **Parameters:**

  - `provider`: (string) The authentication provider (e.g., 'google', 'credentials', 'email').
  - `options`: (object, optional)
    - `callbackUrl`: (string) URL to redirect to after successful sign-in.
    - `redirect`: (boolean, default: `true`) For 'credentials' and 'email' providers, set to `false` to prevent redirection and handle the response manually.
    - Other provider-specific parameters (e.g., `email`, `password` for 'credentials').

- **Usage:**

```typescript
import { signIn } from "@niledatabase/react"; // Or from a hook like useEmailSignIn

// Sign in with Google, redirect to dashboard
signIn("google", { callbackUrl: "/dashboard" });

// Sign in with email and password
signIn("credentials", {
  email: "<EMAIL>",
  password: "password123",
  callbackUrl: "/dashboard",
});

// Sign in with email (magic link), opt-out of redirection
signIn("email", { email: "<EMAIL>", redirect: false }).then(
  (response) => {
    if (response.ok) {
      console.log("Magic link sent!");
    } else {
      console.error("Failed to send magic link:", response.error);
    }
  }
);
```

#### `signOut`

Initiates a sign-out process. It makes a POST request to the sign-out endpoint.

- **Parameters:**

  - `options`: (object, optional)
    - `callbackUrl`: (string) URL to redirect to after successful sign-out.
    - `redirect`: (boolean, default: `true`) Set to `false` to stay on the current page (session will still be deleted).

- **Usage:**

```typescript
import { signOut } from "@niledatabase/react"; // Or from the SignOutButton component

// Sign out and redirect to the sign-in page
signOut({ callbackUrl: "/sign-in" });

// Sign out but stay on the current page
signOut({ redirect: false });
```

### React Hooks

Hooks provide reactive access to authentication state and actions within your React components. They must be used within a `<SessionProvider />` (or `<SignedIn />`/`<SignedOut />`).

#### `useSession`

Retrieves the current authentication session state.

- **Returns:** An object containing:

  - `data`: The session object (or `null` if not authenticated). Contains user info if authenticated.
  - `status`: ('loading' | 'authenticated' | 'unauthenticated') The current session status.
  - `update`: A function to manually trigger a session update.

- **Usage:**

```tsx
import { useSession, UserInfo } from "@niledatabase/react"; // Assuming UserInfo is a component you might build or from Nile

export default function UserProfile() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return <p>Loading session...</p>;
  }

  if (status === "unauthenticated") {
    return <p>You are not signed in.</p>;
  }

  // 'session.user' will contain user details
  return (
    <div>
      <p>Welcome, {session?.user?.email}!</p>
      {/* Or use a pre-built component if available */}
      {/* <UserInfo /> */}
    </div>
  );
}
```

#### `useTenantId`

Manages the current tenant ID, persisting it in cookies (`nile.tenant_id`) and refetching tenant-specific data when it changes. This cookie is used by the server-side SDK.

- **Returns:** `[tenantId, setTenantId, isLoading, error]`

  - `tenantId`: (string | undefined) The current tenant ID.
  - `setTenantId`: `(tenant: string) => void` Function to update the tenant ID.
  - `isLoading`: (boolean) True if tenant data is being fetched.
  - `error`: (Error | null) Any error encountered.

- **Props:**
  | Name | Type | Default | Description |
  | :------- | :----------------------------------- | :-------- | :-------------------------------- |
  | `params` | `HookProps & { tenant?: Tenant }` | undefined | Initial tenant data and options. |
  | `client` | `QueryClient` | undefined | React Query client instance. |

  `HookProps`:

  ```typescript
  export type HookProps = {
    tenants?: Tenant[]; // A list of tenants (e.g., for pre-filling)
    onError?: (e: Error) => void; // Failure callback
    baseUrl?: string; // Base URL for API calls (e.g., '/api')
  };
  ```

- **Behavior:**

  1.  Initializes `tenantId` from `params.tenant.id` if provided.
  2.  If no tenant, tries to read from the `nile.tenant_id` cookie.
  3.  If no cookie, may trigger a refetch of tenants (depending on implementation details).
  4.  Calling `setTenantId(newTenantId)` updates both the hook's state and the `nile.tenant_id` cookie.

- **Usage:**

```tsx
import { useTenantId } from "@niledatabase/react";

export default function TenantSelector() {
  const [tenantId, setTenantId, isLoading, error] = useTenantId();

  // Example tenants list (could be fetched via useTenants)
  const availableTenants = [
    { id: "tenant-A", name: "Tenant Alpha" },
    { id: "tenant-B", name: "Tenant Bravo" },
  ];

  if (isLoading) return <p>Loading tenant info...</p>;
  if (error) return <p>Error managing tenant: {error.message}</p>;

  return (
    <div>
      <p>Current Tenant: {tenantId ?? "None selected"}</p>
      <select
        value={tenantId || ""}
        onChange={(e) => setTenantId(e.target.value)}
        className="p-2 border rounded" /* Example Tailwind styling */
      >
        <option value="" disabled>
          Select a Tenant
        </option>
        {availableTenants.map((tenant) => (
          <option key={tenant.id} value={tenant.id}>
            {tenant.name}
          </option>
        ))}
      </select>
    </div>
  );
}
```

#### `useTenants`

Fetches a list of tenants associated with the current user. It uses React Query internally.

- **Returns:** Result of `useQuery` from React Query:
  | Property | Type | Description |
  | :---------- | :--------------------- | :---------------------------------- |
  | `data` | `Tenant[] \| undefined` | List of tenants. |
  | `isLoading` | `boolean` | `true` while fetching data. |
  | `error` | `Error \| null` | Fetch error, if any. |
  | `refetch` | `() => void` | Function to manually refetch tenants. |

- **Props:**
  | Name | Type | Default | Description |
  | :------- | :--------------------------------------- | :-------- | :-------------------------------- |
  | `params` | `HookProps & { disableQuery?: boolean }` | undefined | Hook configuration options. |
  | `client` | `QueryClient` | undefined | Optional React Query client. |

  `HookProps`:

  ```typescript
  export type HookProps = {
    tenants?: Tenant[]; // Preloaded tenants (e.g., from SSR)
    onError?: (e: Error) => void;
    baseUrl?: string; // Base URL for API calls
  };
  ```

- **Behavior:**

  - If `params.disableQuery` is `true`, the query is disabled.
  - If `params.tenants` is provided and not empty (e.g., hydration), the query is also disabled initially.
  - Otherwise, it fetches tenants from `${baseUrl}/api/tenants`.
  - The request typically runs once on mount unless manually refetched or dependencies change.

- **Usage:**

```tsx
import { useTenants } from "@niledatabase/react";

export default function TenantList() {
  const {
    data: tenants,
    isLoading,
    error,
  } = useTenants({ params: { baseUrl: "/api" } });

  if (isLoading) return <p className="text-gray-500">Loading tenants...</p>;
  if (error)
    return (
      <p className="text-red-500">Error loading tenants: {error.message}</p>
    );

  return (
    <ul className="list-disc pl-5">
      {tenants?.map((tenant) => (
        <li key={tenant.id} className="mb-1">
          {tenant.name}
        </li>
      ))}
    </ul>
  );
}
```

#### `useEmailSignIn`

Provides a mutation function for signing in a user via email (typically magic link).

- **Returns:** `signInFunction(data: { email: string })`

  - `signInFunction`: An asynchronous function to call with the user's email.

- **Props:**
  | Name | Type | Default | Description |
  | :------------- | :----------------------------- | :-------- | :--------------------------------------------- |
  | `onSuccess` | `(data: Response) => void` | undefined | Callback after successful sign-in initiation. |
  | `onError` | `(error: Error) => void` | undefined | Callback if sign-in initiation fails. |
  | `beforeMutate` | `(data: any) => any` | undefined | Function to modify data before mutation. |
  | `callbackUrl` | `string` | undefined | URL to redirect to after verification from email. |
  | `redirect` | `boolean` | `false` | Whether the client should redirect after call. |

- **Behavior:**

  - Calls `signIn('email', data)` internally.
  - Allows data modification via `beforeMutate`.
  - Handles success/error with callbacks.

- **Usage:**

```tsx
import { useEmailSignIn } from "@niledatabase/react"; // Corrected import
import { useState } from "react";

export default function EmailLoginForm() {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");

  const emailSignIn = useEmailSignIn({
    onSuccess: () => setMessage("Check your email for a sign-in link!"),
    onError: (error) => setMessage(`Login failed: ${error.message}`),
    // callbackUrl: '/dashboard' // Where the user lands after clicking the email link
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setMessage("Sending link...");
    emailSignIn({ email });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label
          htmlFor="email"
          className="block text-sm font-medium text-gray-700"
        >
          Email
        </label>
        <input
          type="email"
          id="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm" /* Example shadcn/tailwind style */
        />
      </div>
      <button
        type="submit"
        className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700" /* Example shadcn/tailwind style */
      >
        Sign In with Email
      </button>
      {message && <p className="text-sm text-gray-600">{message}</p>}
    </form>
  );
}
```

#### `useMe`

Fetches and returns data for the currently authenticated user.

- **Returns:** `user: User | undefined | null`

  - The user object if authenticated and fetched, `null` if not authenticated, `undefined` while loading.

- **Props:**
  | Name | Type | Default | Description |
  | :--------- | :--------------------------- | :-------- | :---------------------------------------------- |
  | `fetchUrl` | `string` | `/api/me` | API endpoint to fetch user data. |
  | `user` | `User \| undefined \| null` | undefined | Initial user data (avoids fetch if provided). |

  ```typescript
  // Example User type (align with your actual User definition)
  interface User {
    id: string;
    name?: string | null;
    email: string;
    // ...other properties
  }
  ```

- **Behavior:**

  - If `user` prop is provided, it's used directly.
  - Otherwise, fetches from `fetchUrl` on component mount (once).

- **Usage:**

  ```tsx
  "use client"; // If using Next.js App Router or similar client-side rendering context
  import { useMe } from "@niledatabase/react";

  export default function UserProfileDisplay() {
    const user = useMe({ fetchUrl: "/api/auth/me" }); // Adjust fetchUrl if needed

    if (user === undefined) return <p>Loading user profile...</p>;
    if (user === null) return <p>Not signed in.</p>;

    return <div>Welcome, {user.name || user.email}!</div>;
  }
  ```

#### `useResetPassword`

Provides functionality for handling password reset requests.

- **Returns:** `resetPasswordFunction(data: { email: string } | { token: string, newPassword: string })`

  - `resetPasswordFunction`: An async function to initiate password reset (with email) or set a new password (with token).

- **Props (`params`):**

  ```typescript
  export type ResetPasswordParams = {
    onSuccess?: (data: Response) => void;
    onError?: (error: Error) => void;
    beforeMutate?: (data: MutateFnParams) => MutateFnParams; // MutateFnParams: { email: string } | { token: string, newPassword: string }
    callbackUrl?: string; // URL for email link to redirect to (e.g., /set-new-password)
    baseUrl?: string; // Base API URL
    fetchUrl?: string; // Specific endpoint, defaults to `${baseUrl}/api/auth/reset-password`
  };
  ```

- **Behavior:**

  - Sends POST to API for reset request (email), PUT for password update (token).
  - Runs CSRF request on hook initialization.

- **Usage:**

```tsx
import { useResetPassword } from "@niledatabase/react";
import { useState } from "react";

export default function ResetPasswordForm() {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");

  const resetPassword = useResetPassword({
    onSuccess: () => setMessage("Password reset email sent. Check your inbox."),
    onError: (err) => setMessage(`Error: ${err.message}`),
    baseUrl: "/api", // Your API base
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage("Processing...");
    await resetPassword({ email });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <input
        type="email"
        name="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
        placeholder="Enter your email"
        className="w-full p-2 border rounded"
      />
      <button
        type="submit"
        className="w-full p-2 bg-green-500 text-white rounded"
      >
        Send Reset Link
      </button>
      {message && <p>{message}</p>}
    </form>
  );
}
```

#### `useSignUp`

Handles user sign-up requests, with optional tenant creation.

- **Returns:** `signUpFunction(data: SignUpInfo)`

  - `signUpFunction`: An async function to call with user's sign-up details.

- **Props (`params`):**

  ```typescript
  export type SignUpProps = {
    onSuccess?: (data: Response, variables: unknown) => void;
    onError?: (error: Error) => void;
    beforeMutate?: (data: SignUpInfo) => SignUpInfo;
    callbackUrl?: string; // Redirect after successful sign-up & login
    baseUrl?: string;
    createTenant?: boolean | string; // true: tenant named user's email, string: custom tenant name
  };

  export type SignUpInfo = {
    email: string;
    password: string;
    tenantId?: string; // To sign up into an existing tenant
    newTenantName?: string; // Used internally if createTenant is true/string
    fetchUrl?: string; // Custom endpoint, defaults to /api/signup
  };
  ```

- **Props (`client`):** Optional `QueryClient` instance.

- **Behavior:**

  - POSTs to `/api/signup` (or custom `fetchUrl`).
  - Handles tenant creation based on `createTenant` prop.
  - Updates session and redirects/reloads on success.
  - Prefetches auth providers and CSRF tokens on mount.

- **Usage:**

```tsx
import { useSignUp } from "@niledatabase/react";
import { useState } from "react";

export default function SignUpForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");

  const signUp = useSignUp({
    onSuccess: () => {
      setMessage("Sign-up successful! Redirecting...");
      // Redirection is handled by the hook if callbackUrl is set, or page reloads
    },
    onError: (err) => setMessage(`Sign-up failed: ${err.message}`),
    createTenant: true, // Create a tenant named after the user's email
    baseUrl: "/api",
    // callbackUrl: '/dashboard'
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage("Signing up...");
    await signUp({ email, password });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
        placeholder="Email"
        className="w-full p-2 border rounded"
      />
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        required
        placeholder="Password"
        className="w-full p-2 border rounded"
      />
      <button
        type="submit"
        className="w-full p-2 bg-blue-500 text-white rounded"
      >
        Sign Up
      </button>
      {message && <p>{message}</p>}
    </form>
  );
}
```

#### `useSignIn`

Handles user authentication via credentials (email/password).

- **Returns:** `signInFunction(data: LoginInfo)`

  - `signInFunction`: An async function for email/password login.

- **Props (`params`):**

  ```typescript
  export type SignInProps = {
    onSuccess?: () => void;
    onError?: (error: Error) => void;
    beforeMutate?: (data: LoginInfo) => LoginInfo;
    callbackUrl?: string; // Redirect after successful login
  };

  export type LoginInfo = {
    email: string;
    password: string;
  };
  ```

- **Behavior:**

  - Uses Nile/NextAuth's `signIn('credentials', data)`.
  - Supports pre-processing data with `beforeMutate`.

- **Usage:**

```tsx
import { useSignIn } from "@niledatabase/react";
import { useState } from "react";

export default function SignInForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [message, setMessage] = useState("");

  const signIn = useSignIn({
    onSuccess: () => {
      setMessage("Login successful! Redirecting...");
      // Redirection handled by hook/NextAuth if callbackUrl is set, or page reloads
    },
    onError: (err) => setMessage(`Login failed: ${err.message}`),
    // callbackUrl: '/dashboard'
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage("Signing in...");
    await signIn({ email, password });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
        placeholder="Email"
        className="w-full p-2 border rounded"
      />
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        required
        placeholder="Password"
        className="w-full p-2 border rounded"
      />
      <button
        type="submit"
        className="w-full p-2 bg-blue-500 text-white rounded"
      >
        Sign In
      </button>
      {message && <p>{message}</p>}
    </form>
  );
}
```

---

## 2. Server SDK (`@niledatabase/server`)

The `@niledatabase/server` SDK is used on your backend (e.g., in API route handlers within your Vite project if you have a custom server, or in a separate Node.js backend). It provides methods for direct interaction with Nile's Auth, Users, Tenants, and DB services.

### Server Setup & Route Customization

Nile API allows overriding default API routes to match your application's structure. This is configured in `ServerConfig`.

**Default Routes:**
Nile provides default routes prefixed by `ServerConfig.api.routePrefix` (default: `/api`).
Example: `/api/auth/signin`, `/api/me`, `/api/tenants`.

```typescript
// List of default route keys (used for overrides)
export const appRoutes = {
  SIGNIN: "/api/auth/signin",
  PROVIDERS: "/api/auth/providers",
  SESSION: "/api/auth/session",
  CSRF: "/api/auth/csrf",
  CALLBACK: "/api/auth/callback",
  SIGNOUT: "/api/auth/signout",
  ERROR: "/api/auth/error",
  VERIFY_REQUEST: "/api/auth/verify-request",
  PASSWORD_RESET: "/api/auth/reset-password",
  ME: "/api/me",
  USERS: "/api/users",
  TENANTS: "/api/tenants",
  TENANT: "/api/tenants/{tenantId}",
  TENANT_USER: "/api/tenants/{tenantId}/users/{userId}",
  TENANT_USERS: "/api/tenants/{tenantId}/users",
  SIGNUP: "/api/signup",
  LOG: "/api/auth/_log",
};
```

**Initializing Nile Server & Customizing Routes:**

This setup is typically done in a central file on your server.
For a Vite project with a custom server (e.g., Express.js), you'd integrate this into your server setup. The `[...nile]` route file convention is common in frameworks like Next.js. Adapt as needed for your server structure.

**Example: `src/server/nile.ts` (or similar for your project)**

```typescript
// src/server/nile.ts (or your chosen path)
import { Nile } from "@niledatabase/server";

export const nile = await Nile({
  // workspace: "your_workspace_id", // From Nile dashboard
  // database: "your_database_name", // From Nile dashboard
  // apiBasePath: "your_nile_api_endpoint", // e.g., https://api.eu-west-1.niledatabase.io
  //
  // These are optional overrides for the API routes your app will expose
  api: {
    routePrefix: "/nile-api", // All routes will be under /nile-api/...
    routes: {
      SIGNIN: "/auth/login", // Becomes /nile-api/auth/login
      SIGNOUT: "/auth/logout", // Becomes /nile-api/auth/logout
      ME: "/users/profile", // Becomes /nile-api/users/profile
    },
  },
});

// Export handlers to be used in your route files
export const { handlers } = nile.api;
```

**Example: API Route Handler (e.g., `src/server/routes/auth.ts` if using Express)**

If you overrode `SIGNIN` to `/auth/login` and `SIGNOUT` to `/auth/logout` with `routePrefix: '/nile-api'`:

```typescript
// src/server/routes/auth.ts (Express.js example)
import { Router } from "express";
import { handlers } from "../nile"; // Your nile.ts from above

const authRouter = Router();

// Route for /nile-api/auth/login
authRouter.post("/auth/login", handlers.POST);

// Route for /nile-api/auth/logout
authRouter.post("/auth/logout", handlers.POST);

export default authRouter;
```

And for other routes that fall under the `routePrefix` but weren't specifically overridden (e.g., `/nile-api/tenants`):

```typescript
// src/server/routes/nileDefault.ts (Express.js example for catch-all)
import { Router } from "express";
import { handlers } from "../nile"; // Your nile.ts

const nileRouter = Router();

// This would handle routes like /nile-api/tenants, /nile-api/users/profile etc.
// The path here should match what's *not* covered by specific overrides
// and align with how your server routes requests.
nileRouter.all("*", (req, res) => {
  // Determine the correct handler based on req.method and req.path
  if (req.method === "POST" && handlers.POST) {
    return handlers.POST(req, res);
  }
  if (req.method === "GET" && handlers.GET) {
    return handlers.GET(req, res);
  }
  // Add DELETE, PUT as needed
  res.status(405).send("Method Not Allowed");
});

export default nileRouter;
```

**Your main server file (e.g., `src/server/index.ts`):**

```typescript
// src/server/index.ts (Express.js example)
import express from "express";
import ViteExpress from "vite-express";
import authRouter from "./routes/auth";
import nileRouter from "./routes/nileDefault";
// import { nile } from './nile'; // Your nile.ts instance

const app = express();
app.use(express.json());

// Mount your custom Nile API routes
app.use("/nile-api", authRouter); // Handles /nile-api/auth/login, /nile-api/auth/logout
app.use("/nile-api", nileRouter); // Handles other /nile-api/* routes

ViteExpress.listen(app, 3000, () =>
  console.log("Server is listening on port 3000...")
);
```

**Customizing React Components for Overridden Routes:**
If you override routes, update the `fetchUrl` or `basePath` props in your React components accordingly.

```tsx
// Example: components/AuthPage.tsx
import {
  SignOutButton,
  SignUpForm,
  SignedIn,
  SignedOut,
  // ... other components
} from "@niledatabase/react";
import "@niledatabase/react/styles.css";

export default function AuthPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <SignedIn
        className="flex flex-col gap-4"
        basePath="/nile-api" /* Matches your routePrefix */
      >
        {/* <UserInfo /> */}
        {/* <TenantSelector className="py-6 mb-10" /> */}
        <SignOutButton
          fetchUrl="/nile-api/auth/logout" /* Matches your overridden SIGNOUT route */
        />
      </SignedIn>
      <SignedOut basePath="/nile-api" /* Matches your routePrefix */>
        {/* Assuming SignUpForm internally constructs its URL based on basePath or defaults */}
        {/* If SignUpForm has a fetchUrl prop, you might need to set it if its specific route was overridden */}
        <SignUpForm
          createTenant
          // fetchUrl="/nile-api/auth/signup" // If SIGNUP was also overridden
        />
      </SignedOut>
    </div>
  );
}
```

**Notes on Route Customization:**

- Only specified routes are overridden; others use defaults under `routePrefix`.
- Placeholders like `{tenantId}` work as expected.
- Routes are set at server initialization and cannot be changed at runtime.

### Auth Module (Server-Side)

Securely authenticates users on the server. While React components handle much of this, direct server-side SDK use is sometimes necessary.
All methods are available under `nile.api.auth.*` or sometimes `nile.api.*` for convenience.

#### `login`

Logs in a user.
`await nile.api.login({ email, password, config })`

- **Parameters:**

  - `email`: (string) User's email.
  - `password`: (string) User's password.
  - `config`: (object, optional)
    - `returnResponse`: (boolean) If `true`, returns the `Response` object. If `false` (default), sets session cookie on success.

- **Example (without `returnResponse`):**

  ```typescript
  await nile.api.login({
    email: "<EMAIL>",
    password: "securepassword",
  });
  // Session cookie is set in nile.api.headers
  const authCookie = nile.api.headers?.get("cookie");
  // ... (token extraction logic as in original docs) ...
  ```

- **Example (with `returnResponse`):**

  ```typescript
  const loginRes = await nile.api.login({
    email: "<EMAIL>",
    password: "securepassword",
    config: { returnResponse: true },
  });

  if (loginRes && loginRes.ok) {
    const authCookie = loginRes.headers?.get("set-cookie");
    // ... (token extraction logic) ...
  } else {
    console.error("Login failed:", await loginRes?.text());
  }
  ```

#### `signOut`

Signs out a user server-side.
`await nile.api.auth.signOut()`

- **Behavior:** Removes auth headers from the `nile.api.headers` object. This does _not_ remove the cookie from the user's browser. Use the React SDK's `<SignOutButton />` or `signOut()` function for client-side sign-out.
- **Returns:** A Promise resolving to:

  - An object `{ url: string }` with the local sign-out URL if CSRF setup is correct.
  - A `Response` object (e.g., 400 status) on error (like missing CSRF).

- **Example:**
  ```typescript
  const signOutRes = await nile.api.auth.signOut();
  console.log("Sign out initiated:", signOutRes); // e.g., { url: 'http://localhost:3000/nile-api/auth/signout?csrf=true' }
  console.log("Nile API headers after sign out:", nile.api.headers); // Should be empty or auth headers removed
  ```

#### `getSession`

Gets the current session. Alias: `nile.api.session()`.
`await nile.api.auth.getSession()`

- **Returns:** A Promise resolving to a `JWT` object, an `ActiveSession` object, or `undefined` if no active session.

  ```typescript
  type JWT = {
    /* ... as in original ... */
  };
  type ActiveSession = {
    /* ... as in original, including user object ... */
  };
  ```

- **Example:**
  ```typescript
  // Important: Ensure nile.api.headers are set from the incoming request for this to work
  // e.g., in an Express middleware: nile.api.headers = req.headers;
  const session = await nile.api.auth.getSession();
  if (session) {
    console.log("Current session:", session);
  } else {
    console.log("No active session.");
  }
  ```

#### `getCsrf`

Gets a new CSRF token. Usually handled automatically.
`await nile.api.auth.getCsrf()`

- **Returns:** A Promise resolving to the CSRF token string.

#### `listProviders`

Gets enabled authentication providers for the current tenant.
`await nile.api.auth.listProviders()`

- **Returns:** A Promise resolving to a `Providers` object.
  ```typescript
  type ProviderName = /* ... as in original ... */;
  type Providers = { [providerName in ProviderName]?: Provider; }; // Optional for safety
  type Provider = { /* ... as in original ... */ };
  ```

#### `headers`

The `nile.api.headers` object (a `Headers` instance) holds/sets headers for the current session context for server-side SDK calls. Crucial for passing user context (cookies, tenant ID) to Nile APIs.

- **Setting from an incoming request (e.g., Express middleware):**
  ```typescript
  // In your server middleware or before calling Nile SDK methods that need auth context
  // app.use((req, res, next) => {
  //   nile.api.headers = new Headers(req.headers as HeadersInit);
  //   next();
  // });
  ```
- **Manual modification (use with care):**
  ```typescript
  nile.api.headers.set("X-Tenant-Id", "some-tenant-id");
  console.log(nile.api.headers.get("X-Tenant-Id"));
  ```

_(The Users, Tenants, and DB module sections would follow a similar "neatified" structure: clear headings, parameter tables or lists, concise return descriptions, and well-formatted code examples. I'll summarize their key methods for brevity here, but you can expand them like the Auth module.)_

### Users Module (Server-Side)

Manage users via `nile.api.users.*`.
`interface User { /* ... as in original ... */ }`

- `createUser({ email, password, preferredName?, newTenant? })`: Creates a new user.
- `createTenantUser({ email, password, preferredName? })`: Creates a user within the current tenant context (`nile.tenantId` must be set).
- `me()`: Gets the current authenticated user's details.
- `updateMe({ name?, familyName?, ... })`: Updates the current user.
- `linkUser({ id, tenantId? })` or `linkUser(userId, tenantId?)`: Adds a user to a tenant.
- `unlinkUser({ id, tenantId? })` or `unlinkUser(userId, tenantId?)`: Removes a user from a tenant.
- `listUsers()`: Lists users in the current tenant (`nile.tenantId` must be set).
- `updateUser({ id?, name?, ... })`: Updates a specified user (requires appropriate permissions).

### Tenants Module (Server-Side)

Manage tenants via `nile.api.tenants.*`.
`interface Tenant { id: string; name: string; /* ...other fields... */ }`

- **Setting current tenant:** `nile.tenantId = 'your-tenant-id';` This affects subsequent tenant-scoped operations.
- `createTenant(name | { name, id? })`: Creates a new tenant and links to current user.
- `getTenant(id?)`: Gets tenant info (by ID or current `nile.tenantId`).
- `updateTenant(name)`: Updates the name of the current tenant (`nile.tenantId`).
- `deleteTenant(id?)`: Soft-deletes a tenant (by ID or current `nile.tenantId`).
- `listTenants()`: Lists tenants for the current user.

### DB Module (Server-Side)

Query your Nile database via `nile.db.*`. Tenant-aware.

- `query(sql: string, params?: any[])`: Executes SQL.

  - **Shared Table:** `await nile.db.query('SELECT * FROM shared_table');`
  - **Tenant-Aware Table (cross-tenant if `nile.tenantId` is null):**
    ```typescript
    nile.tenantId = null; // For cross-tenant query
    const results = await nile.db.query("SELECT * FROM tenant_specific_table");
    ```
  - **Tenant-Aware Table (specific tenant):**
    ```typescript
    nile.tenantId = "target-tenant-id";
    // INSERT operations should include tenant_id matching nile.tenantId
    await nile.db.query("INSERT INTO items (name, tenant_id) VALUES ($1, $2)", [
      "My Item",
      nile.tenantId,
    ]);
    const items = await nile.db.query("SELECT * FROM items"); // Scoped to 'target-tenant-id'
    ```

- `client()`: Gets a raw database client for transactions. Remember `client.release()`.
  ```typescript
  const client = await nile.db.client();
  try {
    await client.query("BEGIN");
    // ...your queries...
    await client.query("COMMIT");
  } catch (e) {
    await client.query("ROLLBACK");
    throw e;
  } finally {
    client.release();
  }
  ```

---

## 3. Feature Guides

### Email Verification Flow

Email verification confirms user identities. Nile Auth handles sending verification emails and token management.

**Prerequisites:**

1.  Configured SMTP server in Nile Auth UI (Tenants & Users -> Configuration -> Email templates).
2.  Your web server/API routes set up to handle Nile Auth requests (as detailed in "Server Setup & Route Customization").

**Implementation Steps:**

1.  **Install Dependencies:**

    ```bash
    npm install @niledatabase/server @niledatabase/react
    ```

2.  **Expose Nile API Routes:**
    Ensure your server (e.g., Express in a Vite setup) correctly exposes the Nile handlers. Refer to the "Server Setup & Route Customization" section.
    For example, your `src/server/nile.ts` and route handlers should be configured.

3.  **Middleware for Verification Enforcement (Server-Side Example - Express):**
    Protect routes that require a verified email.

    ```typescript
    // Example: src/server/middleware/requireVerifiedEmail.ts
    import { Request, Response, NextFunction } from "express";
    import { nile } from "../nile"; // Your initialized Nile server instance

    export async function requireVerifiedEmail(
      req: Request,
      res: Response,
      next: NextFunction
    ) {
      // Ensure Nile SDK has access to request headers for session context
      // This might be handled globally or per-request
      const tempHeaders = new Headers();
      Object.entries(req.headers).forEach(([key, value]) => {
        if (typeof value === "string") tempHeaders.set(key, value);
        else if (Array.isArray(value))
          value.forEach((v) => tempHeaders.append(key, v));
      });
      // Temporarily assign headers for the scope of this request for 'me' call
      const originalHeaders = nile.api.headers;
      nile.api.headers = tempHeaders;

      try {
        const user = await nile.api.users.me(); // Fetches user based on session from headers

        if (!user || user instanceof Response || !user.emailVerified) {
          // Restore original headers
          nile.api.headers = originalHeaders;
          // Redirect to a page prompting for verification
          // Or send an error response if it's an API endpoint
          return res.status(403).json({
            error:
              "Email not verified. Please check your inbox or request a new verification email.",
          });
          // For page loads, you might redirect: return res.redirect('/verify-email-prompt');
        }
        // Restore original headers
        nile.api.headers = originalHeaders;
        next();
      } catch (error) {
        nile.api.headers = originalHeaders;
        console.error("Error in requireVerifiedEmail middleware:", error);
        return res
          .status(500)
          .json({ error: "Internal server error during verification check." });
      }
    }

    // Usage in your route definition:
    // import { requireVerifiedEmail } from './middleware/requireVerifiedEmail';
    // app.get('/api/sensitive-data', requireVerifiedEmail, (req, res) => { /* ... */ });
    ```

4.  **Verification Button/Action in Frontend (React):**
    Allow users to request a new verification email. This often uses the `useEmailSignIn` hook, as sending a "sign-in" link also serves as verification if the user isn't verified.

    ```tsx
    // src/components/RequestVerificationEmail.tsx
    "use client";
    import { useEmailSignIn } from "@niledatabase/react";
    import { useSession } from "@niledatabase/react"; // To get current user's email
    import { useState } from "react";

    export default function RequestVerificationEmail() {
      const { data: session } = useSession();
      const [message, setMessage] = useState("");

      const sendVerificationLink = useEmailSignIn({
        onSuccess: () =>
          setMessage("Verification email sent. Please check your inbox."),
        onError: (error) =>
          setMessage(`Failed to send email: ${error.message}`),
      });

      const handleSendVerification = () => {
        if (session?.user?.email) {
          setMessage("Sending...");
          sendVerificationLink({ email: session.user.email });
        } else {
          setMessage("Could not find user email. Are you signed in?");
        }
      };

      if (!session?.user || session.user.emailVerified) {
        return null; // Don't show if user is verified or not logged in
      }

      return (
        <div className="p-4 my-4 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700">
          <p>Your email is not verified.</p>
          <button
            onClick={handleSendVerification}
            className="mt-2 px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            Resend Verification Email
          </button>
          {message && <p className="mt-2 text-sm">{message}</p>}
        </div>
      );
    }
    ```

5.  **Sign-in via Email (Magic Link):**
    The `useEmailSignIn` hook or `<EmailSignIn />` component can be used for passwordless login, which inherently verifies the email upon first successful use if not already verified.

    ```tsx
    // src/components/EmailLoginForm.tsx
    "use client";
    import { EmailSignIn } from "@niledatabase/react"; // This is a pre-built component
    // Or use useEmailSignIn hook for a custom form as shown previously

    export default function EmailLoginPage() {
      return (
        <div>
          <h2 className="text-xl font-semibold mb-4">Sign In with Email</h2>
          <EmailSignIn
          // You can pass props like onSuccess, onError, callbackUrl
          // onSuccess={() => console.log('Magic link request successful')}
          // callbackUrl="/dashboard" // Where user lands after clicking link
          />
          <p className="mt-2 text-sm text-gray-600">
            We'll send you a magic link to sign in.
          </p>
        </div>
      );
    }
    ```

**Verification Flow Summary:**

1.  User signs up or attempts to access a protected resource.
2.  If email is not verified:
    - Backend middleware can block access or redirect.
    - Frontend can display a prompt to verify (e.g., `RequestVerificationEmail` component).
3.  User clicks "Resend Verification Email" or attempts a magic link login.
4.  Nile sends an email with a verification/sign-in token (via your configured SMTP).
5.  User clicks the link in the email.
6.  The link URL hits your Nile callback endpoint (e.g., `/api/auth/callback/email?...`).
7.  Nile Auth verifies the token, updates the user's `emailVerified` status, and creates an authenticated session.
8.  User is redirected (e.g., to `callbackUrl` or app homepage) and can now access resources.

---

This revised documentation should be much clearer and easier to navigate for developing Earnhub. Remember to adapt file paths and server setup examples (like Express) to your specific Vite project structure if it differs. Good luck!
