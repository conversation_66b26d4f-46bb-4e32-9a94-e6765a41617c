
import { cn } from '@/lib/utils';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  const footerLinks = [
    {
      title: "Company",
      links: [
        { label: "About Us", url: "#" },
        { label: "Contact", url: "#" },
        { label: "Careers", url: "#" },
      ]
    },
    {
      title: "Resources",
      links: [
        { label: "Blog", url: "#" },
        { label: "FAQ", url: "#" },
        { label: "Support", url: "#" },
      ]
    },
    {
      title: "Legal",
      links: [
        { label: "Terms", url: "#" },
        { label: "Privacy", url: "#" },
        { label: "Cookies", url: "#" },
      ]
    }
  ];
  
  return (
    <footer className="bg-white border-t border-earnhub-gray/20">
      <div className="max-w-7xl mx-auto py-12 px-6 md:px-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-10">
          {/* Logo and info */}
          <div className="col-span-1 md:col-span-1">
            <div className="text-2xl font-bold mb-4">
              <span className="text-earnhub-red">Earn</span>
              <span className="text-earnhub-dark">Hub</span>
            </div>
            
            <p className="text-earnhub-darkGray mb-6 max-w-xs">
              EarnHub is a platform that helps people earn money through referrals, tasks, and competitions.
            </p>
            
            <div className="flex space-x-4">
              {/* Social Media Icons */}
              {['facebook', 'twitter', 'instagram'].map((social) => (
                <a 
                  key={social} 
                  href="#" 
                  className="w-10 h-10 rounded-full bg-earnhub-lightGray flex items-center justify-center hover:bg-earnhub-red/10 transition-colors"
                >
                  <div className="w-5 h-5 rounded-full bg-earnhub-red/40"></div>
                </a>
              ))}
            </div>
          </div>
          
          {/* Footer Links */}
          {footerLinks.map((section) => (
            <div key={section.title} className="col-span-1">
              <h3 className="font-semibold text-earnhub-dark mb-4">{section.title}</h3>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.label}>
                    <a 
                      href={link.url} 
                      className="text-earnhub-darkGray hover:text-earnhub-red transition-colors"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
        
        {/* Bottom Bar */}
        <div className="border-t border-earnhub-gray/20 mt-12 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-earnhub-darkGray text-sm mb-4 md:mb-0">
            © {currentYear} EarnHub. All rights reserved.
          </p>
          
          <div className="flex space-x-6">
            <a href="#" className="text-earnhub-darkGray hover:text-earnhub-red text-sm transition-colors">
              Terms
            </a>
            <a href="#" className="text-earnhub-darkGray hover:text-earnhub-red text-sm transition-colors">
              Privacy
            </a>
            <a href="#" className="text-earnhub-darkGray hover:text-earnhub-red text-sm transition-colors">
              Cookies
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
