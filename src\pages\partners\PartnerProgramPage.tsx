
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { fetchPartnerProgram } from "@/services/partnerService";
import { PartnerTier } from "@/types/partner";
import { CheckCircle, TrendingUp, Users, Wallet } from "lucide-react";

const PartnerProgramPage = () => {
  const navigate = useNavigate();
  const [selectedTier, setSelectedTier] = useState<string>("basic");
  
  const { data: program, isLoading } = useQuery({
    queryKey: ['partnerProgram'],
    queryFn: fetchPartnerProgram
  });
  
  const handleJoinNow = () => {
    navigate("/partners/register");
  };
  
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 pt-24">
        <div className="flex flex-col items-center">
          <div className="w-full max-w-4xl animate-pulse">
            <div className="h-10 bg-gray-200 rounded-md mb-6"></div>
            <div className="h-20 bg-gray-200 rounded-md mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-80 bg-gray-200 rounded-md"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8 pt-24">
      <div className="max-w-4xl mx-auto">
        <header className="text-center mb-12">
          <Badge className="mb-4 bg-earnhub-red">New Opportunity</Badge>
          <h1 className="text-3xl md:text-5xl font-bold mb-4">
            Earn Money as an EarnHub Partner
          </h1>
          <p className="text-lg md:text-xl text-gray-600 mb-8">
            Join our partner program and earn commissions by promoting and selling EarnHub products and services.
          </p>
          <Button size="lg" onClick={handleJoinNow} className="bg-earnhub-red hover:bg-red-700">
            Join Now
          </Button>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <Card className="flex flex-col">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <TrendingUp className="h-12 w-12 text-earnhub-red" />
              </div>
              <CardTitle>Uncapped Earnings</CardTitle>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-center">
                There's no limit to how much you can earn. Commissions can go up to 40% for premium services and courses.
              </p>
            </CardContent>
          </Card>

          <Card className="flex flex-col">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <Wallet className="h-12 w-12 text-earnhub-red" />
              </div>
              <CardTitle>Fast Payouts</CardTitle>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-center">
                Get paid directly to your EarnHub wallet. Access your earnings quickly and easily.
              </p>
            </CardContent>
          </Card>

          <Card className="flex flex-col">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <Users className="h-12 w-12 text-earnhub-red" />
              </div>
              <CardTitle>Dedicated Support</CardTitle>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-center">
                Get access to exclusive marketing materials and dedicated partner support to maximize your success.
              </p>
            </CardContent>
          </Card>
        </div>

        <section className="mb-16">
          <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex flex-col items-center">
              <div className="rounded-full bg-earnhub-red/10 p-4 mb-4">
                <span className="text-2xl font-bold text-earnhub-red">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Join the Program</h3>
              <p className="text-center">Sign up and get approved to become an EarnHub Partner</p>
            </div>

            <div className="flex flex-col items-center">
              <div className="rounded-full bg-earnhub-red/10 p-4 mb-4">
                <span className="text-2xl font-bold text-earnhub-red">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Generate Links</h3>
              <p className="text-center">Create unique affiliate links for products and services</p>
            </div>

            <div className="flex flex-col items-center">
              <div className="rounded-full bg-earnhub-red/10 p-4 mb-4">
                <span className="text-2xl font-bold text-earnhub-red">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Earn Commissions</h3>
              <p className="text-center">Get paid when customers purchase through your links</p>
            </div>
          </div>
        </section>

        <section className="mb-16">
          <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">Partnership Tiers</h2>
          
          {program && (
            <Tabs defaultValue={selectedTier} onValueChange={setSelectedTier}>
              <TabsList className="grid grid-cols-4">
                {program.tiers.map((tier: PartnerTier) => (
                  <TabsTrigger key={tier.level} value={tier.level}>
                    {tier.name}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              {program.tiers.map((tier: PartnerTier) => (
                <TabsContent key={tier.level} value={tier.level}>
                  <Card>
                    <CardHeader>
                      <CardTitle>{tier.name}</CardTitle>
                      <CardDescription>
                        {tier.baseCommissionBonus > 0 ? 
                          `${tier.baseCommissionBonus}% bonus on top of base commission rates` : 
                          'Standard commission rates'}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2">Requirements:</h4>
                          <ul className="list-disc pl-5 space-y-1">
                            {tier.requirements.map((req, index) => (
                              <li key={index}>{req}</li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-medium mb-2">Benefits:</h4>
                          <ul className="space-y-2">
                            {tier.benefits.map((benefit, index) => (
                              <li key={index} className="flex items-start">
                                <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                                <span>{benefit}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      {tier.level === "basic" ? (
                        <Button onClick={handleJoinNow} className="w-full bg-earnhub-red hover:bg-red-700">
                          Join Now
                        </Button>
                      ) : (
                        <div className="w-full text-center text-sm text-gray-500">
                          Start with Basic tier and grow your partnership to unlock this tier
                        </div>
                      )}
                    </CardFooter>
                  </Card>
                </TabsContent>
              ))}
            </Tabs>
          )}
        </section>

        <section className="mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">Frequently Asked Questions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-bold">How do I get paid?</h4>
                  <p className="mt-1">Commissions are paid directly to your EarnHub wallet. You can withdraw them at any time.</p>
                </div>
                <div>
                  <h4 className="font-bold">How much can I earn?</h4>
                  <p className="mt-1">Commission rates vary by product, ranging from 10% to 40% depending on the product and your partnership tier.</p>
                </div>
                <div>
                  <h4 className="font-bold">Who can join?</h4>
                  <p className="mt-1">Any active EarnHub member can apply to become a partner. Approval is based on your account standing and activity.</p>
                </div>
                <div>
                  <h4 className="font-bold">How do I track my sales?</h4>
                  <p className="mt-1">You'll get access to a comprehensive dashboard showing clicks, conversions, and commissions in real-time.</p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleJoinNow} className="w-full">
                Become a Partner Today
              </Button>
            </CardFooter>
          </Card>
        </section>
      </div>
    </div>
  );
};

export default PartnerProgramPage;
