
import { useState, useEffect } from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from '@/components/ui/carousel';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Sparkles } from 'lucide-react';
import { Product } from '@/types/shop';
import { fetchFeaturedProducts } from '@/services/shopService';
import { useCart } from '@/contexts/CartContext';
import { useQuery } from '@tanstack/react-query';

export const FeaturedProducts = () => {
  const { addToCart } = useCart();
  
  const { data: featuredProducts, isLoading, error } = useQuery({
    queryKey: ['featuredProducts'],
    queryFn: fetchFeaturedProducts
  });

  if (isLoading) {
    return (
      <div className="space-y-4 py-6">
        <div className="flex items-center space-x-2">
          <Sparkles className="text-earnhub-red" size={20} />
          <h2 className="text-2xl font-bold">Featured Products</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 animate-pulse">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-64 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return <div className="text-red-500">Failed to load featured products</div>;
  }

  if (!featuredProducts?.length) {
    return null;
  }

  const handleAddToCart = (product: Product) => {
    addToCart({
      productId: product._id,
      title: product.title,
      price: product.discountedPrice || product.price,
      quantity: 1,
      image: product.image,
      savings: product.savings
    });
  };

  return (
    <div className="space-y-4 py-6">
      <div className="flex items-center space-x-2">
        <Sparkles className="text-earnhub-red" size={20} />
        <h2 className="text-2xl font-bold">Featured Products</h2>
      </div>
      
      <Carousel className="w-full">
        <CarouselContent className="-ml-2 md:-ml-4">
          {featuredProducts.map((product) => (
            <CarouselItem key={product._id} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
              <Card className="overflow-hidden h-full">
                <div className="relative h-40 bg-gray-100">
                  <img 
                    src={product.image || "/placeholder.svg"} 
                    alt={product.title} 
                    className="w-full h-full object-cover"
                  />
                  {product.hot && (
                    <div className="absolute top-2 right-2">
                      <Badge className="bg-orange-500">Hot Deal</Badge>
                    </div>
                  )}
                  {product.badge && (
                    <div className="absolute top-2 right-2">
                      <Badge className="bg-earnhub-red">{product.badge}</Badge>
                    </div>
                  )}
                </div>
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-medium text-lg">{product.title}</h3>
                      <Badge variant="outline">{product.category}</Badge>
                    </div>
                    <div className="text-right">
                      {product.discountedPrice && (
                        <div className="text-sm text-gray-400 line-through">KES {product.price.toLocaleString()}</div>
                      )}
                      <div className="text-lg font-bold text-earnhub-red">
                        KES {(product.discountedPrice || product.price).toLocaleString()}
                      </div>
                      {product.savings && (
                        <div className="text-xs text-green-600 font-medium">Save {product.savings}</div>
                      )}
                    </div>
                  </div>
                  <Button 
                    className="w-full mt-2"
                    onClick={() => handleAddToCart(product)}
                  >
                    Add to Cart
                  </Button>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="left-0 md:-left-12" />
        <CarouselNext className="right-0 md:-right-12" />
      </Carousel>
    </div>
  );
};
