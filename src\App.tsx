import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { AuthProvider } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import Index from "./pages/Index";
import Auth from "./pages/Auth";
import Login from "./pages/Login";
import SignUp from "./pages/SignUp";
import DashboardLayout from "./components/DashboardLayout";
import Dashboard from "./pages/Dashboard";
import WalletPage from "./pages/WalletPage";
import TasksPage from "./pages/TasksPage";
import TaskDetailsPage from "./pages/TaskDetailsPage";
import ProfilePage from "./pages/ProfilePage";
import EditProfilePage from "./pages/EditProfilePage";
import ReferralsPage from "./pages/ReferralsPage";
import NotificationsPage from "./pages/NotificationsPage";
import SettingsPage from "./pages/SettingsPage";
import NotFound from "./pages/NotFound";

// Additional pages
import QuestsPage from "./pages/QuestsPage";
import AchievementsPage from "./pages/AchievementsPage";
import Competitions from "./pages/Competitions";
import CompetitionDetail from "./pages/CompetitionDetail";
import RafflePage from "./pages/RafflePage";
import ShopPage from "./pages/ShopPage";
import MembershipPage from "./pages/MembershipPage";
import OpportunitiesPage from "./pages/OpportunitiesPage";
import OpportunityDetail from "./pages/OpportunityDetail";
import AmbassadorsPage from "./pages/AmbassadorsPage";
import GuildsPage from "./pages/GuildsPage";
import ReferralMetricsPage from "./pages/ReferralMetricsPage";
import TaskDetail from "./pages/TaskDetail";

// Shop pages
import CategoriesPage from "./pages/shop/CategoriesPage";
import CategoryDetailPage from "./pages/shop/CategoryDetailPage";
import ProductDetailPage from "./pages/shop/ProductDetailPage";
import CheckoutPage from "./pages/shop/CheckoutPage";

// Partner pages
import PartnerProgramPage from "./pages/partners/PartnerProgramPage";
import PartnerRegistrationPage from "./pages/partners/PartnerRegistrationPage";
import PartnerDashboardPage from "./pages/partners/PartnerDashboardPage";
import PartnerDashboardLinksPage from "./pages/partners/PartnerDashboardLinksPage";
import PublicProductPage from "./pages/partners/PublicProductPage";
import PublicCheckoutPage from "./pages/partners/PublicCheckoutPage";
import PublicThankYouPage from "./pages/partners/PublicThankYouPage";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <HelmetProvider>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/auth" element={<Auth />} />
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<SignUp />} />
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <Dashboard />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/wallet"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <WalletPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/tasks"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <TasksPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/tasks/:taskId"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <TaskDetailsPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/profile"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <ProfilePage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/profile/:userId"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <ProfilePage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/edit-profile"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <EditProfilePage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/referrals"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <ReferralsPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/notifications"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <NotificationsPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <SettingsPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />

              {/* Additional protected routes */}
              <Route
                path="/quests"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <QuestsPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/achievements"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <AchievementsPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/competitions"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <Competitions />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/competitions/:id"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <CompetitionDetail />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/raffle"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <RafflePage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/shop"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <ShopPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/membership"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <MembershipPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/opportunities"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <OpportunitiesPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/opportunities/:id"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <OpportunityDetail />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/ambassadors"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <AmbassadorsPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/guilds"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <GuildsPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/referral-metrics"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <ReferralMetricsPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/task/:id"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <TaskDetail />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />

              {/* Shop routes */}
              <Route
                path="/shop/categories"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <CategoriesPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/shop/categories/:categoryId"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <CategoryDetailPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/shop/products/:productId"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <ProductDetailPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/shop/checkout"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <CheckoutPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />

              {/* Partner routes */}
              <Route
                path="/partners"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <PartnerProgramPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/partners/register"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <PartnerRegistrationPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/partners/dashboard"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <PartnerDashboardPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/partners/links"
                element={
                  <ProtectedRoute>
                    <DashboardLayout>
                      <PartnerDashboardLinksPage />
                    </DashboardLayout>
                  </ProtectedRoute>
                }
              />

              {/* Public partner routes (no authentication required) */}
              <Route
                path="/p/:partnerId/product/:productId"
                element={<PublicProductPage />}
              />
              <Route
                path="/p/:partnerId/checkout"
                element={<PublicCheckoutPage />}
              />
              <Route
                path="/p/:partnerId/thank-you"
                element={<PublicThankYouPage />}
              />

              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
    </HelmetProvider>
  </QueryClientProvider>
);

export default App;
