
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronRight, Package } from 'lucide-react';
import { ProductCategory } from '@/types/shop';
import { fetchProductCategories } from '@/services/shopService';
import { useQuery } from '@tanstack/react-query';

export const CategoryList = () => {
  const { data: categories, isLoading, error } = useQuery({
    queryKey: ['productCategories'],
    queryFn: fetchProductCategories
  });

  if (isLoading) {
    return (
      <div className="space-y-4 py-6">
        <h2 className="text-xl font-bold flex items-center">
          <Package size={20} className="mr-2" />
          Categories
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 animate-pulse">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error || !categories?.length) {
    return null;
  }

  return (
    <div className="space-y-4 py-6">
      <h2 className="text-xl font-bold flex items-center">
        <Package size={20} className="mr-2" />
        Categories
      </h2>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {categories.map((category) => (
          <Link 
            to={`/shop/category/${category._id}`} 
            key={category._id}
          >
            <Card className="overflow-hidden h-full hover:shadow-md transition-all hover:-translate-y-1">
              <div className="h-24 bg-gray-100">
                <img 
                  src={category.image || "/placeholder.svg"} 
                  alt={category.name} 
                  className="w-full h-full object-cover"
                />
              </div>
              <CardContent className="p-4">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium">{category.name}</h3>
                  <ChevronRight size={16} className="text-earnhub-red" />
                </div>
                <p className="text-sm text-gray-500 line-clamp-1">{category.description}</p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
      
      <div className="flex justify-center">
        <Button asChild variant="outline">
          <Link to="/shop/categories">View All Categories</Link>
        </Button>
      </div>
    </div>
  );
};
