
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Zap, Briefcase, Award, User, 
  Medal, Users, Ticket, ShoppingCart, Wallet,
  Link
} from "lucide-react";
import { Link as RouterLink } from "react-router-dom";
import { useEffect } from "react";
import anime from "animejs";

// Quick access links with compatibility for Sanity CMS data structure
interface QuickAccessItem {
  name: string;
  icon: keyof typeof iconMap;
  path: string;
  // Sanity CMS fields
  _id?: string;
  _type?: string;
}

// Map of icon names to components
const iconMap = {
  Zap,
  Briefcase, 
  Award,
  User,
  Medal, 
  Users, 
  Ticket, 
  ShoppingCart,
  Wallet,
  Link
};

// Default items when no Sanity data is available
const defaultQuickAccessItems: QuickAccessItem[] = [
  { name: "Tasks", icon: "Zap", path: "/tasks" },
  { name: "Opportunities", icon: "Briefcase", path: "/opportunities" },
  { name: "Guilds", icon: "Users", path: "/guilds" },
  { name: "Membership", icon: "Award", path: "/membership" },
  { name: "Quest<PERSON>", icon: "Medal", path: "/quests" },
  { name: "Partners", icon: "Link", path: "/partners" },
  { name: "Raffle", icon: "Ticket", path: "/raffle" },
  { name: "Shop", icon: "ShoppingCart", path: "/shop" },
  { name: "Wallet", icon: "Wallet", path: "/wallet" }
];

const QuickAccessGrid = () => {
  // In a real implementation, you would fetch this data from Sanity CMS
  const quickAccessItems = defaultQuickAccessItems;
  
  useEffect(() => {
    // Animation for the quick access grid
    anime({
      targets: '.quick-access-button',
      scale: [0.9, 1],
      opacity: [0, 1],
      delay: anime.stagger(50, {grid: [3, 3], from: 'center'}),
      easing: 'easeOutExpo'
    });
  }, []);
  
  return (
    <div className="grid grid-cols-3 md:grid-cols-5 gap-3">
      {quickAccessItems.map((item, index) => {
        const IconComponent = iconMap[item.icon];
        return (
          <Button 
            key={item._id || index} 
            variant="outline" 
            className="h-auto flex flex-col py-4 hover:border-earnhub-red hover:bg-earnhub-red/5 quick-access-button"
            asChild
          >
            <RouterLink to={item.path}>
              {IconComponent && <IconComponent className="h-6 w-6 mb-2 text-earnhub-red" />}
              <span className="text-xs">{item.name}</span>
            </RouterLink>
          </Button>
        );
      })}
    </div>
  );
};

export default QuickAccessGrid;
