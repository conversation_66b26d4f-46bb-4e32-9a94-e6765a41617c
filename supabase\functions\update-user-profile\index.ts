// supabase/functions/update-user-profile/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from './cors.ts';

interface ProfileUpdatePayload {
  full_name?: string;
  username?: string;
  gender?: string;
  age_range?: string;
  county?: string;
  country?: string;
  income_level?: string;
  education_level?: string;
  social_media?: Record<string, string>; // JSONB
  availability?: string;
  avatar_sanity_id?: string;
  bio?: string;
}

// Basic validation (can be expanded)
function validatePayload(payload: ProfileUpdatePayload): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  if (payload.full_name && payload.full_name.length > 255) errors.push("Full name exceeds 255 characters.");
  if (payload.username && payload.username.length > 50) errors.push("Username exceeds 50 characters.");
  if (payload.username && !/^[a-zA-Z0-9_]+$/.test(payload.username)) errors.push("Username contains invalid characters. Use only alphanumeric and underscores.");
  if (payload.gender && payload.gender.length > 20) errors.push("Gender exceeds 20 characters.");
  if (payload.age_range && payload.age_range.length > 20) errors.push("Age range exceeds 20 characters.");
  if (payload.county && payload.county.length > 100) errors.push("County exceeds 100 characters.");
  if (payload.country && payload.country.length > 100) errors.push("Country exceeds 100 characters.");
  if (payload.income_level && payload.income_level.length > 50) errors.push("Income level exceeds 50 characters.");
  if (payload.education_level && payload.education_level.length > 50) errors.push("Education level exceeds 50 characters.");
  if (payload.availability && payload.availability.length > 50) errors.push("Availability exceeds 50 characters.");
  if (payload.avatar_sanity_id && payload.avatar_sanity_id.length > 255) errors.push("Avatar Sanity ID exceeds 255 characters.");
  // social_media (JSONB) and bio (TEXT) have less strict length validation here, but DB might enforce.

  return { valid: errors.length === 0, errors };
}

async function isUsernameTaken(supabaseClient: SupabaseClient, username: string, currentUserId: string): Promise<boolean> {
  const { data, error } = await supabaseClient
    .from('profiles')
    .select('id')
    .eq('username', username)
    .neq('id', currentUserId) // Important: exclude the current user's profile
    .limit(1);

  if (error) {
    console.error('Error checking username uniqueness:', error);
    throw new Error(`Error checking username: ${error.message}`);
  }
  return data && data.length > 0;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  if (req.method !== 'POST') { // Or PUT
    return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    );

    const { data: { user }, error: userError } = await supabaseClient.auth.getUser();
    if (userError || !user) {
      console.error('User not authenticated:', userError);
      return new Response(JSON.stringify({ error: 'User not authenticated' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 401,
      });
    }
    const userId = user.id;

    const payload: ProfileUpdatePayload = await req.json();
    console.log(`Update request for user ${userId} with payload:`, payload);

    // Exclude non-updatable fields explicitly
    const { profile_completion_percentage, ...updatablePayload } = payload as any;
    if (Object.keys(updatablePayload).length === 0) {
        return new Response(JSON.stringify({ error: 'No fields provided for update.' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400,
        });
    }

    const { valid, errors: validationErrors } = validatePayload(updatablePayload);
    if (!valid) {
      console.warn('Validation errors:', validationErrors);
      return new Response(JSON.stringify({ error: 'Validation failed', details: validationErrors }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // Check username uniqueness if username is being updated
    if (updatablePayload.username) {
      const usernameTaken = await isUsernameTaken(supabaseClient, updatablePayload.username, userId);
      if (usernameTaken) {
        return new Response(JSON.stringify({ error: 'Username is already taken.' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 409, // Conflict
        });
      }
    }
    
    // Prepare the data for Supabase, ensuring no undefined fields are sent if not provided
    const updateData: Record<string, any> = {};
    for (const key in updatablePayload) {
        if (Object.prototype.hasOwnProperty.call(updatablePayload, key)) {
            const value = (updatablePayload as any)[key];
            if (value !== undefined) { // Only include keys that were actually in the payload
                 updateData[key] = value;
            }
        }
    }
    // The 'updated_at' field is handled by the trigger in the database.

    const { data: updatedProfile, error: updateError } = await supabaseClient
      .from('profiles')
      .update(updateData)
      .eq('id', userId)
      .select() // Select the updated profile data
      .single();

    if (updateError) {
      console.error('Error updating profile:', updateError);
      // Check for specific error types, e.g., RLS violation (though less likely if using user's ID)
      if (updateError.code === '23505' && updateError.message.includes('profiles_username_key')) {
         // This is a fallback, primary check is isUsernameTaken
        return new Response(JSON.stringify({ error: 'Username is already taken (database constraint).' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 409,
        });
      }
      return new Response(JSON.stringify({ error: `Failed to update profile: ${updateError.message}` }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }

    console.log(`Profile updated successfully for user ${userId}:`, updatedProfile);
    return new Response(JSON.stringify({ message: 'Profile updated successfully', profile: updatedProfile }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Unhandled error in update-user-profile Edge Function:', error);
    // Check if error is due to invalid JSON payload
    if (error instanceof SyntaxError && error.message.includes("JSON")) {
        return new Response(JSON.stringify({ error: 'Invalid JSON payload' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400,
        });
    }
    return new Response(JSON.stringify({ error: error.message || 'An unexpected error occurred' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});
