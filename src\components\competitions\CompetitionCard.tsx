
import { formatDistanceToNow } from 'date-fns';
import { Link } from 'react-router-dom';
import { Award, Users, Calendar } from 'lucide-react';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CompetitionType } from '@/types/competitions';

interface CompetitionCardProps {
  competition: CompetitionType;
}

const CompetitionCard: React.FC<CompetitionCardProps> = ({ competition }) => {
  const {
    id,
    title,
    image,
    type,
    participants,
    startDate,
    endDate,
    userPosition,
    status,
    prizes
  } = competition;

  const formattedStartDate = formatDistanceToNow(new Date(startDate), { addSuffix: true });
  const formattedEndDate = formatDistanceToNow(new Date(endDate), { addSuffix: true });

  const getTypeColor = (type: string): string => {
    switch (type) {
      case 'referral':
        return 'bg-emerald-100 text-emerald-800';
      case 'sales':
        return 'bg-blue-100 text-blue-800';
      case 'task':
        return 'bg-amber-100 text-amber-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusInfo = () => {
    if (status === 'active') {
      return {
        label: 'Ends',
        date: formattedEndDate
      };
    } else if (status === 'upcoming') {
      return {
        label: 'Starts',
        date: formattedStartDate
      };
    } else {
      return {
        label: 'Ended',
        date: formattedEndDate
      };
    }
  };
  
  const statusInfo = getStatusInfo();

  return (
    <Card className="overflow-hidden flex flex-col">
      <div className="h-48 relative overflow-hidden">
        <img 
          src={image} 
          alt={title} 
          className="w-full h-full object-cover"
        />
        <div className="absolute top-3 left-3">
          <Badge className={`${getTypeColor(type)} uppercase text-xs font-medium px-2.5 py-1`}>
            {type}
          </Badge>
        </div>
        {userPosition && userPosition <= 3 && status === 'active' && (
          <div className="absolute top-3 right-3">
            <Badge className="bg-amber-100 text-amber-800 uppercase text-xs font-medium px-2.5 py-1">
              {userPosition === 1 ? '🏆 1st Place' : userPosition === 2 ? '🥈 2nd Place' : '🥉 3rd Place'}
            </Badge>
          </div>
        )}
      </div>

      <CardHeader className="pb-0">
        <h3 className="text-xl font-bold">{title}</h3>
      </CardHeader>
      
      <CardContent className="flex-grow space-y-3 py-3">
        <div className="flex items-center text-sm text-gray-600">
          <Users className="h-4 w-4 mr-2" />
          <span>{participants} Participants</span>
        </div>
        
        <div className="flex items-center text-sm text-gray-600">
          <Calendar className="h-4 w-4 mr-2" />
          <span>{statusInfo.label} {statusInfo.date}</span>
        </div>
        
        <div className="mt-3">
          <h4 className="text-sm font-medium flex items-center mb-1">
            <Award className="h-4 w-4 mr-1" /> Top Prizes
          </h4>
          <div className="space-y-1">
            {prizes.slice(0, 3).map((prize) => (
              <div key={prize.position} className="text-xs text-gray-600 flex justify-between">
                <span>{prize.position === 1 ? '🥇 1st' : prize.position === 2 ? '🥈 2nd' : '🥉 3rd'}</span>
                <span className="font-medium">{prize.reward}</span>
              </div>
            ))}
          </div>
        </div>
        
        {status === 'active' && userPosition && (
          <div className="mt-2 pt-2 border-t border-gray-100">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Your Position</span>
              <span className="font-medium">{userPosition}{userPosition === 1 ? 'st' : userPosition === 2 ? 'nd' : userPosition === 3 ? 'rd' : 'th'}</span>
            </div>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="pt-0">
        <Button className="w-full" asChild>
          <Link to={`/competition/${id}`}>View Details</Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export default CompetitionCard;
