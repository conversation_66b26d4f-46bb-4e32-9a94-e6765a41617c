// sanity/schemaTypes/faqItem.ts
import {Rule} from 'sanity'

export default {
  name: 'faqItem',
  title: 'FAQ Item',
  type: 'document',
  fields: [
    {
      name: 'question',
      title: 'Question',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'answer',
      title: 'Answer',
      type: 'array',
      of: [{type: 'block'}], // Rich text for the answer
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'category',
      title: 'Category',
      type: 'string',
      options: {
        list: ['general', 'account', 'payments', 'tasks', 'referrals', 'partner_program'],
      },
    },
    {
      name: 'sortOrder',
      title: 'Sort Order',
      type: 'number',
    },
    {
      name: 'tags',
      title: 'Keywords/Tags',
      type: 'array',
      of: [{type: 'string'}],
    },
  ],
}
