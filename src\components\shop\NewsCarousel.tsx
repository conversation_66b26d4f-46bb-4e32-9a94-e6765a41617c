
import { useRef, useEffect } from 'react';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BellRing } from 'lucide-react';
import { ProductNews } from '@/types/shop';
import { fetchProductNews } from '@/services/shopService';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';

export const NewsCarousel = () => {
  const carouselRef = useRef<HTMLDivElement>(null);
  
  const { data: newsItems, isLoading, error } = useQuery({
    queryKey: ['productNews'],
    queryFn: fetchProductNews
  });

  // Auto-scroll animation
  useEffect(() => {
    if (!newsItems?.length || !carouselRef.current) return;
    
    const scrollSpeed = 0.5; // pixels per millisecond
    let lastTime: number | null = null;
    let animationFrameId: number;
    
    const scroll = (timestamp: number) => {
      if (lastTime === null) {
        lastTime = timestamp;
      }
      
      const element = carouselRef.current;
      if (!element) return;
      
      const elapsed = timestamp - lastTime;
      element.scrollLeft += scrollSpeed * elapsed;
      
      // Reset scroll position when reached end to create continuous effect
      if (element.scrollLeft >= (element.scrollWidth - element.clientWidth)) {
        element.scrollLeft = 0;
      }
      
      lastTime = timestamp;
      animationFrameId = requestAnimationFrame(scroll);
    };
    
    // Pause animation when hovering
    const handleMouseEnter = () => {
      cancelAnimationFrame(animationFrameId);
      lastTime = null;
    };
    
    const handleMouseLeave = () => {
      animationFrameId = requestAnimationFrame(scroll);
    };
    
    carouselRef.current.addEventListener('mouseenter', handleMouseEnter);
    carouselRef.current.addEventListener('mouseleave', handleMouseLeave);
    
    animationFrameId = requestAnimationFrame(scroll);
    
    return () => {
      cancelAnimationFrame(animationFrameId);
      carouselRef.current?.removeEventListener('mouseenter', handleMouseEnter);
      carouselRef.current?.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [newsItems]);

  if (isLoading) {
    return (
      <div className="py-4">
        <div className="flex items-center space-x-2 mb-4">
          <BellRing className="text-earnhub-red" size={20} />
          <h2 className="text-xl font-bold">Important Announcements</h2>
        </div>
        <div className="h-16 bg-gray-200 rounded-lg animate-pulse"></div>
      </div>
    );
  }

  if (error || !newsItems?.length) {
    return null;
  }

  return (
    <div className="py-4">
      <div className="flex items-center space-x-2 mb-4">
        <BellRing className="text-earnhub-red" size={20} />
        <h2 className="text-xl font-bold">Important Announcements</h2>
      </div>
      
      <div 
        className="flex overflow-x-auto gap-4 pb-4 hide-scrollbar"
        ref={carouselRef}
      >
        {newsItems.map((news) => (
          <Card 
            key={news._id}
            className="flex-shrink-0 w-[300px] md:w-[400px] bg-gradient-to-r from-red-50 to-orange-50 border-earnhub-red/20"
          >
            <CardContent className="p-4 flex items-center gap-3">
              {news.image && (
                <img 
                  src={news.image} 
                  alt={news.title} 
                  className="w-12 h-12 object-cover rounded"
                />
              )}
              <div className="flex-1">
                <h3 className="font-medium text-earnhub-red">{news.title}</h3>
                <p className="text-sm text-gray-600">{news.description}</p>
                {news.discount && (
                  <Badge className="mt-1 bg-orange-500">{news.discount}% OFF</Badge>
                )}
              </div>
              {news.productId && (
                <Button size="sm" asChild>
                  <Link to={`/shop/product/${news.productId}`}>View</Link>
                </Button>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
