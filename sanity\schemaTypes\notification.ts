
export default {
  name: 'notification',
  title: 'Notifications',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Notification Title',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'message',
      title: 'Notification Message',
      type: 'text',
      validation: Rule => Rule.required()
    },
    {
      name: 'type',
      title: 'Notification Type',
      type: 'string',
      options: {
        list: [
          { title: 'System', value: 'system' },
          { title: 'Account', value: 'account' },
          { title: 'Competition', value: 'competition' },
          { title: 'Task', value: 'task' },
          { title: 'Promotion', value: 'promotion' },
          { title: 'Partner', value: 'partner' },
          { title: 'Recommendation', value: 'recommendation' }
        ]
      },
      validation: Rule => Rule.required()
    },
    {
      name: 'priority',
      title: 'Priority Level',
      type: 'string',
      options: {
        list: [
          { title: 'Low', value: 'low' },
          { title: 'Medium', value: 'medium' },
          { title: 'High', value: 'high' }
        ]
      },
      initialValue: 'medium'
    },
    {
      name: 'userId',
      title: 'User ID',
      type: 'string',
      description: 'User ID this notification is for (empty for global)'
    },
    {
      name: 'isRead',
      title: 'Read Status',
      type: 'boolean',
      initialValue: false
    },
    {
      name: 'actionUrl',
      title: 'Action URL',
      type: 'string',
      description: 'URL to navigate to when notification is clicked'
    },
    {
      name: 'icon',
      title: 'Icon',
      type: 'string',
      description: 'Icon name from Lucide icons'
    },
    {
      name: 'createdAt',
      title: 'Created At',
      type: 'datetime',
      initialValue: () => new Date().toISOString()
    },
    {
      name: 'expiresAt',
      title: 'Expires At',
      type: 'datetime'
    }
  ]
}
