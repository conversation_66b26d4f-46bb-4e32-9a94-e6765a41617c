
import { useNavigate } from "react-router-dom";
import { useQuery } from '@tanstack/react-query';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { fetchProductCategories } from '@/services/shopService';
import { CartDrawer } from '@/components/shop/CartDrawer';

const CategoriesPage = () => {
  const navigate = useNavigate();
  
  const { data: categories, isLoading } = useQuery({
    queryKey: ['productCategories'],
    queryFn: fetchProductCategories
  });

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-8">
      <div className="flex justify-between items-center mb-8">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => navigate('/shop')} className="mr-2">
            <ArrowLeft size={20} />
          </Button>
          <h1 className="text-2xl md:text-3xl font-bold">All Categories</h1>
        </div>
        <CartDrawer />
      </div>
      
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 animate-pulse">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="h-48 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {categories?.map((category) => (
            <Card 
              key={category._id}
              className="overflow-hidden hover:shadow-lg transition-all cursor-pointer hover:-translate-y-1"
              onClick={() => navigate(`/shop/category/${category._id}`)}
            >
              <div className="h-40 bg-gray-100">
                <img 
                  src={category.image || "/placeholder.svg"} 
                  alt={category.name} 
                  className="w-full h-full object-cover"
                />
              </div>
              <CardContent className="p-6">
                <h2 className="text-xl font-bold mb-2">{category.name}</h2>
                <p className="text-gray-600">{category.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default CategoriesPage;
