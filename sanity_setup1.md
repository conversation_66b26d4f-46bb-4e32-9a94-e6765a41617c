Sanity CMS Setup & Integration Guide for EarnHub
This guide provides step-by-step instructions for setting up Sanity CMS to manage dynamic content for your EarnHub application. This includes content like news updates, competitions, tasks, opportunities, achievements, and featured sections. This guide assumes your EarnHub project is built with React/TypeScript.

1.  Introduction to Sanity CMS for EarnHub
    Sanity.io is a headless CMS that offers a flexible and developer-friendly way to manage structured content. For EarnHub, Sanity will allow you to:

        Create and Manage Content: Easily update news, task details, competition information, etc., without needing to change the application code.
        Define Custom Content Models: Structure your content exactly how you need it (e.g., a "Task" content type with fields for title, description, reward, estimated time).
        Deliver Content via API: Fetch your content from Sanity and display it in your React frontend.
        Collaboration: Allow team members to manage content through the Sanity Studio (a customizable, open-source editing environment).

2.  Setting Up Your Sanity Project

    Install Sanity CLI:
    If you don_t have it already, install the Sanity CLI globally using npm or yarn.
    bash

npm install -g @sanity/cli

# or

yarn global add @sanity/cli

Initialize a New Sanity Project:

    Navigate to your main EarnHub project directory (or a preferred location for the Sanity Studio codebase, often a sub-directory like earnfinity-hub/sanity-studio or a separate repository).
    Run the Sanity initialization command:

bash

    sanity init

        Follow the Prompts:
            Log in or create a Sanity account: The CLI will guide you through this.
            Choose a project template: Select "Clean project with no predefined schemas" to build your schemas from scratch, which is recommended for a custom application like EarnHub.
            Output path: Confirm the directory name for your Sanity Studio (e.g., sanity-studio).
            Use TypeScript? Yes, since your main project uses it.
            Package manager: Choose npm or yarn.
    Project Structure:
        This will create a new folder (e.g., sanity-studio) containing your Sanity Studio configuration and schema definitions.
        Key files and folders:
            sanity.config.ts (or .js): Main configuration file for your studio.
            schemas/: This directory is where you will define your content models (document types, object types, etc.).

3. Defining Content Schemas (Content Models)
   Schemas define the structure of your content types. You will create a new file in the schemas/ directory for each content type.
   Example Schema: schemas/task.ts
   typescript

// schemas/task.ts
import {defineField, defineType} from 'sanity'

export default defineType({
name: 'task',
title: 'Task',
type: 'document',
fields: [
defineField({
name: 'title',
title: 'Title',
type: 'string',
validation: Rule => Rule.required(),
}),
defineField({
name: 'slug',
title: 'Slug',
type: 'slug',
options: {
source: 'title',
maxLength: 96,
},
validation: Rule => Rule.required(),
}),
defineField({
name: 'description',
title: 'Description',
type: 'text', // For longer text
}),
defineField({
name: 'shortDescription',
title: 'Short Description / Teaser',
type: 'string',
}),
defineField({
name: 'estimatedTime',
title: 'Estimated Time (e.g., 10 mins, 1 hour)',
type: 'string',
}),
defineField({
name: 'rewardAmountKES',
title: 'Reward Amount (KES)',
type: 'number',
}),
defineField({
name: 'rewardPoints',
title: 'Reward Points (EarnCoins)',
type: 'number',
}),
defineField({
name: 'category',
title: 'Category',
type: 'string',
options: {
list: [
{title: 'App Testing', value: 'app_testing'},
{title: 'Survey', value: 'survey'},
{title: 'Social Media', value: 'social_media'},
{title: 'Content Creation', value: 'content_creation'},
// Add more categories as needed
],
},
}),
defineField({
name: 'isActive',
title: 'Is Active / Available',
type: 'boolean',
initialValue: true,
}),
defineField({
name: 'premiumOnly',
title: 'Premium Only Task (Silver/Gold)',
type: 'boolean',
initialValue: false,
}),
defineField({
name: 'mainImage',
title: 'Main Image (Optional)',
type: 'image',
options: {
hotspot: true, // Enables image cropping
},
}),
// Add more fields as needed: difficulty, prerequisites, steps, etc.
],
preview: {
select: {
title: 'title',
media: 'mainImage',
isActive: 'isActive',
},
prepare(selection) {
const {title, media, isActive} = selection
return {
title: `${isActive ? '🟢' : '🔴'} ${title}`,
media: media,
}
},
},
})

Other Schema Examples (Create similar files in schemas/):

    newsUpdate.ts:
        Fields: title, slug, publicationDate, author (reference to a user/author type if needed), summary, body (block content for rich text), mainImage, category/tags.
    competition.ts:
        Fields: title, slug, startDate, endDate, description, rules (block content), prizeDetails (text or array of objects), maxParticipants, status (upcoming, active, past), entryFee, featuredImage.
    opportunity.ts:
        Fields: title, slug, type (job, program, sale), companyName (if applicable), location, description, requirements, rewardOrSalary, applicationLinkOrMethod, postedDate, closingDate.
    achievementSanity.ts (to distinguish from DB achievement):
        Fields: title, slug, description, icon (image), category.
    featuredSection.ts:
        Fields: title, linkTo (reference to task, competition, news, etc.), image, displayOrder.

Register Your Schemas:
Open sanity.config.ts (or .js) in your Sanity Studio project and import/add your schemas to the schema.types array:
typescript

// sanity.config.ts
import {defineConfig} from 'sanity'
import {structureTool} from 'sanity/structure'
import {visionTool} from '@sanity/vision'
// Import your schemas
import task from './schemas/task'
import newsUpdate from './schemas/newsUpdate'
import competition from './schemas/competition'
import opportunity from './schemas/opportunity'
import achievementSanity from './schemas/achievementSanity'
import featuredSection from './schemas/featuredSection'

// ... other imports if any

export default defineConfig({
name: 'earnhub_cms',
title: 'EarnHub CMS',

projectId: 'YOUR_PROJECT_ID', // Find this in your sanity.json or manage.sanity.io
dataset: 'production', // Or your chosen dataset name

plugins: [structureTool(), visionTool()],

schema: {
types: [
// Add your schemas here
task,
newsUpdate,
competition,
opportunity,
achievementSanity,
featuredSection,
// ... any other document types or objects
],
},
})

4. Running and Deploying Sanity Studio

   Run Studio Locally:
   Navigate to your Sanity Studio directory (e.g., cd sanity-studio).
   Run the development server:
   bash

sanity start

    This will typically open the Sanity Studio in your browser (usually at http://localhost:3333) . You can now create and edit content based on the schemas you defined.

Deploy Studio:

    To make the Studio accessible to your team online, deploy it:

bash

    sanity deploy

        Follow the prompts. Sanity will host your Studio at a URL like yourprojectname.sanity.studio.

5. Integrating Sanity with Your EarnHub Frontend (React)

   Install Sanity Client:
   In your main EarnHub React project (earnfinity-hub), install the Sanity client library:
   bash

npm install @sanity/client

# or

yarn add @sanity/client

Configure Sanity Client:

    Create a utility file to initialize and export your Sanity client.
    src/lib/sanityClient.ts (in your main EarnHub React project):

typescript

// src/lib/sanityClient.ts
import {createClient} from '@sanity/client'

export const sanityClient = createClient({
projectId: 'YOUR_PROJECT_ID', // Get this from manage.sanity.io or your sanity.config.ts
dataset: 'production', // Or your chosen dataset
useCdn: process.env.NODE_ENV === 'production', // `false` if you want to ensure fresh data
apiVersion: '2023-05-03', // Use a recent API version
// token: 'YOUR_SANITY_READ_TOKEN', // Only if you have private datasets and need to authenticate read requests
});

// Helper function for image URLs (optional but recommended)
import imageUrlBuilder from '@sanity/image-url';
const builder = imageUrlBuilder(sanityClient);
export function urlFor(source: any) {
return builder.image(source);
}

    Replace YOUR_PROJECT_ID with your actual Sanity project ID.

Fetching Data in Your React Components:

    Use GROQ (Sanity_s query language) to fetch content.
    Example: Fetching Tasks for TasksPage.tsx

typescript

// src/pages/TasksPage.tsx (Conceptual Example)
import React, { useEffect, useState } from 'react';
import { sanityClient, urlFor } from '@/lib/sanityClient'; // Adjust path as needed

interface SanityTask {
\_id: string;
title?: string;
shortDescription?: string;
estimatedTime?: string;
rewardAmountKES?: number;
mainImage?: any; // Sanity image asset
// ... other fields you defined
}

const TasksPage = () => {
const [tasks, setTasks] = useState<SanityTask[]>([]);
const [loading, setLoading] = useState(true);
const [error, setError] = useState<string | null>(null);

useEffect(() => {
const fetchTasks = async () => {
setLoading(true);
setError(null);
try {
const query = `*[_type == "task" && isActive == true] | order(_createdAt desc) {
          _id,
          title,
          slug,
          shortDescription,
          estimatedTime,
          rewardAmountKES,
          mainImage
        }`;
const sanityTasks = await sanityClient.fetch<SanityTask[]>(query);
setTasks(sanityTasks);
} catch (err) {
console.error("Failed to fetch tasks:", err);
setError('Failed to load tasks. Please try again later.');
}
setLoading(false);
};

    fetchTasks();

}, []);

if (loading) return <p>Loading tasks...</p>;
if (error) return <p className="text-red-500">{error}</p>;
if (!tasks.length) return <p>No tasks available at the moment. Check back soon!</p>;

return (

<div>
<h1>Available Tasks</h1>
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
{tasks.map((task) => (
<div key={task._id} className="border p-4 rounded-lg">
{task.mainImage && (
<img
src={urlFor(task.mainImage).width(300).height(200).url()}
alt={task.title || 'Task image'}
className="w-full h-40 object-cover rounded-md mb-2"
/>
)}
<h2>{task.title}</h2>
<p>{task.shortDescription}</p>
<p>Reward: KES {task.rewardAmountKES}</p>
<p>Time: {task.estimatedTime}</p>
{/_ Add a link to a detailed task page using task.slug.current _/}
</div>
))}
</div>
</div>
);
};

export default TasksPage;

Displaying Rich Text (Block Content):

    For fields like body in news articles (defined as type: 'blockContent' or array of block), you_ll need a component to render it.
    Install @portabletext/react:

bash

npm install @portabletext/react

# or

yarn add @portabletext/react

    Use it in your component:

typescript

    // Example for displaying rich text content
    import { PortableText } from '@portabletext/react';

    // const MyNewsArticleComponent = ({ bodyContent }) => {
    //   return <PortableText value={bodyContent} />;
    // };

    You can customize the rendering of different block types (headings, lists, custom blocks) by passing components to PortableText.

6. Best Practices & Next Steps

   GROQ Queries: Learn GROQ to efficiently fetch the exact data you need. Use the Vision tool in Sanity Studio (sanity start then navigate to Vision plugin) to test your queries.
   Image Optimization: Use Sanity_s image pipeline (via urlFor) to serve optimized images (resizing, cropping, format conversion).
   Data Validation: Use Sanity_s validation rules in your schemas to ensure content quality.
   Previews: Set up live content previews from your Sanity Studio to your frontend application for a better editorial experience.
   Webhooks: Use Sanity webhooks to trigger builds or other actions when content is updated (e.g., re-build a static site, clear a cache).
   Security: If your dataset is private, ensure you handle API tokens securely. For public datasets, read access is generally open.
   Iterate on Schemas: Your content needs might evolve. Sanity makes it relatively easy to update schemas (though consider data migration if changes are significant).

This guide provides a comprehensive starting point for integrating Sanity CMS with EarnHub. Refer to the official Sanity.io documentation for more in-depth information and advanced features.

Create a new document type
Create a new file in your Studio’s schemaTypes folder called postType.ts with the code below which contains a set of fields for a new post document type.
/studio-earnhub1/schemaTypes/postType.ts

import {defineField, defineType} from 'sanity'

export const postType = defineType({
name: 'post',
title: 'Post',
type: 'document',
fields: [
defineField({
name: 'title',
type: 'string',
validation: (rule) => rule.required(),
}),
defineField({
name: 'slug',
type: 'slug',
options: {source: 'title'},
validation: (rule) => rule.required(),
}),
defineField({
name: 'publishedAt',
type: 'datetime',
initialValue: () => new Date().toISOString(),
validation: (rule) => rule.required(),
}),
defineField({
name: 'image',
type: 'image',
}),
defineField({
name: 'body',
type: 'array',
of: [{type: 'block'}],
}),
],
})

2
Register the post schema type to the Studio schema
Now you can import this document type into the schemaTypes array in the index.ts file in the same folder.
/studio-earnhub1/schemaTypes/index.ts

import {postType} from './postType'

export const schemaTypes = [postType]

3
Publish your first document
When you save these two files, your Studio should automatically reload and show your first document type. Click the + symbol at the top left to create and publish a new post document.

Display content on the home page
React Router uses a loader function exported from routes for server-side fetching of data. Routes are configured in the app/routes.ts file.
The default home page can be found at app/routes/home.tsx
Update it to render a list of posts fetched from your Sanity dataset using the code below.
/react-router-earnhub1/app/routes/home.tsx

import { SanityDocument } from "@sanity/client";
import { Link } from "react-router";
import { client } from "~/sanity/client";
import { Route } from "./+types/home";

const POSTS_QUERY = `*[
  _type == "post"
  && defined(slug.current)
]|order(publishedAt desc)[0...12]{_id, title, slug, publishedAt}`;

export async function loader() {
return { posts: await client.fetch<SanityDocument[]>(POSTS_QUERY) };
}

export default function IndexPage({ loaderData }: Route.ComponentProps) {
const { posts } = loaderData;

return (

<main className="container mx-auto min-h-screen max-w-3xl p-8">
<h1 className="text-4xl font-bold mb-8">Posts</h1>
<ul className="flex flex-col gap-y-4">
{posts.map((post) => (
<li className="hover:underline" key={post._id}>
<Link to={`/${post.slug.current}`}>
<h2 className="text-xl font-semibold">{post.title}</h2>
<p>{new Date(post.publishedAt).toLocaleDateString()}</p>
</Link>
</li>
))}
</ul>
</main>
);
}

6
Display individual posts
Create a new route for individual post pages.
The dynamic value of a slug when visiting /:post in the URL is used as a parameter in the GROQ query used by Sanity Client.
Notice that we’re using Tailwind CSS Typography’s prose class name to style the post’s body block content. Install it in your project following their documentation.
Update the routes.ts configuration file to load this route when individual post links are clicked.
/react-router-earnhub1/app/routes/post.tsx

import { Link } from "react-router";
import imageUrlBuilder from "@sanity/image-url";
import { SanityDocument } from "@sanity/client";
import { SanityImageSource } from "@sanity/image-url/lib/types/types";
import { PortableText } from "@portabletext/react";
import { Route } from "../routes/+types/post";
import { client } from "~/sanity/client";

const { projectId, dataset } = client.config();
const urlFor = (source: SanityImageSource) =>
projectId && dataset
? imageUrlBuilder({ projectId, dataset }).image(source)
: null;

const POST_QUERY = `*[_type == "post" && slug.current == $slug][0]`;

export async function loader({ params }: Route.LoaderArgs) {
return { post: await client.fetch<SanityDocument>(POST_QUERY, params) };
}

export default function Component({ loaderData }: Route.ComponentProps) {
const { post } = loaderData;
const postImageUrl = post.image
? urlFor(post.image)?.width(550).height(310).url()
: null;

return (

<main className="container mx-auto min-h-screen max-w-3xl p-8 flex flex-col gap-4">
<Link to="/" className="hover:underline">
← Back to posts
</Link>
{postImageUrl && (
<img
          src={postImageUrl}
          alt={post.title}
          className="aspect-video rounded-xl"
          width="550"
          height="310"
        />
)}
<h1 className="text-4xl font-bold mb-8">{post.title}</h1>
<div className="prose">
<p>Published: {new Date(post.publishedAt).toLocaleDateString()}</p>
{Array.isArray(post.body) && <PortableText value={post.body} />}
</div>
</main>
);
}

/react-router-earnhub1/app/routes.ts

import { type RouteConfig, index, route } from "@react-router/dev/routes";

export default [
index("routes/home.tsx"),
route(":slug", "routes/post.tsx"),
] satisfies RouteConfig;
