I'll help you plan out the Sanity CMS schemas for your EarnHub project, which will work alongside your Nile DB setup. First, let me examine your current codebase to understand which components need Sanity CMS schemas based on your existing interfaces.

I'll help you plan out the Sanity CMS schemas for your EarnHub project, which will work alongside your Nile DB setup. First, let me examine your current codebase to understand which components need Sanity CMS schemas based on your existing interfaces.

Searched for
sanity|cms
Let me adjust my search to find the Sanity CMS-related components in your codebase:

Let me adjust my search to find the Sanity CMS-related components in your codebase:

Searched for
sanity|cms
Let me check the structure of your current types and components to better understand how they're using Sanity CMS:

Let me check the structure of your current types and components to better understand how they're using Sanity CMS:

Read
src/types/competitions.ts
Read
src/components/QuickAccessGrid.tsx
Let me examine a few more key files to understand how the components are structured and what schemas would be most appropriate:

Let me examine a few more key files to understand how the components are structured and what schemas would be most appropriate:

Read
src/pages/ProfilePage.tsx
Read
src/components/ProfileCompletion.tsx
Let me check the profile component structure and the components folder where the ProfileCompletion component might be located:

Let me check the profile component structure and the components folder where the ProfileCompletion component might be located:

Read
src/components/profile/ProfileCompletion.tsx
Read
src/components/onboarding/OnboardingModal.tsx
Let me look at one more component to understand the overall structure, specifically the OnboardingModal and SignUpOnboarding components:

Let me look at one more component to understand the overall structure, specifically the OnboardingModal and SignUpOnboarding components:

Read
src/components/SignUpOnboarding.tsx
Read
src/pages/RafflePage.tsx
Let me check a few more components to understand the overall structure of your application better:

Let me check a few more components to understand the overall structure of your application better:

Read
src/pages/AchievementsPage.tsx
Read
src/components/competitions/CompetitionCard.tsx
Now that I have a good understanding of your application and its components, I'll develop a comprehensive plan for creating Sanity CMS schemas that will integrate with your Nile DB setup. Let's look at how we can make these work together effectively:

Plan for Creating Sanity CMS Schemas with Nile DB Integration

1. Sanity CMS Schema Overview
   I'll create Sanity schemas for the following content types based on your application's needs:

User-related Content

Profile Avatars
Achievement Types
Skill Categories
Membership Tiers
Opportunity Content

Job Listings & Opportunities
Companies/Organizations
Engagement Content

Competitions
Quests & Tasks
Raffles
News & Updates
Live Activity Feed
Marketing Content

Testimonials
FAQ Items
Benefits/Features 2. Nile DB Schema Overview
In your Nile DB, we'll create the following tables for the tenant-based architecture:

Core User Data

users (extends Nile auth)
profiles
membership_subscriptions
Financial Data

wallets
transactions
payment_methods
Engagement Data

user_tasks
competition_entries
raffle_entries
achievement_progress
quest_progress
referrals 3. Integration Architecture
The integration between Sanity CMS and Nile DB will follow these principles:

CMS for Content, DB for User Data

Sanity: Stores all content, UI elements, and configuration
Nile DB: Stores all user data, transactions, and relationships
Reference IDs

User activities in Nile DB will reference Sanity content using IDs
Example: A user's task completion in Nile DB references a task defined in Sanity
Real-time Updates

User-specific content (like dashboard stats) will be pulled from Nile DB
Content structures and templates will come from Sanity 4. Schema Implementation Approach
For each component in your application:

Create a Sanity schema for content structure
Create corresponding Nile DB tables for user data
Implement API services that combine both data sources
I'll now outline detailed schemas for both Sanity CMS and Nile DB based on your application needs. Let's start with the key components:

Sanity CMS Schemas
These schemas will define the content structure for your application. The Sanity schemas will primarily focus on content that administrators manage, while user-specific data will be stored in Nile DB.

1. Core Content Types

// Achievement Schema
export default {
name: 'achievement',
title: 'Achievement',
type: 'document',
fields: [
{
name: 'title',
title: 'Title',
type: 'string',
validation: Rule => Rule.required()
},
{
name: 'description',
title: 'Description',
type: 'text',
validation: Rule => Rule.required()
},
{
name: 'category',
title: 'Category',
type: 'string',
options: {
list: [
{title: 'Tasks', value: 'tasks'},
{title: 'Referrals', value: 'referrals'},
{title: 'Competitions', value: 'competitions'},
{title: 'Profile', value: 'profile'},
{title: 'Earnings', value: 'earnings'}
]
}
},
{
name: 'difficulty',
title: 'Difficulty',
type: 'string',
options: {
list: [
{title: 'Easy', value: 'easy'},
{title: 'Medium', value: 'medium'},
{title: 'Hard', value: 'hard'},
{title: 'Expert', value: 'expert'}
]
}
},
{
name: 'points',
title: 'Points',
type: 'number',
validation: Rule => Rule.required().min(1)
},
{
name: 'reward',
title: 'Reward',
type: 'string'
},
{
name: 'icon',
title: 'Icon',
type: 'string'
},
{
name: 'criteriaType',
title: 'Criteria Type',
type: 'string',
options: {
list: [
{title: 'Task Completion', value: 'taskCompletion'},
{title: 'Referral Count', value: 'referralCount'},
{title: 'Profile Completion', value: 'profileCompletion'},
{title: 'Earnings Amount', value: 'earningsAmount'},
{title: 'Competition Participation', value: 'competitionParticipation'}
]
}
},
{
name: 'criteriaValue',
title: 'Criteria Value',
type: 'number',
description: 'The target value needed to complete the achievement'
}
]
}

// Competition Schema
export default {
name: 'competition',
title: 'Competition',
type: 'document',
fields: [
{
name: 'title',
title: 'Title',
type: 'string',
validation: Rule => Rule.required()
},
{
name: 'slug',
title: 'Slug',
type: 'slug',
options: {
source: 'title',
maxLength: 96
}
},
{
name: 'description',
title: 'Description',
type: 'text'
},
{
name: 'type',
title: 'Type',
type: 'string',
options: {
list: [
{title: 'Referral', value: 'referral'},
{title: 'Sales', value: 'sales'},
{title: 'Task', value: 'task'}
]
}
},
{
name: 'mainImage',
title: 'Main Image',
type: 'image',
options: {
hotspot: true
}
},
{
name: 'startDate',
title: 'Start Date',
type: 'datetime'
},
{
name: 'endDate',
title: 'End Date',
type: 'datetime'
},
{
name: 'status',
title: 'Status',
type: 'string',
options: {
list: [
{title: 'Active', value: 'active'},
{title: 'Upcoming', value: 'upcoming'},
{title: 'Past', value: 'past'}
]
}
},
{
name: 'prizes',
title: 'Prizes',
type: 'array',
of: [
{
type: 'object',
fields: [
{
name: 'position',
title: 'Position',
type: 'number'
},
{
name: 'reward',
title: 'Reward',
type: 'string'
}
]
}
]
},
{
name: 'rules',
title: 'Rules',
type: 'array',
of: [{type: 'string'}]
}
]
}

// Opportunity Schema
export default {
name: 'opportunity',
title: 'Opportunity',
type: 'document',
fields: [
{
name: 'title',
title: 'Title',
type: 'string',
validation: Rule => Rule.required()
},
{
name: 'slug',
title: 'Slug',
type: 'slug',
options: {
source: 'title',
maxLength: 96
}
},
{
name: 'description',
title: 'Description',
type: 'text'
},
{
name: 'type',
title: 'Type',
type: 'string',
options: {
list: [
{title: 'Job', value: 'job'},
{title: 'Program', value: 'program'},
{title: 'Sales', value: 'sales'}
]
}
},
{
name: 'category',
title: 'Category',
type: 'string'
},
{
name: 'company',
title: 'Company',
type: 'string'
},
{
name: 'reward',
title: 'Reward',
type: 'string'
},
{
name: 'deadline',
title: 'Deadline',
type: 'date'
},
{
name: 'requirements',
title: 'Requirements',
type: 'array',
of: [{type: 'string'}]
},
{
name: 'applicationLink',
title: 'Application Link',
type: 'url'
},
{
name: 'status',
title: 'Status',
type: 'string',
options: {
list: [
{title: 'Open', value: 'open'},
{title: 'Limited', value: 'limited'},
{title: 'Closing Soon', value: 'closing-soon'},
{title: 'Closed', value: 'closed'}
]
}
},
{
name: 'postedDate',
title: 'Posted Date',
type: 'date'
},
{
name: 'featured',
title: 'Featured',
type: 'boolean',
description: 'Whether this opportunity should be featured on the platform'
}
]
}

// Raffle Schema
export default {
name: 'raffle',
title: 'Raffle',
type: 'document',
fields: [
{
name: 'title',
title: 'Title',
type: 'string',
validation: Rule => Rule.required()
},
{
name: 'description',
title: 'Description',
type: 'text'
},
{
name: 'type',
title: 'Type',
type: 'string',
options: {
list: [
{title: 'Daily', value: 'daily'},
{title: 'Weekly', value: 'weekly'},
{title: 'Monthly', value: 'monthly'}
]
}
},
{
name: 'prize',
title: 'Prize',
type: 'string'
},
{
name: 'prizeAmount',
title: 'Prize Amount',
type: 'number'
},
{
name: 'currency',
title: 'Currency',
type: 'string',
initialValue: 'KES'
},
{
name: 'startTime',
title: 'Start Time',
type: 'datetime'
},
{
name: 'endTime',
title: 'End Time',
type: 'datetime'
},
{
name: 'status',
title: 'Status',
type: 'string',
options: {
list: [
{title: 'Active', value: 'active'},
{title: 'Completed', value: 'completed'}
]
}
},
{
name: 'minimumEntryRequirements',
title: 'Minimum Entry Requirements',
type: 'text',
description: 'Any requirements for users to enter this raffle'
}
]
}

// Quick Access Links Schema
export default {
name: 'quickAccessItem',
title: 'Quick Access Item',
type: 'document',
fields: [
{
name: 'name',
title: 'Name',
type: 'string'
},
{
name: 'icon',
title: 'Icon',
type: 'string',
options: {
list: [
{title: 'Zap', value: 'Zap'},
{title: 'Briefcase', value: 'Briefcase'},
{title: 'Award', value: 'Award'},
{title: 'User', value: 'User'},
{title: 'Medal', value: 'Medal'},
{title: 'Users', value: 'User'},
{title: 'Ticket', value: 'Ticket'},
{title: 'ShoppingCart', value: 'ShoppingCart'},
{title: 'Wallet', value: 'Wallet'}
]
}
},
{
name: 'path',
title: 'Path',
type: 'string'
},
{
name: 'order',
title: 'Display Order',
type: 'number'
}
]
} 2. Membership & Ambassador Program Content

// Membership Tier Schema
export default {
name: 'membershipTier',
title: 'Membership Tier',
type: 'document',
fields: [
{
name: 'name',
title: 'Name',
type: 'string',
validation: Rule => Rule.required()
},
{
name: 'description',
title: 'Description',
type: 'text'
},
{
name: 'price',
title: 'Price',
type: 'number'
},
{
name: 'billingFrequency',
title: 'Billing Frequency',
type: 'string',
options: {
list: [
{title: 'Monthly', value: 'monthly'},
{title: 'Quarterly', value: 'quarterly'},
{title: 'Yearly', value: 'yearly'}
]
}
},
{
name: 'features',
title: 'Features',
type: 'array',
of: [
{
type: 'object',
fields: [
{
name: 'feature',
title: 'Feature',
type: 'string'
},
{
name: 'included',
title: 'Included',
type: 'boolean'
}
]
}
]
},
{
name: 'tier',
title: 'Tier Level',
type: 'number',
description: 'Numerical representation of tier level (higher is better)'
},
{
name: 'color',
title: 'Color',
type: 'string',
description: 'Color code for this membership tier'
}
]
}

// Ambassador Benefit Schema
export default {
name: 'ambassadorBenefit',
title: 'Ambassador Benefit',
type: 'document',
fields: [
{
name: 'title',
title: 'Title',
type: 'string',
validation: Rule => Rule.required()
},
{
name: 'description',
title: 'Description',
type: 'text'
},
{
name: 'icon',
title: 'Icon',
type: 'string'
},
{
name: 'order',
title: 'Display Order',
type: 'number'
}
]
}
Nile DB Table Schemas
These would be created in your Nile DB to store user-specific data with tenant isolation:

-- Users table (extends Nile's built-in users)
CREATE TABLE users (
id UUID PRIMARY KEY REFERENCES auth.users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
full_name VARCHAR(255),
username VARCHAR(50) UNIQUE,
created_at TIMESTAMPTZ DEFAULT NOW(),
updated_at TIMESTAMPTZ DEFAULT NOW(),
account_status VARCHAR(20) DEFAULT 'active',
membership_tier VARCHAR(50) DEFAULT 'basic',
total_points INTEGER DEFAULT 0,
is_ambassador BOOLEAN DEFAULT FALSE
);

-- Profile details table
CREATE TABLE profiles (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
gender VARCHAR(20),
age_range VARCHAR(20),
county VARCHAR(100),
income_level VARCHAR(50),
education_level VARCHAR(50),
social_media TEXT,
availability VARCHAR(50),
avatar_id INTEGER,
created_at TIMESTAMPTZ DEFAULT NOW(),
updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User skills table
CREATE TABLE user_skills (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
skill VARCHAR(100) NOT NULL,
created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User interests table
CREATE TABLE user_interests (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
interest VARCHAR(100) NOT NULL,
created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User devices table
CREATE TABLE user_devices (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
device_type VARCHAR(50) NOT NULL,
created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Wallet table
CREATE TABLE wallets (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
balance DECIMAL(10, 2) DEFAULT 0,
currency VARCHAR(3) DEFAULT 'KES',
created_at TIMESTAMPTZ DEFAULT NOW(),
updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Transactions table
CREATE TABLE transactions (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
wallet_id UUID NOT NULL REFERENCES wallets(id),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
amount DECIMAL(10, 2) NOT NULL,
type VARCHAR(20) NOT NULL, -- deposit, withdrawal, earning, referral
status VARCHAR(20) DEFAULT 'completed',
reference VARCHAR(100),
description TEXT,
created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User achievements progress
CREATE TABLE achievement_progress (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
achievement_id VARCHAR(100) NOT NULL, -- Sanity CMS ID
progress INTEGER DEFAULT 0,
completed BOOLEAN DEFAULT FALSE,
completed_at TIMESTAMPTZ,
created_at TIMESTAMPTZ DEFAULT NOW(),
updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Competition entries
CREATE TABLE competition_entries (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
competition_id VARCHAR(100) NOT NULL, -- Sanity CMS ID
current_position INTEGER,
score INTEGER DEFAULT 0,
joined_at TIMESTAMPTZ DEFAULT NOW(),
updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Raffle entries
CREATE TABLE raffle_entries (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
raffle_id VARCHAR(100) NOT NULL, -- Sanity CMS ID
entry_time TIMESTAMPTZ DEFAULT NOW(),
is_winner BOOLEAN DEFAULT FALSE
);

-- Referrals tracking
CREATE TABLE referrals (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
referrer_id UUID NOT NULL REFERENCES users(id),
referee_id UUID REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
referral_code VARCHAR(50),
status VARCHAR(20) DEFAULT 'pending', -- pending, active, rewarded
reward_amount DECIMAL(10, 2),
created_at TIMESTAMPTZ DEFAULT NOW(),
completed_at TIMESTAMPTZ
);

-- Opportunity applications
CREATE TABLE opportunity_applications (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
opportunity_id VARCHAR(100) NOT NULL, -- Sanity CMS ID
status VARCHAR(20) DEFAULT 'submitted', -- submitted, reviewing, accepted, rejected
application_date TIMESTAMPTZ DEFAULT NOW(),
notes TEXT
);

-- Ambassador applications
CREATE TABLE ambassador_applications (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
application_text TEXT,
status VARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected
applied_at TIMESTAMPTZ DEFAULT NOW(),
reviewed_at TIMESTAMPTZ,
reviewer_notes TEXT
);

-- Membership subscriptions
CREATE TABLE membership_subscriptions (
id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
user_id UUID NOT NULL REFERENCES users(id),
tenant_id UUID NOT NULL REFERENCES tenants(id),
tier_id VARCHAR(100) NOT NULL, -- Sanity CMS ID
status VARCHAR(20) DEFAULT 'active', -- active, cancelled, expired
start_date TIMESTAMPTZ DEFAULT NOW(),
end_date TIMESTAMPTZ,
payment_frequency VARCHAR(20), -- monthly, quarterly, yearly
amount DECIMAL(10, 2),
auto_renew BOOLEAN DEFAULT TRUE
);
Integration Architecture
The integration between Sanity CMS and Nile DB would work like this:

Content Management in Sanity:

Admin defines achievements, competitions, raffles, etc. in Sanity
Images and rich content stored in Sanity's CDN
User Data in Nile DB:

User profiles, progress, and transactions in Nile DB
References to Sanity content using IDs
API Layer:

Create API endpoints that combine content from Sanity with user-specific data from Nile DB
Implement security with tenant isolation using Nile's features
React Components:

Components query both data sources as needed
User-specific components like dashboard pull from Nile DB
Content components pull from Sanity CMS
This dual-database approach gives you the benefits of both systems:

Sanity's excellent content management capabilities
Nile's powerful multitenancy and security features
