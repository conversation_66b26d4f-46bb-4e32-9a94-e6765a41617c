
import React, { useState } from 'react';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { 
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import { CompetitionType } from '@/types/competitions';
import { Trophy } from 'lucide-react';

interface LeaderboardEntry {
  userId: string;
  username: string;
  avatar: string | null;
  rank: number;
  score: number;
  isCurrentUser: boolean;
}

interface CompetitionLeaderboardProps {
  leaderboard: LeaderboardEntry[];
  competition: CompetitionType;
}

const CompetitionLeaderboard: React.FC<CompetitionLeaderboardProps> = ({ leaderboard, competition }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  
  // Calculate paginated data
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = leaderboard.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(leaderboard.length / itemsPerPage);
  
  // Find user's entry
  const userEntry = leaderboard.find(entry => entry.isCurrentUser);
  
  return (
    <div className="space-y-6">
      {competition.status === 'active' && userEntry && (
        <div className="bg-gray-50 p-6 rounded-lg mb-4">
          <h3 className="text-lg font-semibold mb-4">Your Performance</h3>
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
              {userEntry.avatar ? (
                <img 
                  src={userEntry.avatar} 
                  alt={userEntry.username} 
                  className="w-full h-full rounded-full"
                />
              ) : (
                <span>{userEntry.username.substring(0, 2).toUpperCase()}</span>
              )}
            </div>
            <div>
              <p className="font-medium">{userEntry.username}</p>
              <div className="flex space-x-4 text-sm">
                <span className="text-gray-600">Rank: <span className="font-medium">{userEntry.rank}</span></span>
                <span className="text-gray-600">Score: <span className="font-medium">{userEntry.score}</span></span>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <div className="bg-white rounded-lg overflow-hidden border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-16">Rank</TableHead>
              <TableHead>Competitor</TableHead>
              <TableHead className="text-right">Score</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentItems.map((entry) => (
              <TableRow 
                key={entry.userId} 
                className={entry.isCurrentUser ? "bg-amber-50" : ""}
              >
                <TableCell className="font-medium">
                  {entry.rank <= 3 ? (
                    <div className="flex items-center">
                      {entry.rank === 1 ? (
                        <span className="text-xl mr-1">🥇</span>
                      ) : entry.rank === 2 ? (
                        <span className="text-xl mr-1">🥈</span>
                      ) : (
                        <span className="text-xl mr-1">🥉</span>
                      )}
                      {entry.rank}
                    </div>
                  ) : (
                    entry.rank
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                      {entry.avatar ? (
                        <img 
                          src={entry.avatar} 
                          alt={entry.username} 
                          className="w-full h-full rounded-full"
                        />
                      ) : (
                        <span className="text-xs">{entry.username.substring(0, 2).toUpperCase()}</span>
                      )}
                    </div>
                    <span className={entry.isCurrentUser ? "font-medium" : ""}>{entry.username}</span>
                    {entry.isCurrentUser && <span className="ml-2 text-xs bg-amber-100 text-amber-800 px-2 py-0.5 rounded">You</span>}
                  </div>
                </TableCell>
                <TableCell className="text-right font-medium">{entry.score} pts</TableCell>
              </TableRow>
            ))}
            
            {currentItems.length === 0 && (
              <TableRow>
                <TableCell colSpan={3} className="text-center py-6">
                  <div className="flex flex-col items-center justify-center">
                    <Trophy className="h-8 w-8 text-gray-300 mb-2" />
                    <p className="text-gray-500">No leaderboard data available</p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        
        {totalPages > 1 && (
          <div className="py-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious 
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>
                
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Logic to show 5 page numbers centered around current page
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <PaginationItem key={pageNum}>
                      <PaginationLink 
                        onClick={() => setCurrentPage(pageNum)}
                        isActive={currentPage === pageNum}
                      >
                        {pageNum}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}
                
                <PaginationItem>
                  <PaginationNext 
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>
    </div>
  );
};

export default CompetitionLeaderboard;
