// sanity/schemaTypes/testimonial.ts
import {Rule} from 'sanity'

export default {
  name: 'testimonial',
  title: 'Testimonial',
  type: 'document',
  fields: [
    {
      name: 'userName',
      title: 'User Name',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'userTitle',
      title: 'User Title/Role',
      type: 'string',
      description: "e.g., 'University Student', 'Freelancer'",
    },
    {
      name: 'testimonialText',
      title: 'Testimonial Text',
      type: 'text',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'rating',
      title: 'Rating (1-5)',
      type: 'number',
      validation: (Rule: Rule) => Rule.min(1).max(5),
    },
    {
      name: 'userAvatarUrl',
      title: 'User Avatar URL (Optional)',
      type: 'url',
    },
    {
      name: 'dateReceived',
      title: 'Date Received',
      type: 'date',
      initialValue: () => new Date().toISOString().split('T')[0],
    },
    {
      name: 'isFeatured',
      title: 'Featured Testimonial',
      type: 'boolean',
      initialValue: false,
    },
  ],
}
