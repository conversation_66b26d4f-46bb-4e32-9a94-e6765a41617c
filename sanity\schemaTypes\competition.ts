// sanity/schemaTypes/competition.ts
import {Rule} from 'sanity'

export default {
  name: 'competition',
  title: 'Competition',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
    },
    {
      name: 'type',
      title: 'Type',
      type: 'string',
      options: {
        list: ['referral', 'sales', 'task'],
      },
    },
    {
      name: 'mainImage',
      title: 'Main Image',
      type: 'image',
      options: {
        hotspot: true,
      },
    },
    {
      name: 'startDate',
      title: 'Start Date',
      type: 'datetime',
    },
    {
      name: 'endDate',
      title: 'End Date',
      type: 'datetime',
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: ['active', 'upcoming', 'past'],
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'prizes',
      title: 'Prizes',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {name: 'position', type: 'number', title: 'Position'},
            {name: 'reward', type: 'string', title: 'Reward'},
          ],
        },
      ],
    },
    {
      name: 'rules',
      title: 'Rules',
      type: 'array',
      of: [{type: 'string'}],
    },
    {
      name: 'entryFee',
      title: 'Entry Fee (KES)',
      type: 'number',
      initialValue: 0,
    },
    {
      name: 'maxParticipants',
      title: 'Max Participants',
      type: 'number',
    },
    {
      name: 'minParticipants',
      title: 'Min Participants to Start',
      type: 'number',
    },
    {
      name: 'visibility',
      title: 'Visibility',
      type: 'string',
      options: {
        list: ['public', 'private', 'invite-only'],
      },
      initialValue: 'public',
    },
  ],
}
