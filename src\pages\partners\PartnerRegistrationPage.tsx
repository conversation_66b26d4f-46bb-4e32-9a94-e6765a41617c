
import React, { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

const formSchema = z.object({
  businessName: z.string().min(2, { message: "Business name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z.string().min(10, { message: "Phone number is required" }),
  website: z.string().optional(),
  socialMedia: z.string().optional(),
  description: z.string().min(10, { message: "Please provide a brief description of your business" }),
  marketingChannel: z.string().min(2, { message: "Please specify how you plan to promote our products" }),
  termsAgreed: z.literal(true, {
    errorMap: () => ({ message: "You must accept the terms and conditions" }),
  }),
  privacyAgreed: z.literal(true, {
    errorMap: () => ({ message: "You must accept the privacy policy" }),
  }),
});

type FormValues = z.infer<typeof formSchema>;

const PartnerRegistrationPage = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  
  // Default form values
  const defaultValues: FormValues = {
    businessName: "",
    email: "",
    phone: "",
    website: "",
    socialMedia: "",
    description: "",
    marketingChannel: "",
    termsAgreed: true, // Fixed the issue here - changed false to true
    privacyAgreed: true, // Fixed the issue here - changed false to true
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      console.log('Form submitted:', data);
      toast({
        title: "Application submitted!",
        description: "Your partner application has been received. We'll review it shortly.",
      });
      setIsSubmitting(false);
      navigate('/partners/dashboard');
    }, 1500);
  };

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-2">Partner Program Registration</h1>
        <p className="text-gray-600 mb-8">Join our partner program and start earning commissions on sales you refer.</p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Basic Partner</CardTitle>
              <CardDescription>15% Commission</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <p>✓ Basic marketing materials</p>
              <p>✓ Standard support</p>
              <p>✓ Monthly payments</p>
            </CardContent>
          </Card>
          
          <Card className="border-earnhub-red">
            <CardHeader className="bg-earnhub-red text-white">
              <CardTitle>Silver Partner</CardTitle>
              <CardDescription className="text-white/90">25% Commission</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <p>✓ All Basic features</p>
              <p>✓ Priority support</p>
              <p>✓ Custom landing pages</p>
              <p>✓ Bi-weekly payments</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Gold Partner</CardTitle>
              <CardDescription>40% Commission</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <p>✓ All Silver features</p>
              <p>✓ Dedicated account manager</p>
              <p>✓ Weekly payments</p>
              <p>✓ Special promotions</p>
            </CardContent>
          </Card>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Partner Application</CardTitle>
            <CardDescription>
              Please fill out the form below to apply for our partner program. All fields marked with * are required.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Business Information</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="businessName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Business Name *</FormLabel>
                          <FormControl>
                            <Input placeholder="Your business name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Business Email *</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number *</FormLabel>
                          <FormControl>
                            <Input placeholder="+****************" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="website"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Website</FormLabel>
                          <FormControl>
                            <Input placeholder="https://www.example.com" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="socialMedia"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Social Media Profiles</FormLabel>
                        <FormControl>
                          <Input placeholder="Instagram: @example, Facebook: /example, etc." {...field} />
                        </FormControl>
                        <FormDescription>
                          List your social media handles where you plan to promote our products
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Separator />
                  
                  <h3 className="text-lg font-medium">Partnership Information</h3>
                  
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Business Description *</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="Please describe your business and how you plan to benefit from our partner program." 
                            rows={4} 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="marketingChannel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Marketing Channels *</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="How do you plan to promote our products? (e.g. website, social media, email marketing, etc.)" 
                            rows={3} 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Separator />
                  
                  <Alert>
                    <AlertDescription>
                      By submitting this application, you agree to our Partner Program Terms and Conditions. 
                      We'll review your application and get back to you within 2 business days.
                    </AlertDescription>
                  </Alert>
                  
                  <FormField
                    control={form.control}
                    name="termsAgreed"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 py-2">
                        <FormControl>
                          <Checkbox 
                            checked={field.value} 
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            I agree to the Partner Program <a href="#" className="text-earnhub-red underline">Terms and Conditions</a> *
                          </FormLabel>
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="privacyAgreed"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 py-2">
                        <FormControl>
                          <Checkbox 
                            checked={field.value} 
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            I agree to the <a href="#" className="text-earnhub-red underline">Privacy Policy</a> *
                          </FormLabel>
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
                
                <Button type="submit" className="w-full" disabled={isSubmitting}>
                  {isSubmitting ? "Submitting..." : "Apply to Partner Program"}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PartnerRegistrationPage;
