
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Timer, Clock } from 'lucide-react';

const LimitedTimeOffer = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 3,
    hours: 11,
    minutes: 59,
    seconds: 59
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 };
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 };
        } else if (prev.hours > 0) {
          return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 };
        } else if (prev.days > 0) {
          return { ...prev, days: prev.days - 1, hours: 23, minutes: 59, seconds: 59 };
        }
        return prev;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <section className="py-16 px-6 md:px-10 bg-earnhub-dark relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 right-0 w-[300px] h-[300px] bg-earnhub-red/20 rounded-full blur-3xl"></div>
      </div>
      
      <div className="max-w-7xl mx-auto relative z-10">
        <div className="bg-white/10 backdrop-blur-md border border-white/10 rounded-2xl p-8 md:p-12">
          <div className="flex flex-col md:flex-row items-center gap-8 md:gap-12">
            <div className="flex-1">
              <div className="inline-block px-4 py-1 rounded-full bg-earnhub-red mb-4">
                <p className="text-white font-medium text-sm flex items-center gap-2">
                  <Timer size={16} className="animate-pulse" />
                  Limited Time Offer
                </p>
              </div>
              
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Get Silver for <span className="text-earnhub-red">50% OFF</span> + KES 1,000 Bonus
              </h2>
              
              <p className="text-white/80 text-lg mb-6">
                Unlock <span className="font-bold text-white">50% commission</span> on referrals, premium tasks worth KES 200+ each, and entry to exclusive competitions! This is our lowest price ever for Silver membership.
              </p>
              
              <div className="flex gap-4">
                <Button size="lg" className="bg-earnhub-red hover:bg-earnhub-red/90 text-white font-medium">
                  Claim This Deal Now
                </Button>
                <Button size="lg" variant="ghost" className="text-white border border-white/20 hover:bg-white/10">
                  Learn More
                </Button>
              </div>
            </div>
            
            <div className="md:w-1/3 w-full">
              <div className="bg-black/30 backdrop-blur-sm rounded-xl p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Clock size={20} className="text-earnhub-red" />
                  <p className="text-white font-medium">This offer expires in:</p>
                </div>
                
                <div className="grid grid-cols-4 gap-2">
                  {Object.entries(timeLeft).map(([key, value], index) => (
                    <div key={index} className="bg-black/20 rounded-lg p-3 text-center">
                      <p className="text-2xl md:text-3xl font-bold text-white">{value}</p>
                      <p className="text-white/70 text-xs uppercase">{key}</p>
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 text-center text-white/60 text-sm">
                  <p>Only <span className="text-earnhub-red font-bold">27 spots</span> left at this price!</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LimitedTimeOffer;
