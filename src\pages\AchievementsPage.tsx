
import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Award, Lock, Check, ArrowRight } from "lucide-react";
import anime from "animejs";

// Achievement data structure (compatible with Sanity CMS)
interface Achievement {
  _id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard' | 'expert';
  points: number;
  progress?: number;  // 0-100
  completed?: boolean;
  unlocked?: boolean;
  icon?: string;  // For custom icons in Sanity
  reward?: string;
}

// Mock achievements that would be fetched from Sanity CMS
const mockAchievements: Achievement[] = [
  // Completed achievements
  {
    _id: '1',
    title: 'First Steps',
    description: 'Complete your first task on EarnHub',
    category: 'tasks',
    difficulty: 'easy',
    points: 10,
    progress: 100,
    completed: true,
    unlocked: true,
    reward: '50 EarnHub Points'
  },
  {
    _id: '2',
    title: 'Social Butterfly',
    description: 'Connect your social media accounts',
    category: 'profile',
    difficulty: 'easy',
    points: 15,
    progress: 100,
    completed: true,
    unlocked: true,
    reward: '100 EarnHub Points'
  },
  {
    _id: '3',
    title: 'Refer a Friend',
    description: 'Successfully refer your first friend to EarnHub',
    category: 'referrals',
    difficulty: 'easy',
    points: 25,
    progress: 100,
    completed: true,
    unlocked: true,
    reward: '200 EarnHub Points + KES 50'
  },
  
  // In progress achievements
  {
    _id: '4',
    title: 'Task Master',
    description: 'Complete 10 tasks on EarnHub',
    category: 'tasks',
    difficulty: 'medium',
    points: 50,
    progress: 70,
    completed: false,
    unlocked: true,
    reward: '500 EarnHub Points'
  },
  {
    _id: '5',
    title: 'Competition Enthusiast',
    description: 'Participate in 5 different competitions',
    category: 'competitions',
    difficulty: 'medium',
    points: 75,
    progress: 40,
    completed: false,
    unlocked: true,
    reward: '750 EarnHub Points + Badge'
  },
  {
    _id: '6',
    title: 'Referral Network',
    description: 'Refer 5 active friends to EarnHub',
    category: 'referrals',
    difficulty: 'medium',
    points: 100,
    progress: 20,
    completed: false,
    unlocked: true,
    reward: '1,000 EarnHub Points + KES 250'
  },
  
  // Locked achievements
  {
    _id: '7',
    title: 'Elite Earner',
    description: 'Earn KES 10,000 through EarnHub',
    category: 'earnings',
    difficulty: 'hard',
    points: 200,
    progress: 0,
    completed: false,
    unlocked: false,
    reward: '2,000 EarnHub Points + Elite Badge'
  },
  {
    _id: '8',
    title: 'Community Leader',
    description: 'Refer 25 active members and become an Ambassador',
    category: 'referrals',
    difficulty: 'expert',
    points: 500,
    progress: 0,
    completed: false,
    unlocked: false,
    reward: '5,000 EarnHub Points + Ambassador Status'
  }
];

// Achievement categories with icons
const achievementCategories = [
  { id: 'all', name: 'All Achievements' },
  { id: 'tasks', name: 'Tasks' },
  { id: 'referrals', name: 'Referrals' },
  { id: 'competitions', name: 'Competitions' },
  { id: 'profile', name: 'Profile' },
  { id: 'earnings', name: 'Earnings' }
];

const AchievementsPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [totalPoints, setTotalPoints] = useState(0);
  const [completedCount, setCompletedCount] = useState(0);
  
  useEffect(() => {
    // Calculate total earned points and completed achievements
    const completed = mockAchievements.filter(a => a.completed);
    setCompletedCount(completed.length);
    const points = completed.reduce((sum, achievement) => sum + achievement.points, 0);
    setTotalPoints(points);
    
    // Animation for achievement cards
    anime({
      targets: '.achievement-card',
      opacity: [0, 1],
      translateY: [15, 0],
      delay: anime.stagger(100),
      easing: 'easeOutExpo',
      duration: 800
    });
    
    // Special animation for completed achievements
    anime({
      targets: '.completed-badge',
      scale: [0.5, 1],
      opacity: [0, 1],
      duration: 800,
      delay: anime.stagger(200)
    });
    
    // Animate the trophy icon
    anime({
      targets: '.trophy-icon',
      rotate: [-5, 5],
      duration: 2000,
      direction: 'alternate',
      loop: true,
      easing: 'easeInOutSine'
    });
    
    // Animate progress bars
    anime({
      targets: '.progress-active',
      width: (el: HTMLElement) => {
        const value = el.dataset.progress;
        return value ? `${value}%` : '0%';
      },
      easing: 'easeInOutQuad',
      duration: 1000,
      delay: anime.stagger(150)
    });
  }, []);
  
  // Filter achievements by category and status
  const filterAchievements = (status: 'completed' | 'in-progress' | 'locked') => {
    return mockAchievements.filter(achievement => {
      const categoryMatch = selectedCategory === 'all' || achievement.category === selectedCategory;
      const statusMatch = 
        (status === 'completed' && achievement.completed) ||
        (status === 'in-progress' && !achievement.completed && achievement.unlocked) ||
        (status === 'locked' && !achievement.unlocked);
      
      return categoryMatch && statusMatch;
    });
  };

  // Get the counts for each tab
  const completedAchievements = filterAchievements('completed');
  const inProgressAchievements = filterAchievements('in-progress');
  const lockedAchievements = filterAchievements('locked');
  
  return (
    <div className="container mx-auto px-4 py-6 pb-24 md:pb-6">
      <div className="mb-8 text-center">
        <div className="inline-block p-4 rounded-full bg-earnhub-red/10 mb-4">
          <Award size={32} className="text-earnhub-red trophy-icon" />
        </div>
        <h1 className="text-2xl md:text-3xl font-bold mb-2">Achievements</h1>
        <p className="text-gray-600 max-w-lg mx-auto">
          Complete tasks, refer friends, and participate in activities to earn achievements and rewards.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="animate-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Total Points Earned</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-earnhub-red">{totalPoints}</div>
          </CardContent>
        </Card>
        
        <Card className="animate-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Achievements Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-earnhub-red">{completedCount} / {mockAchievements.length}</div>
          </CardContent>
        </Card>
        
        <Card className="animate-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Current Level</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-earnhub-red">Silver</div>
            <p className="text-xs text-gray-500 mt-1">250 more points to Gold</p>
          </CardContent>
        </Card>
      </div>
      
      <div className="mb-6">
        <h2 className="text-lg font-medium mb-3">Filter by Category</h2>
        <div className="flex gap-2 overflow-x-auto pb-2">
          {achievementCategories.map(category => (
            <Badge
              key={category.id}
              variant={category.id === selectedCategory ? "default" : "outline"}
              className="cursor-pointer px-4 py-2"
              onClick={() => setSelectedCategory(category.id)}
            >
              {category.name}
            </Badge>
          ))}
        </div>
      </div>
      
      <Tabs defaultValue="in-progress" className="mb-8">
        <TabsList className="mb-6 w-full grid grid-cols-3">
          <TabsTrigger value="in-progress">
            In Progress ({inProgressAchievements.length})
          </TabsTrigger>
          <TabsTrigger value="completed">
            Completed ({completedAchievements.length})
          </TabsTrigger>
          <TabsTrigger value="locked">
            Locked ({lockedAchievements.length})
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="in-progress">
          {inProgressAchievements.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {inProgressAchievements.map(achievement => (
                <Card key={achievement._id} className="achievement-card border-l-4 border-l-blue-500">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg">{achievement.title}</CardTitle>
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                        {achievement.difficulty.charAt(0).toUpperCase() + achievement.difficulty.slice(1)}
                      </Badge>
                    </div>
                    <CardDescription>{achievement.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center text-sm mb-1">
                      <span>Progress</span>
                      <span>{achievement.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                      <div 
                        className="bg-blue-500 h-2 rounded-full progress-active" 
                        data-progress={achievement.progress}
                        style={{ width: `${achievement.progress}%` }}
                      ></div>
                    </div>
                    <div className="mt-1 text-sm flex">
                      <span className="text-gray-500">Reward:</span>
                      <span className="ml-1 font-medium">{achievement.reward}</span>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0">
                    <div className="text-sm text-gray-500 flex items-center">
                      <Award size={16} className="mr-1" />
                      <span>{achievement.points} points</span>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-gray-50 rounded-md">
              <p>No achievements in this category are currently in progress.</p>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="completed">
          {completedAchievements.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {completedAchievements.map(achievement => (
                <Card key={achievement._id} className="achievement-card border-l-4 border-l-green-500">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg">{achievement.title}</CardTitle>
                      <Badge className="bg-green-100 text-green-800 border-green-200 completed-badge">
                        <Check size={14} className="mr-1" />
                        Completed
                      </Badge>
                    </div>
                    <CardDescription>{achievement.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '100%' }}></div>
                    </div>
                    <div className="mt-1 text-sm flex">
                      <span className="text-gray-500">Reward:</span>
                      <span className="ml-1 font-medium">{achievement.reward}</span>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0">
                    <div className="text-sm text-gray-500 flex items-center">
                      <Award size={16} className="mr-1" />
                      <span>{achievement.points} points earned</span>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-gray-50 rounded-md">
              <p>You haven't completed any achievements in this category yet.</p>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="locked">
          {lockedAchievements.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {lockedAchievements.map(achievement => (
                <Card key={achievement._id} className="achievement-card border-l-4 border-l-gray-400 opacity-80">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg flex items-center">
                        <Lock size={16} className="mr-2 text-gray-400" />
                        {achievement.title}
                      </CardTitle>
                      <Badge variant="outline" className="bg-gray-100 text-gray-600 border-gray-200">
                        {achievement.difficulty.charAt(0).toUpperCase() + achievement.difficulty.slice(1)}
                      </Badge>
                    </div>
                    <CardDescription>{achievement.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                      <div className="bg-gray-400 h-2 rounded-full" style={{ width: '0%' }}></div>
                    </div>
                    <div className="mt-1 text-sm flex">
                      <span className="text-gray-500">Reward:</span>
                      <span className="ml-1 font-medium">{achievement.reward}</span>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0">
                    <div className="text-sm text-gray-500 flex items-center">
                      <Award size={16} className="mr-1" />
                      <span>{achievement.points} potential points</span>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-gray-50 rounded-md">
              <p>No locked achievements in this category.</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
      
      <Card className="bg-gradient-to-r from-earnhub-red/90 to-purple-600 text-white border-none mb-8">
        <CardContent className="py-8">
          <div className="text-center">
            <h3 className="text-xl font-bold mb-2">Next Achievement Milestone</h3>
            <p className="mb-6 opacity-90">Complete 5 more tasks to unlock "Task Master" achievement</p>
            <div className="flex items-center justify-center gap-2">
              <span className="font-medium">Your Progress</span>
              <ArrowRight size={18} />
              <span className="font-bold">50 more points to Silver Level</span>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">How Achievements Work</h3>
        <ul className="space-y-3">
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Award className="h-4 w-4 text-white" />
            </div>
            <span>Complete tasks and activities on EarnHub to earn achievements.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Award className="h-4 w-4 text-white" />
            </div>
            <span>Each achievement awards points that count toward your level and status.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Award className="h-4 w-4 text-white" />
            </div>
            <span>Higher levels unlock exclusive opportunities, higher earnings, and special badges.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Award className="h-4 w-4 text-white" />
            </div>
            <span>Achievements can also reward you with EarnHub points, cash bonuses, and special access.</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default AchievementsPage;
