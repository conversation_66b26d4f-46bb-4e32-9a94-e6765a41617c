
import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Activity, Check, Clock, Gift, Star, Zap } from "lucide-react";
import anime from "animejs";
import { useToast } from "@/hooks/use-toast";

// Quest data structure (compatible with Sanity CMS)
interface Quest {
  _id: string;
  title: string;
  description: string;
  category: 'daily' | 'weekly' | 'special';
  steps: {
    title: string;
    completed: boolean;
  }[];
  reward: {
    points: number;
    cash?: number;
    other?: string;
  };
  progress: number;  // 0-100
  expiry?: string;   // ISO date string
  completed?: boolean;
  claimed?: boolean;
}

// Mock quests that would be fetched from <PERSON>ity CMS
const mockQuests: Quest[] = [
  {
    _id: '1',
    title: 'Daily Login Streak',
    description: 'Log in to EarnHub for 5 consecutive days',
    category: 'daily',
    steps: [
      { title: 'Day 1: Log in to EarnHub', completed: true },
      { title: 'Day 2: Log in to EarnHub', completed: true },
      { title: 'Day 3: Log in to EarnHub', completed: true },
      { title: 'Day 4: Log in to EarnHub', completed: false },
      { title: 'Day 5: Log in to EarnHub', completed: false },
    ],
    reward: {
      points: 50,
      cash: 25,
    },
    progress: 60,
    expiry: '2025-05-15T23:59:59Z',
  },
  {
    _id: '2',
    title: 'Task Completion',
    description: 'Complete 3 tasks this week',
    category: 'weekly',
    steps: [
      { title: 'Complete first task', completed: true },
      { title: 'Complete second task', completed: true },
      { title: 'Complete third task', completed: false },
    ],
    reward: {
      points: 100,
      cash: 50,
    },
    progress: 66,
    expiry: '2025-05-18T23:59:59Z',
  },
  {
    _id: '3',
    title: 'Social Media Sharing',
    description: 'Share EarnHub on your social media platforms',
    category: 'weekly',
    steps: [
      { title: 'Share on Facebook', completed: true },
      { title: 'Share on Twitter', completed: false },
      { title: 'Share on Instagram', completed: false },
    ],
    reward: {
      points: 75,
      other: 'Special Badge',
    },
    progress: 33,
    expiry: '2025-05-20T23:59:59Z',
  },
  {
    _id: '4',
    title: 'Referral Quest',
    description: 'Refer 2 friends to EarnHub this month',
    category: 'special',
    steps: [
      { title: 'Refer first friend', completed: true },
      { title: 'Refer second friend', completed: false },
    ],
    reward: {
      points: 200,
      cash: 100,
      other: 'Referral Badge',
    },
    progress: 50,
    expiry: '2025-05-31T23:59:59Z',
  },
  {
    _id: '5',
    title: 'Survey Completion',
    description: 'Complete the market research survey',
    category: 'daily',
    steps: [
      { title: 'Answer all survey questions', completed: true },
    ],
    reward: {
      points: 30,
      cash: 15,
    },
    progress: 100,
    completed: true,
    claimed: false,
  },
  {
    _id: '6',
    title: 'Profile Completion',
    description: 'Complete your EarnHub profile details',
    category: 'special',
    steps: [
      { title: 'Add profile picture', completed: true },
      { title: 'Verify email address', completed: true },
      { title: 'Add phone number', completed: true },
      { title: 'Link social media accounts', completed: true },
    ],
    reward: {
      points: 100,
    },
    progress: 100,
    completed: true,
    claimed: true,
  },
];

// Formatted time remaining from ISO date string
const getTimeRemaining = (expiry: string) => {
  const total = new Date(expiry).getTime() - new Date().getTime();
  if (total <= 0) return 'Expired';
  
  const days = Math.floor(total / (1000 * 60 * 60 * 24));
  const hours = Math.floor((total / (1000 * 60 * 60)) % 24);
  
  if (days > 0) {
    return `${days}d ${hours}h remaining`;
  } else {
    const minutes = Math.floor((total / (1000 * 60)) % 60);
    return `${hours}h ${minutes}m remaining`;
  }
};

// Quest card component
const QuestCard = ({ quest, onClaim }: { quest: Quest, onClaim: (id: string) => void }) => {
  return (
    <Card className={`quest-card relative ${quest.completed ? 'border-green-500' : ''}`}>
      {quest.category === 'daily' && (
        <Badge variant="outline" className="absolute top-2 right-2 bg-blue-100 text-blue-800 border-blue-200">
          Daily
        </Badge>
      )}
      {quest.category === 'weekly' && (
        <Badge variant="outline" className="absolute top-2 right-2 bg-purple-100 text-purple-800 border-purple-200">
          Weekly
        </Badge>
      )}
      {quest.category === 'special' && (
        <Badge variant="outline" className="absolute top-2 right-2 bg-amber-100 text-amber-800 border-amber-200">
          Special
        </Badge>
      )}
      
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">{quest.title}</CardTitle>
        <CardDescription>{quest.description}</CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div>
          <div className="flex justify-between items-center text-sm mb-1">
            <span>Progress</span>
            <span>{quest.progress}%</span>
          </div>
          <Progress value={quest.progress} className="progress-bar" />
        </div>
        
        <div className="space-y-2">
          {quest.steps.map((step, idx) => (
            <div key={idx} className="flex items-center">
              {step.completed ? (
                <div className="h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mr-2">
                  <Check className="h-3 w-3 text-green-600" />
                </div>
              ) : (
                <div className="h-5 w-5 rounded-full border border-gray-300 mr-2"></div>
              )}
              <span className={`text-sm ${step.completed ? 'line-through text-gray-500' : ''}`}>
                {step.title}
              </span>
            </div>
          ))}
        </div>
        
        {quest.expiry && !quest.completed && (
          <div className="flex items-center text-sm text-gray-500">
            <Clock className="h-4 w-4 mr-1" />
            <span>{getTimeRemaining(quest.expiry)}</span>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="flex flex-col items-start">
        <div className="flex items-center text-sm text-gray-700 mb-4">
          <Gift className="h-4 w-4 mr-1 text-earnhub-red" />
          <span className="font-medium">Rewards:</span>
          <div className="flex flex-wrap gap-1 ml-1">
            <Badge variant="secondary" className="text-xs">
              {quest.reward.points} Points
            </Badge>
            {quest.reward.cash && (
              <Badge variant="secondary" className="text-xs">
                KES {quest.reward.cash}
              </Badge>
            )}
            {quest.reward.other && (
              <Badge variant="secondary" className="text-xs">
                {quest.reward.other}
              </Badge>
            )}
          </div>
        </div>
        
        {quest.completed && !quest.claimed ? (
          <Button 
            className="w-full"
            onClick={() => onClaim(quest._id)}
          >
            Claim Rewards
          </Button>
        ) : quest.completed && quest.claimed ? (
          <Button variant="outline" disabled className="w-full">
            Rewards Claimed
          </Button>
        ) : (
          <Button variant="outline" className="w-full">
            Continue Quest
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

const QuestsPage = () => {
  const [activeQuests, setActiveQuests] = useState<Quest[]>([]);
  const [completedQuests, setCompletedQuests] = useState<Quest[]>([]);
  const { toast } = useToast();
  
  useEffect(() => {
    // Split quests into active and completed
    setActiveQuests(mockQuests.filter(q => !q.completed));
    setCompletedQuests(mockQuests.filter(q => q.completed));
    
    // Animation for quest cards
    anime({
      targets: '.quest-card',
      scale: [0.97, 1],
      opacity: [0, 1],
      delay: anime.stagger(100),
      easing: 'easeOutExpo',
      duration: 800
    });
    
    // Animation for progress bars
    anime({
      targets: '.progress-bar',
      width: function(el: HTMLElement) {
        const parent = el.parentNode as HTMLElement;
        const widthPercentage = el.style.width || '0%';
        return widthPercentage;
      },
      easing: 'easeInOutQuad',
      duration: 1000,
      delay: anime.stagger(150)
    });
    
    // Animate the activity icon
    anime({
      targets: '.activity-icon',
      rotate: [-3, 3],
      duration: 2000,
      direction: 'alternate',
      loop: true,
      easing: 'easeInOutQuad'
    });
  }, []);
  
  const handleClaimReward = (questId: string) => {
    const quest = mockQuests.find(q => q._id === questId);
    if (!quest) return;
    
    // Mark quest as claimed
    quest.claimed = true;
    
    // Update state
    setCompletedQuests([...completedQuests]);
    
    // Show success toast with rewards
    toast({
      title: "Rewards Claimed!",
      description: `You've received ${quest.reward.points} points${quest.reward.cash ? ` and KES ${quest.reward.cash}` : ''}${quest.reward.other ? ` and ${quest.reward.other}` : ''}.`,
    });
  };
  
  // Filter quests by category
  const filterQuestsByCategory = (quests: Quest[], category: string) => {
    if (category === 'all') return quests;
    return quests.filter(quest => quest.category === category);
  };
  
  return (
    <div className="container mx-auto px-4 py-6 pb-24 md:pb-6">
      <div className="mb-8 text-center">
        <div className="inline-block p-4 rounded-full bg-earnhub-red/10 mb-4">
          <Activity size={32} className="text-earnhub-red activity-icon" />
        </div>
        <h1 className="text-2xl md:text-3xl font-bold mb-2">Quests & Challenges</h1>
        <p className="text-gray-600 max-w-lg mx-auto">
          Complete quests to earn points, cash rewards, and special badges. Track your progress and claim rewards.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="animate-card bg-gradient-to-br from-blue-50 to-blue-100">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="bg-blue-100 p-2 rounded-full mr-3">
                  <Star className="h-5 w-5 text-blue-600" />
                </div>
                <h3 className="font-medium">Daily Quests</h3>
              </div>
              <Badge className="bg-blue-100 text-blue-800 border-none">Refreshes Daily</Badge>
            </div>
            <p className="text-sm text-gray-600">Simple tasks that can be completed each day for quick rewards.</p>
            <div className="mt-4">
              <div className="text-xs text-gray-500 mb-1">Daily Quests Completed</div>
              <div className="flex justify-between items-center">
                <Progress value={50} className="flex-1 mr-2" />
                <span className="text-sm">1/2</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="animate-card bg-gradient-to-br from-purple-50 to-purple-100">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="bg-purple-100 p-2 rounded-full mr-3">
                  <Zap className="h-5 w-5 text-purple-600" />
                </div>
                <h3 className="font-medium">Weekly Challenges</h3>
              </div>
              <Badge className="bg-purple-100 text-purple-800 border-none">5 Days Left</Badge>
            </div>
            <p className="text-sm text-gray-600">More substantial tasks with bigger rewards that reset weekly.</p>
            <div className="mt-4">
              <div className="text-xs text-gray-500 mb-1">Weekly Quests Completed</div>
              <div className="flex justify-between items-center">
                <Progress value={50} className="flex-1 mr-2" />
                <span className="text-sm">1/2</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="animate-card bg-gradient-to-br from-amber-50 to-amber-100">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="bg-amber-100 p-2 rounded-full mr-3">
                  <Gift className="h-5 w-5 text-amber-600" />
                </div>
                <h3 className="font-medium">Special Quests</h3>
              </div>
              <Badge className="bg-amber-100 text-amber-800 border-none">Limited Time</Badge>
            </div>
            <p className="text-sm text-gray-600">One-time or special event quests with premium rewards.</p>
            <div className="mt-4">
              <div className="text-xs text-gray-500 mb-1">Special Quests Completed</div>
              <div className="flex justify-between items-center">
                <Progress value={50} className="flex-1 mr-2" />
                <span className="text-sm">1/2</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="active" className="mb-8">
        <TabsList className="mb-6 w-full grid grid-cols-2">
          <TabsTrigger value="active">
            Active Quests ({activeQuests.length})
          </TabsTrigger>
          <TabsTrigger value="completed">
            Completed Quests ({completedQuests.length})
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="active">
          <Tabs defaultValue="all">
            <TabsList className="mb-4 w-full grid grid-cols-4">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="daily">Daily</TabsTrigger>
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
              <TabsTrigger value="special">Special</TabsTrigger>
            </TabsList>
            
            {['all', 'daily', 'weekly', 'special'].map((category) => (
              <TabsContent key={category} value={category}>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filterQuestsByCategory(activeQuests, category).map(quest => (
                    <QuestCard 
                      key={quest._id} 
                      quest={quest} 
                      onClaim={handleClaimReward} 
                    />
                  ))}
                  
                  {filterQuestsByCategory(activeQuests, category).length === 0 && (
                    <div className="col-span-full text-center py-12">
                      <p>No active quests in this category. Check back later!</p>
                    </div>
                  )}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </TabsContent>
        
        <TabsContent value="completed">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {completedQuests.map(quest => (
              <QuestCard 
                key={quest._id} 
                quest={quest} 
                onClaim={handleClaimReward} 
              />
            ))}
            
            {completedQuests.length === 0 && (
              <div className="col-span-full text-center py-12">
                <p>You haven't completed any quests yet. Get started with active quests!</p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
      
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">How Quests Work</h3>
        <ul className="space-y-3">
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Activity className="h-4 w-4 text-white" />
            </div>
            <span>Complete quests to earn points, cash rewards, and special badges.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Activity className="h-4 w-4 text-white" />
            </div>
            <span>Daily quests reset every day at midnight, while weekly quests reset every Monday.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Activity className="h-4 w-4 text-white" />
            </div>
            <span>Special quests are available for a limited time and offer premium rewards.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Activity className="h-4 w-4 text-white" />
            </div>
            <span>Be sure to claim your rewards after completing a quest!</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default QuestsPage;
