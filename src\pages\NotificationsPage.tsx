import { useState } from 'react';
import { useNotifications } from '@/hooks/use-notifications';
import { Helmet } from 'react-helmet-async';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { NotificationType, PriorityLevel } from '@/types/notification';
import NotificationItem from '@/components/notifications/NotificationItem';
import { Bell, Check, Filter, X, Settings } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';

export function NotificationsPage() {
  const { 
    notifications, 
    unreadCount, 
    filters, 
    updateFilters, 
    resetFilters, 
    markAllAsRead 
  } = useNotifications();
  const navigate = useNavigate();
  const [showSettings, setShowSettings] = useState(false);
  
  // Group notifications by date
  const groupedNotifications = notifications.reduce((acc, notification) => {
    const date = new Date(notification.createdAt);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    let groupKey = '';
    
    if (date.toDateString() === today.toDateString()) {
      groupKey = 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      groupKey = 'Yesterday';
    } else {
      groupKey = date.toLocaleDateString();
    }
    
    if (!acc[groupKey]) {
      acc[groupKey] = [];
    }
    
    acc[groupKey].push(notification);
    return acc;
  }, {} as Record<string, typeof notifications>);
  
  const typeOptions: { value: NotificationType; label: string }[] = [
    { value: 'system', label: 'System' },
    { value: 'account', label: 'Account' },
    { value: 'competition', label: 'Competitions' },
    { value: 'task', label: 'Tasks' },
    { value: 'promotion', label: 'Promotions' },
    { value: 'partner', label: 'Partner' },
    { value: 'recommendation', label: 'Recommendations' },
  ];
  
  const notificationSettings = {
    emailNotifications: true,
    pushNotifications: true,
    typePreferences: typeOptions.map(opt => ({
      type: opt.value,
      enabled: true
    })),
    priorityThreshold: 'all'
  };
  
  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20">
      <Helmet>
        <title>Notifications | EarnHub</title>
      </Helmet>
      
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Bell className="h-6 w-6 mr-2 text-earnhub-red" />
          <h1 className="text-2xl font-bold">Notifications</h1>
          {unreadCount > 0 && (
            <Badge variant="default" className="ml-2 bg-earnhub-red">
              {unreadCount} unread
            </Badge>
          )}
        </div>
        
        <div className="flex space-x-2">
          <Button 
            variant="outline"
            size="sm"
            onClick={() => setShowSettings(!showSettings)}
            className="hidden md:flex"
          >
            <Settings className="h-4 w-4 mr-2" />
            Preferences
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={markAllAsRead}
            disabled={unreadCount === 0}
          >
            <Check className="h-4 w-4 mr-2" />
            Mark all as read
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
        <div className="md:col-span-2 lg:col-span-3">
          <div className="mb-6 flex flex-wrap gap-2 items-center">
            <div className="flex items-center space-x-2 mr-2">
              <span className="text-sm font-medium">Filter:</span>
              <Select
                value={filters.readStatus}
                onValueChange={(value) => updateFilters({ readStatus: value as any })}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="read">Read</SelectItem>
                  <SelectItem value="unread">Unread</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2 mr-2">
              <Select
                value={filters.priority}
                onValueChange={(value) => updateFilters({ priority: value as any })}
              >
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All priorities</SelectItem>
                  <SelectItem value="high">High priority</SelectItem>
                  <SelectItem value="medium">Medium priority</SelectItem>
                  <SelectItem value="low">Low priority</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex flex-wrap gap-1 mt-2 md:mt-0">
              {typeOptions.map((type) => (
                <Badge
                  key={type.value}
                  variant={filters.types.includes(type.value) ? "default" : "outline"}
                  className={`cursor-pointer ${
                    filters.types.includes(type.value)
                      ? "bg-earnhub-red hover:bg-earnhub-red/90"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => {
                    const currentTypes = [...filters.types];
                    const index = currentTypes.indexOf(type.value);
                    
                    if (index === -1) {
                      updateFilters({ types: [...currentTypes, type.value] });
                    } else {
                      currentTypes.splice(index, 1);
                      updateFilters({ types: currentTypes });
                    }
                  }}
                >
                  {type.label}
                </Badge>
              ))}
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={resetFilters}
              className="ml-auto text-earnhub-red"
            >
              Reset filters
            </Button>
          </div>
          
          {/* Notifications list */}
          <div className="space-y-6">
            {Object.entries(groupedNotifications).length > 0 ? (
              Object.entries(groupedNotifications).map(([date, items]) => (
                <div key={date}>
                  <h2 className="text-sm font-medium text-gray-500 mb-2">{date}</h2>
                  <div className="bg-white rounded-lg border overflow-hidden">
                    {items.map((notification) => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                      />
                    ))}
                  </div>
                </div>
              ))
            ) : (
              <Alert>
                <AlertTitle>No notifications found</AlertTitle>
                <AlertDescription>
                  Try adjusting your filters or check back later for new notifications.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </div>
        
        {/* Sidebar/Settings */}
        {(showSettings || !notifications.length) && (
          <div className="md:block">
            <div className="bg-white rounded-lg border p-4 sticky top-24">
              <h2 className="font-bold text-lg mb-4">Notification Preferences</h2>
              
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="font-medium text-sm">Notification Channels</h3>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Email notifications</span>
                    <input 
                      type="checkbox" 
                      checked={notificationSettings.emailNotifications}
                      className="toggle toggle-primary" 
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Push notifications</span>
                    <input 
                      type="checkbox" 
                      checked={notificationSettings.pushNotifications}
                      className="toggle toggle-primary" 
                    />
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-3">
                  <h3 className="font-medium text-sm">Notification Types</h3>
                  <p className="text-xs text-gray-500">Select which types of notifications you want to receive</p>
                  
                  <div className="space-y-2">
                    {typeOptions.map((type) => (
                      <div key={type.value} className="flex items-center justify-between">
                        <span className="text-sm capitalize">{type.label}</span>
                        <input 
                          type="checkbox" 
                          checked={notificationSettings.typePreferences.find(t => t.type === type.value)?.enabled}
                          className="toggle toggle-primary" 
                        />
                      </div>
                    ))}
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-3">
                  <h3 className="font-medium text-sm">Priority Threshold</h3>
                  <p className="text-xs text-gray-500">Only receive notifications above selected priority</p>
                  
                  <Select defaultValue={notificationSettings.priorityThreshold}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority threshold" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All notifications</SelectItem>
                      <SelectItem value="medium">Medium and high priority</SelectItem>
                      <SelectItem value="high">High priority only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="pt-2">
                  <Button className="w-full bg-earnhub-red hover:bg-earnhub-red/90">
                    Save Preferences
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default NotificationsPage;
