-- Enable RLS for all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;

-- Profiles Table
CREATE TABLE public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name <PERSON><PERSON><PERSON><PERSON>(255),
    username VA<PERSON>HA<PERSON>(50) UNIQUE,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    account_status VARCHAR(20) DEFAULT 'active' CHECK (account_status IN ('active', 'suspended', 'deactivated')),
    membership_tier_id VARCHAR(50) DEFAULT 'starter',
    membership_expiry_date TIMESTAMPTZ,
    total_points INTEGER DEFAULT 0,
    is_ambassador BOOLEAN DEFAULT FALSE,
    referral_code VA<PERSON>HAR(50) UNIQUE,
    last_login TIMESTAMPTZ,
    gender VARCHAR(20),
    age_range VARCHAR(20),
    county VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Kenya',
    income_level VARCHAR(50),
    education_level VARCHAR(50),
    social_media JSONB,
    availability VARCHAR(50),
    avatar_sanity_id VARCHAR(255),
    bio TEXT,
    profile_completion_percentage SMALLINT DEFAULT 0 CHECK (profile_completion_percentage >= 0 AND profile_completion_percentage <= 100)
);

-- RLS for Profiles Table
CREATE POLICY "Users can view their own profile."
ON public.profiles
FOR SELECT
USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile."
ON public.profiles
FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Trigger for Profiles Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Enable RLS for Partner Program tables
ALTER TABLE public.partner_details ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.affiliate_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.partner_commissions ENABLE ROW LEVEL SECURITY;

-- Partner Details Table
CREATE TABLE public.partner_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID UNIQUE NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    application_status VARCHAR(30) DEFAULT 'not_applied' NOT NULL 
        CHECK (application_status IN ('not_applied', 'pending_review', 'approved', 'rejected', 'suspended')),
    approved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- RLS for Partner Details Table
CREATE POLICY "Users can manage their own partner application."
ON public.partner_details
FOR ALL
USING (auth.uid() = user_id);

-- Trigger for Partner Details Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.partner_details
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Affiliate Clicks Table
CREATE TABLE public.affiliate_clicks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    affiliate_code_used VARCHAR(50) NOT NULL,
    partner_user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    clicked_at TIMESTAMPTZ DEFAULT now(),
    ip_address INET,
    user_agent TEXT,
    referring_url TEXT,
    landing_page_url TEXT NOT NULL,
    sanity_product_id VARCHAR(255),
    sanity_affiliate_link_id VARCHAR(255) 
);

-- RLS for Affiliate Clicks Table
CREATE POLICY "Partners can view their own clicks."
ON public.affiliate_clicks
FOR SELECT
USING (auth.uid() = partner_user_id);

CREATE POLICY "Allow public insert of clicks."
ON public.affiliate_clicks
FOR INSERT
WITH CHECK (true);

-- Orders Table
CREATE TABLE public.orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(50),
    shipping_address JSONB,
    billing_address JSONB,
    total_amount DECIMAL(12,2) NOT NULL CHECK (total_amount >= 0),
    currency VARCHAR(10) DEFAULT 'KES' NOT NULL,
    status VARCHAR(30) DEFAULT 'pending_payment' NOT NULL 
        CHECK (status IN ('pending_payment', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded', 'failed_payment', 'disputed')),
    payment_transaction_id UUID UNIQUE REFERENCES public.transactions(id) ON DELETE SET NULL,
    affiliate_click_id UUID REFERENCES public.affiliate_clicks(id) ON DELETE SET NULL,
    affiliate_code_used_on_order VARCHAR(50),
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    shipping_fee DECIMAL(10,2) DEFAULT 0.00,
    notes_to_seller TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- RLS for Orders Table
CREATE POLICY "Users can manage their own orders."
ON public.orders
FOR ALL
USING (auth.uid() = user_id);

-- Trigger for Orders Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.orders
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Order Items Table
CREATE TABLE public.order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE CASCADE,
    sanity_product_id VARCHAR(255) NOT NULL,
    product_name_snapshot VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    price_at_purchase DECIMAL(12,2) NOT NULL,
    total_line_item_amount DECIMAL(12,2) NOT NULL,
    attributes_snapshot JSONB,
    created_at TIMESTAMPTZ DEFAULT now()
    -- No updated_at trigger needed if items are immutable after creation
);

-- RLS for Order Items Table
CREATE POLICY "Users can view items in their own orders."
ON public.order_items
FOR SELECT
USING (EXISTS (SELECT 1 FROM public.orders o WHERE o.id = order_id AND o.user_id = auth.uid()));

-- Partner Commissions Table
CREATE TABLE public.partner_commissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_item_id UUID UNIQUE NOT NULL REFERENCES public.order_items(id) ON DELETE CASCADE,
    order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE RESTRICT, -- To quickly link back to order if needed
    partner_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    commission_amount DECIMAL(12,2) NOT NULL CHECK (commission_amount >= 0),
    commission_rate_snapshot DECIMAL(5,2), -- e.g., 0.10 for 10%
    product_value_snapshot DECIMAL(12,2),
    status VARCHAR(30) DEFAULT 'pending_approval' NOT NULL 
        CHECK (status IN ('pending_approval', 'approved', 'rejected', 'paid_out', 'cancelled')),
    payout_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    reviewed_at TIMESTAMPTZ,
    paid_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- RLS for Partner Commissions Table
CREATE POLICY "Partners can view their own commissions."
ON public.partner_commissions
FOR SELECT
USING (auth.uid() = partner_user_id);

-- Trigger for Partner Commissions Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.partner_commissions
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Function to generate a random referral code
CREATE OR REPLACE FUNCTION public.generate_referral_code()
RETURNS TEXT AS $$
DECLARE
  new_code TEXT;
  is_unique BOOLEAN := FALSE;
BEGIN
  -- Generate an 8-character hex string. This gives 16^8 possibilities.
  -- For more complex codes, consider other encoding or character sets.
  -- While loop for uniqueness check is good practice, but for simplicity
  -- and given the low collision probability with enough length,
  -- we might omit it for an initial version if performance is critical
  -- and the application can handle extremely rare collisions gracefully
  -- (e.g., by a unique constraint that would cause the insert to fail,
  -- prompting a retry or manual intervention).
  -- However, for this implementation, a basic loop is included.
  LOOP
    new_code := substring(encode(gen_random_bytes(6), 'hex') for 8);
    -- Check if the generated code already exists
    PERFORM 1 FROM public.profiles WHERE referral_code = new_code;
    IF NOT FOUND THEN
      is_unique := TRUE;
      EXIT; -- Exit loop if code is unique
    END IF;
    -- If not unique, the loop continues to generate a new one.
    -- Add a safety break or max attempts if there's a concern about infinite loops,
    -- though highly unlikely with sufficient code length.
  END LOOP;
  RETURN new_code;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Trigger function to set referral_code on new profile insert
CREATE OR REPLACE FUNCTION public.set_referral_code_on_profile_insert()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.referral_code IS NULL THEN
    NEW.referral_code := public.generate_referral_code();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to execute the function before insert on profiles table
CREATE TRIGGER trigger_set_referral_code_on_profile_insert
BEFORE INSERT ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION public.set_referral_code_on_profile_insert();

-- Enable RLS for membership_purchases, user_devices, and referrals tables
ALTER TABLE public.membership_purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY;

-- Membership Purchases Table
CREATE TABLE public.membership_purchases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    sanity_membership_tier_id VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending_payment' NOT NULL CHECK (status IN ('pending_payment', 'completed', 'failed', 'refunded')),
    purchase_date TIMESTAMPTZ,
    amount_paid DECIMAL(10, 2),
    currency VARCHAR(10) DEFAULT 'KES',
    transaction_id UUID UNIQUE REFERENCES public.transactions(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- RLS for Membership Purchases Table
CREATE POLICY "Users can view their own membership purchases."
ON public.membership_purchases
FOR SELECT
USING (auth.uid() = user_id);

-- Trigger for Membership Purchases Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.membership_purchases
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- User Devices Table
CREATE TABLE public.user_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    device_type VARCHAR(50) NOT NULL CHECK (device_type IN ('android', 'ios', 'web_push', 'other')),
    device_token TEXT NOT NULL UNIQUE,
    last_seen TIMESTAMPTZ DEFAULT now(),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- RLS for User Devices Table
CREATE POLICY "Users can manage their own devices."
ON public.user_devices
FOR ALL
USING (auth.uid() = user_id);

-- Trigger for User Devices Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.user_devices
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Referrals Table
CREATE TABLE public.referrals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    referrer_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    referee_user_id UUID UNIQUE REFERENCES public.profiles(id) ON DELETE SET NULL,
    referral_code_used VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' NOT NULL CHECK (status IN ('pending', 'registered', 'criteria_met', 'rewarded', 'expired', 'invalid')),
    reward_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL,
    reward_amount_snapshot DECIMAL(10,2),
    conditions_met_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- RLS for Referrals Table
CREATE POLICY "Users can view their own referrals."
ON public.referrals
FOR SELECT
USING (auth.uid() = referrer_user_id OR auth.uid() = referee_user_id);

-- Trigger for Referrals Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.referrals
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Wallets Table
CREATE TABLE public.wallets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL UNIQUE REFERENCES public.profiles(id) ON DELETE CASCADE,
    balance DECIMAL(14, 2) DEFAULT 0.00 CHECK (balance >= 0),
    pending_balance DECIMAL(14, 2) DEFAULT 0.00 CHECK (pending_balance >= 0),
    currency VARCHAR(10) DEFAULT 'KES' NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- RLS for Wallets Table
CREATE POLICY "Users can view their own wallet."
ON public.wallets
FOR SELECT
USING (auth.uid() = user_id);

-- Trigger for Wallets Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.wallets
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Transactions Table
CREATE TABLE public.transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    wallet_id UUID NOT NULL REFERENCES public.wallets(id) ON DELETE RESTRICT,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    amount DECIMAL(14, 2) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('deposit', 'withdrawal', 'earning_quest', 'referral_bonus', 'tip', 'refund', 'adjustment', 'payout', 'spend_membership_payment')),
    status VARCHAR(20) DEFAULT 'pending' NOT NULL CHECK (status IN ('pending', 'pending_payment', 'completed', 'failed', 'cancelled', 'expired', 'disputed')),
    description TEXT,
    reference_entity_type VARCHAR(50),
    reference_entity_id VARCHAR(255),
    payment_processor_checkout_id VARCHAR(255),
    payment_processor_reference VARCHAR(255),
    payment_purpose_details JSONB,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- RLS for Transactions Table
CREATE POLICY "Users can view their own transactions."
ON public.transactions
FOR SELECT
USING (auth.uid() = user_id);

-- Trigger for Transactions Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.transactions
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();
