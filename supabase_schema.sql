-- EarnHub Supabase Database Schema
-- This schema supports the complete EarnHub platform including:
-- - User authentication and profiles
-- - Membership system with Lipia payment integration
-- - Partner program with affiliate tracking
-- - Shop with orders and commissions
-- - Quests, competitions, and achievements
-- - Wallet and transaction management
-- - Referral system
-- - Notifications and real-time features

-- Step 1: <PERSON>reate helper functions first
CREATE OR REPLACE FUNCTION public.moddatetime()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = timezone('utc', now());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to generate a random referral code
CREATE OR REPLACE FUNCTION public.generate_referral_code()
RETURNS TEXT AS $$
DECLARE
  new_code TEXT;
  is_unique BOOLEAN := FALSE;
BEGIN
  LOOP
    new_code := substring(encode(gen_random_bytes(6), 'hex') for 8);
    -- Check if the generated code already exists
    PERFORM 1 FROM public.profiles WHERE referral_code = new_code;
    IF NOT FOUND THEN
      is_unique := TRUE;
      EXIT; -- Exit loop if code is unique
    END IF;
  END LOOP;
  RETURN new_code;
END;
$$ LANGUAGE plpgsql VOLATILE;

-- Step 2: Core User Tables
-- Profiles Table (extends auth.users from Supabase Auth)
CREATE TABLE public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name VARCHAR(255),
    username VARCHAR(50) UNIQUE,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    account_status VARCHAR(20) DEFAULT 'active' CHECK (account_status IN ('active', 'suspended', 'deactivated')),
    membership_tier_id VARCHAR(50) DEFAULT 'starter',
    membership_expiry_date TIMESTAMPTZ,
    total_points INTEGER DEFAULT 0,
    is_ambassador BOOLEAN DEFAULT FALSE,
    referral_code VARCHAR(50) UNIQUE,
    last_login TIMESTAMPTZ,
    gender VARCHAR(20),
    age_range VARCHAR(20),
    county VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Kenya',
    income_level VARCHAR(50),
    education_level VARCHAR(50),
    social_media JSONB,
    availability VARCHAR(50),
    avatar_sanity_id VARCHAR(255),
    bio TEXT,
    profile_completion_percentage SMALLINT DEFAULT 0 CHECK (profile_completion_percentage >= 0 AND profile_completion_percentage <= 100)
);

-- Enable RLS and create policies for Profiles Table
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile."
ON public.profiles
FOR SELECT
USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile."
ON public.profiles
FOR UPDATE
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can insert their own profile."
ON public.profiles
FOR INSERT
WITH CHECK (auth.uid() = id);

-- Trigger for Profiles Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Trigger function to set referral_code on new profile insert
CREATE OR REPLACE FUNCTION public.set_referral_code_on_profile_insert()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.referral_code IS NULL THEN
    NEW.referral_code := public.generate_referral_code();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to execute the function before insert on profiles table
CREATE TRIGGER trigger_set_referral_code_on_profile_insert
BEFORE INSERT ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION public.set_referral_code_on_profile_insert();

-- Step 3: Financial Tables (Wallets and Transactions)
-- Wallets Table
CREATE TABLE public.wallets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL UNIQUE REFERENCES public.profiles(id) ON DELETE CASCADE,
    balance DECIMAL(14, 2) DEFAULT 0.00 CHECK (balance >= 0),
    pending_balance DECIMAL(14, 2) DEFAULT 0.00 CHECK (pending_balance >= 0),
    currency VARCHAR(10) DEFAULT 'KES' NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for Wallets Table
ALTER TABLE public.wallets ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own wallet."
ON public.wallets
FOR SELECT
USING (auth.uid() = user_id);

-- Trigger for Wallets Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.wallets
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Transactions Table
CREATE TABLE public.transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    wallet_id UUID NOT NULL REFERENCES public.wallets(id) ON DELETE RESTRICT,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    amount DECIMAL(14, 2) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN (
        'deposit',
        'withdrawal',
        'earning_quest',
        'earning_referral',
        'earning_competition_prize',
        'earning_raffle_win',
        'earning_partner_commission',
        'spend_membership_payment',
        'spend_product_purchase',
        'spend_raffle_ticket_purchase',
        'spend_competition_entry_fee',
        'refund_product',
        'refund_membership',
        'platform_bonus',
        'fee_withdrawal',
        'fee_platform_service',
        'adjustment_credit',
        'adjustment_debit',
        'tip',
        'payout'
    )),
    status VARCHAR(20) DEFAULT 'pending' NOT NULL CHECK (status IN ('pending', 'pending_payment', 'completed', 'failed', 'cancelled', 'expired', 'disputed')),
    description TEXT,
    reference_entity_type VARCHAR(50),
    reference_entity_id VARCHAR(255),
    payment_processor_checkout_id VARCHAR(255),
    payment_processor_reference VARCHAR(255),
    payment_purpose_details JSONB,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for Transactions Table
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own transactions."
ON public.transactions
FOR SELECT
USING (auth.uid() = user_id);

-- Trigger for Transactions Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.transactions
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Step 4: Engagement Tables (Quests, Competitions, Achievements)
-- User Quests Table
CREATE TABLE public.user_quests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    sanity_quest_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'started' NOT NULL CHECK (status IN ('started', 'in_progress', 'submitted_for_review', 'requires_revision', 'approved', 'rejected', 'completed_paid', 'expired')),
    submission_data JSONB,
    feedback TEXT,
    earned_amount DECIMAL(10, 2),
    earned_points INTEGER,
    reward_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL,
    started_at TIMESTAMPTZ DEFAULT now(),
    submitted_at TIMESTAMPTZ,
    reviewed_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE (user_id, sanity_quest_id)
);

-- Enable RLS and create policies for User Quests Table
ALTER TABLE public.user_quests ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own quest progress."
ON public.user_quests
FOR ALL
USING (auth.uid() = user_id);

-- Trigger for User Quests Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.user_quests
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Competition Entries Table
CREATE TABLE public.competition_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    sanity_competition_id VARCHAR(255) NOT NULL,
    score BIGINT DEFAULT 0,
    rank INTEGER,
    entry_fee_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL,
    joined_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    notes TEXT,
    UNIQUE (user_id, sanity_competition_id)
);

-- Enable RLS and create policies for Competition Entries Table
ALTER TABLE public.competition_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own competition entries."
ON public.competition_entries
FOR ALL
USING (auth.uid() = user_id);

CREATE POLICY "Public can read competition entries for leaderboards."
ON public.competition_entries
FOR SELECT
USING (true);

-- Trigger for Competition Entries Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.competition_entries
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Raffle Entries Table
CREATE TABLE public.raffle_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    sanity_raffle_id VARCHAR(255) NOT NULL,
    ticket_id VARCHAR(100) UNIQUE NOT NULL,
    ticket_purchase_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL,
    entry_time TIMESTAMPTZ DEFAULT now(),
    is_winner BOOLEAN DEFAULT FALSE,
    prize_claim_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL,
    claimed_at TIMESTAMPTZ
);

-- Enable RLS and create policies for Raffle Entries Table
ALTER TABLE public.raffle_entries ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own raffle entries."
ON public.raffle_entries
FOR ALL
USING (auth.uid() = user_id);

CREATE POLICY "Public can read raffle winner info (selectively)."
ON public.raffle_entries
FOR SELECT
USING (is_winner = true);

-- Achievement Progress Table
CREATE TABLE public.achievement_progress (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    sanity_achievement_type_id VARCHAR(255) NOT NULL,
    current_progress INTEGER DEFAULT 0,
    target_value INTEGER DEFAULT 1,
    completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMPTZ,
    reward_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE (user_id, sanity_achievement_type_id)
);

-- Enable RLS and create policies for Achievement Progress Table
ALTER TABLE public.achievement_progress ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own achievement progress."
ON public.achievement_progress
FOR ALL
USING (auth.uid() = user_id);

-- Trigger for Achievement Progress Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.achievement_progress
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Step 5: Referral System
-- Referrals Table
CREATE TABLE public.referrals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    referrer_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    referee_user_id UUID UNIQUE REFERENCES public.profiles(id) ON DELETE SET NULL,
    referral_code_used VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' NOT NULL CHECK (status IN ('pending', 'registered', 'criteria_met', 'rewarded', 'expired', 'invalid')),
    reward_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL,
    reward_amount_snapshot DECIMAL(10,2),
    conditions_met_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for Referrals Table
ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own referrals."
ON public.referrals
FOR SELECT
USING (auth.uid() = referrer_user_id OR auth.uid() = referee_user_id);

-- Trigger for Referrals Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.referrals
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Step 6: Partner Program Tables
-- Partner Details Table
CREATE TABLE public.partner_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID UNIQUE NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    application_status VARCHAR(30) DEFAULT 'not_applied' NOT NULL
        CHECK (application_status IN ('not_applied', 'pending_review', 'approved', 'rejected', 'suspended')),
    approved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for Partner Details Table
ALTER TABLE public.partner_details ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own partner application."
ON public.partner_details
FOR ALL
USING (auth.uid() = user_id);

-- Trigger for Partner Details Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.partner_details
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Affiliate Clicks Table
CREATE TABLE public.affiliate_clicks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    affiliate_code_used VARCHAR(50) NOT NULL,
    partner_user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    clicked_at TIMESTAMPTZ DEFAULT now(),
    ip_address INET,
    user_agent TEXT,
    referring_url TEXT,
    landing_page_url TEXT NOT NULL,
    sanity_product_id VARCHAR(255),
    sanity_affiliate_link_id VARCHAR(255)
);

-- Enable RLS and create policies for Affiliate Clicks Table
ALTER TABLE public.affiliate_clicks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Partners can view their own clicks."
ON public.affiliate_clicks
FOR SELECT
USING (auth.uid() = partner_user_id);

CREATE POLICY "Allow public insert of clicks."
ON public.affiliate_clicks
FOR INSERT
WITH CHECK (true);

-- Step 7: Shop and E-commerce Tables
-- Orders Table
CREATE TABLE public.orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(50),
    shipping_address JSONB,
    billing_address JSONB,
    total_amount DECIMAL(12,2) NOT NULL CHECK (total_amount >= 0),
    currency VARCHAR(10) DEFAULT 'KES' NOT NULL,
    status VARCHAR(30) DEFAULT 'pending_payment' NOT NULL
        CHECK (status IN ('pending_payment', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded', 'failed_payment', 'disputed')),
    payment_transaction_id UUID UNIQUE REFERENCES public.transactions(id) ON DELETE SET NULL,
    affiliate_click_id UUID REFERENCES public.affiliate_clicks(id) ON DELETE SET NULL,
    affiliate_code_used_on_order VARCHAR(50),
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    shipping_fee DECIMAL(10,2) DEFAULT 0.00,
    notes_to_seller TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for Orders Table
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own orders."
ON public.orders
FOR ALL
USING (auth.uid() = user_id);

-- Trigger for Orders Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.orders
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Order Items Table
CREATE TABLE public.order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE CASCADE,
    sanity_product_id VARCHAR(255) NOT NULL,
    product_name_snapshot VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    price_at_purchase DECIMAL(12,2) NOT NULL,
    total_line_item_amount DECIMAL(12,2) NOT NULL,
    attributes_snapshot JSONB,
    created_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for Order Items Table
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view items in their own orders."
ON public.order_items
FOR SELECT
USING (EXISTS (SELECT 1 FROM public.orders o WHERE o.id = order_id AND o.user_id = auth.uid()));

-- Partner Commissions Table
CREATE TABLE public.partner_commissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_item_id UUID UNIQUE NOT NULL REFERENCES public.order_items(id) ON DELETE CASCADE,
    order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE RESTRICT,
    partner_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    commission_amount DECIMAL(12,2) NOT NULL CHECK (commission_amount >= 0),
    commission_rate_snapshot DECIMAL(5,2),
    product_value_snapshot DECIMAL(12,2),
    status VARCHAR(30) DEFAULT 'pending_approval' NOT NULL
        CHECK (status IN ('pending_approval', 'approved', 'rejected', 'paid_out', 'cancelled')),
    payout_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    reviewed_at TIMESTAMPTZ,
    paid_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for Partner Commissions Table
ALTER TABLE public.partner_commissions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Partners can view their own commissions."
ON public.partner_commissions
FOR SELECT
USING (auth.uid() = partner_user_id);

-- Trigger for Partner Commissions Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.partner_commissions
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Step 8: Membership and Additional Tables
-- Membership Purchases Table
CREATE TABLE public.membership_purchases (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    sanity_membership_tier_id VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending_payment' NOT NULL CHECK (status IN ('pending_payment', 'completed', 'failed', 'refunded')),
    purchase_date TIMESTAMPTZ,
    amount_paid DECIMAL(10, 2),
    currency VARCHAR(10) DEFAULT 'KES',
    transaction_id UUID UNIQUE REFERENCES public.transactions(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for Membership Purchases Table
ALTER TABLE public.membership_purchases ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own membership purchases."
ON public.membership_purchases
FOR SELECT
USING (auth.uid() = user_id);

-- Trigger for Membership Purchases Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.membership_purchases
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- User Devices Table (for push notifications)
CREATE TABLE public.user_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    device_type VARCHAR(50) NOT NULL CHECK (device_type IN ('android', 'ios', 'web_push', 'other')),
    device_token TEXT NOT NULL UNIQUE,
    last_seen TIMESTAMPTZ DEFAULT now(),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for User Devices Table
ALTER TABLE public.user_devices ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own devices."
ON public.user_devices
FOR ALL
USING (auth.uid() = user_id);

-- Trigger for User Devices Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.user_devices
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Step 9: Notification and Communication Tables
-- User Notifications Table
CREATE TABLE public.user_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    sanity_notification_template_id VARCHAR(255),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    notification_type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    icon VARCHAR(100),
    action_url TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMPTZ,
    emailed_at TIMESTAMPTZ,
    push_sent_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for User Notifications Table
ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own notifications."
ON public.user_notifications
FOR ALL
USING (auth.uid() = user_id);

-- Live Activities Table (Platform-wide activity feed)
CREATE TABLE public.live_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    sanity_activity_definition_key VARCHAR(100),
    rendered_message TEXT NOT NULL,
    related_entity_type VARCHAR(50),
    related_entity_id VARCHAR(255),
    icon VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for Live Activities Table
ALTER TABLE public.live_activities ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can read live activities."
ON public.live_activities
FOR SELECT
USING (auth.role() = 'authenticated');

-- Step 10: Additional Support Tables
-- User Skills Table (for profile enhancement)
CREATE TABLE public.user_skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    skill_category_sanity_id VARCHAR(255) NOT NULL,
    skill_name VARCHAR(100) NOT NULL,
    proficiency_level VARCHAR(50) CHECK (proficiency_level IN ('beginner', 'intermediate', 'advanced', 'expert')),
    created_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE (user_id, skill_name)
);

-- Enable RLS and create policies for User Skills Table
ALTER TABLE public.user_skills ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own skills."
ON public.user_skills
FOR ALL
USING (auth.uid() = user_id);

-- User Interests Table (for profile enhancement)
CREATE TABLE public.user_interests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    interest_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE (user_id, interest_name)
);

-- Enable RLS and create policies for User Interests Table
ALTER TABLE public.user_interests ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own interests."
ON public.user_interests
FOR ALL
USING (auth.uid() = user_id);

-- Opportunity Applications Table (for job/gig applications)
CREATE TABLE public.opportunity_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    sanity_opportunity_id VARCHAR(255) NOT NULL,
    status VARCHAR(30) DEFAULT 'submitted' NOT NULL CHECK (status IN ('submitted', 'under_review', 'shortlisted', 'interview_scheduled', 'offer_extended', 'offer_accepted', 'offer_declined', 'rejected', 'withdrawn')),
    application_date TIMESTAMPTZ DEFAULT now(),
    cover_letter TEXT,
    attachments JSONB,
    notes TEXT,
    reviewed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE (user_id, sanity_opportunity_id)
);

-- Enable RLS and create policies for Opportunity Applications Table
ALTER TABLE public.opportunity_applications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own opportunity applications."
ON public.opportunity_applications
FOR ALL
USING (auth.uid() = user_id);

-- Trigger for Opportunity Applications Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.opportunity_applications
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Ambassador Applications Table (for ambassador program)
CREATE TABLE public.ambassador_applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL UNIQUE REFERENCES public.profiles(id) ON DELETE CASCADE,
    application_text TEXT NOT NULL,
    status VARCHAR(30) DEFAULT 'pending_review' NOT NULL CHECK (status IN ('pending_review', 'approved', 'rejected', 'interview_scheduled', 'on_hold')),
    applied_at TIMESTAMPTZ DEFAULT now(),
    reviewed_at TIMESTAMPTZ,
    reviewer_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS and create policies for Ambassador Applications Table
ALTER TABLE public.ambassador_applications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own ambassador application."
ON public.ambassador_applications
FOR ALL
USING (auth.uid() = user_id);

-- Trigger for Ambassador Applications Table
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.ambassador_applications
FOR EACH ROW
EXECUTE FUNCTION public.moddatetime();

-- Step 11: Performance Indexes
-- Create indexes for better query performance
CREATE INDEX idx_profiles_referral_code ON public.profiles(referral_code);
CREATE INDEX idx_profiles_membership_tier ON public.profiles(membership_tier_id);
CREATE INDEX idx_transactions_user_id ON public.transactions(user_id);
CREATE INDEX idx_transactions_type ON public.transactions(type);
CREATE INDEX idx_transactions_status ON public.transactions(status);
CREATE INDEX idx_transactions_created_at ON public.transactions(created_at);
CREATE INDEX idx_affiliate_clicks_partner_user_id ON public.affiliate_clicks(partner_user_id);
CREATE INDEX idx_affiliate_clicks_affiliate_code ON public.affiliate_clicks(affiliate_code_used);
CREATE INDEX idx_orders_user_id ON public.orders(user_id);
CREATE INDEX idx_orders_status ON public.orders(status);
CREATE INDEX idx_orders_created_at ON public.orders(created_at);
CREATE INDEX idx_partner_commissions_partner_user_id ON public.partner_commissions(partner_user_id);
CREATE INDEX idx_partner_commissions_status ON public.partner_commissions(status);
CREATE INDEX idx_user_quests_user_id ON public.user_quests(user_id);
CREATE INDEX idx_user_quests_status ON public.user_quests(status);
CREATE INDEX idx_competition_entries_user_id ON public.competition_entries(user_id);
CREATE INDEX idx_referrals_referrer_user_id ON public.referrals(referrer_user_id);
CREATE INDEX idx_referrals_referee_user_id ON public.referrals(referee_user_id);
CREATE INDEX idx_user_notifications_user_id ON public.user_notifications(user_id);
CREATE INDEX idx_user_notifications_is_read ON public.user_notifications(is_read);

-- Step 12: Final Comments and Documentation
-- This schema provides a complete foundation for the EarnHub platform with:
-- 1. User authentication and profiles with Supabase Auth integration
-- 2. Comprehensive financial system with wallets and transactions
-- 3. Partner program with affiliate tracking and commission management
-- 4. Shop functionality with orders and order items
-- 5. Engagement features (quests, competitions, achievements, raffles)
-- 6. Referral system with automatic code generation
-- 7. Notification system for real-time updates
-- 8. Membership system with Lipia payment integration
-- 9. Proper RLS policies for data security
-- 10. Performance indexes for optimal query speed

-- All tables are properly secured with Row Level Security (RLS)
-- All tables have appropriate triggers for updated_at timestamps
-- Foreign key relationships ensure data integrity
-- Check constraints validate data quality
-- Indexes optimize query performance
