import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { User, Flag, Star, Check, Calendar, DollarSign, Award, Users } from "lucide-react";
import anime from "animejs";
import { useToast } from '@/hooks/use-toast';

// Ambassador application form type
interface AmbassadorFormValues {
  name: string;
  email: string;
  phone: string;
  experience: string;
  socialMedia: string;
  motivation: string;
}

// Mock ambassador data structure (compatible with Sanity CMS)
interface Ambassador {
  _id: string;
  name: string;
  title: string;
  level: string;
  avatar?: string;
  achievements: string[];
  members: number;
  featured?: boolean;
  testimonial?: string;
}

// Mock ambassadors - in a real app would be fetched from Sanity CMS
const mockAmbassadors: Ambassador[] = [
  {
    _id: '1',
    name: 'Sarah Johnson',
    title: 'Lead Ambassador',
    level: 'Diamond',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=150&h=150',
    achievements: ['Top Recruiter 2024', '100+ Referrals', 'Event Organizer'],
    members: 127,
    featured: true,
    testimonial: 'Being an ambassador has opened doors to amazing opportunities and allowed me to build a network of incredible people while earning great income.'
  },
  {
    _id: '2',
    name: 'Michael Chen',
    title: 'Regional Ambassador',
    level: 'Platinum',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=150&h=150',
    achievements: ['Rising Star Award', '50+ Referrals', 'Community Builder'],
    members: 68,
    featured: true
  },
  {
    _id: '3',
    name: 'Emily Nguyen',
    title: 'Campus Ambassador',
    level: 'Gold',
    avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&w=150&h=150',
    achievements: ['University Champion', '30+ Referrals'],
    members: 42
  },
  {
    _id: '4',
    name: 'David Ochieng',
    title: 'Community Ambassador',
    level: 'Silver',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=150&h=150',
    achievements: ['Local Events Organizer', 'Rising Talent'],
    members: 28
  }
];

// Ambassador benefits from Sanity CMS
interface AmbassadorBenefit {
  _id: string;
  title: string;
  description: string;
  icon: keyof typeof iconMap;
}

// Map of icon names to components - removing Users icon and using only allowed icons
const iconMap = {
  DollarSign,
  Star,
  Check,
  Calendar,
  Award
};

// Mock benefits - in a real app would be fetched from Sanity CMS
const mockBenefits: AmbassadorBenefit[] = [
  {
    _id: '1',
    title: 'Increased Earnings',
    description: 'Earn higher commissions on all referrals and activities compared to regular members.',
    icon: 'DollarSign'
  },
  {
    _id: '2',
    title: 'Exclusive Opportunities',
    description: 'Get first access to premium tasks, competitions, and events not available to regular users.',
    icon: 'Star'
  },
  {
    _id: '3',
    title: 'Team Management',
    description: 'Build and manage your own team of referrals with multi-level commission structures.',
    icon: 'Star' // Changed from 'Users' to 'Star'
  },
  {
    _id: '4',
    title: 'Featured Profile',
    description: 'Get visibility with a featured ambassador profile on our platform.',
    icon: 'Award'
  },
  {
    _id: '5',
    title: 'Event Hosting',
    description: 'Organize and host official EarnHub events in your region with our support.',
    icon: 'Calendar'
  }
];

// Ambassador levels and requirements
interface AmbassadorLevel {
  name: string;
  requirements: string[];
  benefits: string[];
  color: string;
}

const ambassadorLevels: AmbassadorLevel[] = [
  {
    name: 'Bronze',
    requirements: ['10+ Active Referrals', '3+ Months Active'],
    benefits: ['5% Higher Commissions', 'Basic Support'],
    color: 'bg-amber-700'
  },
  {
    name: 'Silver',
    requirements: ['25+ Active Referrals', '6+ Months Active', 'Monthly Activity Goals'],
    benefits: ['10% Higher Commissions', 'Priority Support', 'Monthly Bonus'],
    color: 'bg-gray-400'
  },
  {
    name: 'Gold',
    requirements: ['50+ Active Referrals', '9+ Months Active', 'Community Leadership'],
    benefits: ['15% Higher Commissions', 'VIP Support', 'Quarterly Bonus', 'Team Building'],
    color: 'bg-yellow-500'
  },
  {
    name: 'Platinum',
    requirements: ['100+ Active Referrals', '12+ Months Active', 'Regional Leadership'],
    benefits: ['20% Higher Commissions', '24/7 Support', 'Monthly Salary', 'Team Commissions'],
    color: 'bg-blue-500'
  },
  {
    name: 'Diamond',
    requirements: ['250+ Active Referrals', '18+ Months Active', 'National Leadership'],
    benefits: ['25% Higher Commissions', 'Personal Account Manager', 'Full Salary+Benefits', 'Executive Meetings'],
    color: 'bg-purple-500'
  }
];

const AmbassadorsPage = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  
  const form = useForm<AmbassadorFormValues>({
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      experience: '',
      socialMedia: '',
      motivation: ''
    }
  });
  
  useEffect(() => {
    // Animation for the cards and sections
    anime({
      targets: '.animate-card',
      opacity: [0, 1],
      translateY: [15, 0],
      delay: anime.stagger(120),
      easing: 'easeOutExpo',
      duration: 700
    });
    
    // Flag icon animation
    anime({
      targets: '.flag-icon',
      translateY: ['-5px', '0px', '-5px'],
      duration: 2000,
      direction: 'alternate',
      loop: true,
      easing: 'easeInOutQuad'
    });
  }, []);
  
  const onSubmit = (data: AmbassadorFormValues) => {
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      console.log(data);
      setIsSubmitting(false);
      setIsDialogOpen(false);
      
      // Show success toast
      toast({
        title: "Application Received!",
        description: "We'll review your ambassador application and get back to you soon.",
      });
    }, 1500);
  };
  
  return (
    <div className="container mx-auto px-4 py-6 pb-24 md:pb-6">
      <div className="mb-8 text-center">
        <div className="inline-block p-4 rounded-full bg-earnhub-red/10 mb-4">
          <Flag size={32} className="text-earnhub-red flag-icon" />
        </div>
        <h1 className="text-2xl md:text-3xl font-bold mb-2">Ambassador Program</h1>
        <p className="text-gray-600 max-w-lg mx-auto">
          Join our exclusive ambassador program and earn more while helping others succeed on EarnHub.
        </p>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button size="lg" className="mt-4">Become an Ambassador</Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Ambassador Application</DialogTitle>
              <DialogDescription>
                Fill out this form to apply for the EarnHub Ambassador Program. We'll review your application and get back to you within 48 hours.
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Your name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="Your email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Your phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="socialMedia"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Social Media Profiles</FormLabel>
                      <FormControl>
                        <Input placeholder="Links to your social media profiles" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="experience"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Relevant Experience</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Describe any relevant experience you have" 
                          {...field} 
                          className="min-h-24"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="motivation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Why do you want to be an ambassador?</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Tell us why you want to join our ambassador program" 
                          {...field} 
                          className="min-h-24"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Submitting..." : "Submit Application"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
      
      <Tabs defaultValue="benefits" className="mb-8">
        <TabsList className="mb-6 w-full grid grid-cols-3">
          <TabsTrigger value="benefits">Benefits</TabsTrigger>
          <TabsTrigger value="levels">Ambassador Levels</TabsTrigger>
          <TabsTrigger value="ambassadors">Meet Ambassadors</TabsTrigger>
        </TabsList>
        
        <TabsContent value="benefits">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockBenefits.map((benefit) => {
              const IconComponent = iconMap[benefit.icon];
              return (
                <Card key={benefit._id} className="animate-card">
                  <CardHeader className="pb-2">
                    <div className="h-10 w-10 rounded-full bg-earnhub-red/10 flex items-center justify-center mb-3">
                      {IconComponent && <IconComponent className="h-5 w-5 text-earnhub-red" />}
                    </div>
                    <CardTitle className="text-lg">{benefit.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">{benefit.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
        
        <TabsContent value="levels">
          <div className="space-y-6">
            {ambassadorLevels.map((level, index) => (
              <Card key={level.name} className="animate-card">
                <CardHeader className="pb-3 flex flex-row items-center gap-4">
                  <div className={`h-12 w-12 rounded-full ${level.color} flex items-center justify-center text-white font-bold`}>
                    {index + 1}
                  </div>
                  <div>
                    <CardTitle>{level.name} Ambassador</CardTitle>
                    <CardDescription>Level {index + 1} Ambassador</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-2 text-gray-700">Requirements</h4>
                      <ul className="space-y-1">
                        {level.requirements.map((req, i) => (
                          <li key={i} className="flex items-start">
                            <Check className="h-4 w-4 text-earnhub-red mt-1 mr-2" />
                            <span className="text-gray-600">{req}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2 text-gray-700">Benefits</h4>
                      <ul className="space-y-1">
                        {level.benefits.map((ben, i) => (
                          <li key={i} className="flex items-start">
                            <Star className="h-4 w-4 text-earnhub-red mt-1 mr-2" />
                            <span className="text-gray-600">{ben}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="ambassadors">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {mockAmbassadors.map((ambassador) => (
              <Card key={ambassador._id} className={`animate-card ${ambassador.featured ? 'border-earnhub-red' : ''}`}>
                <CardHeader className="flex flex-row items-center gap-4">
                  <Avatar className="h-14 w-14">
                    {ambassador.avatar ? (
                      <AvatarImage src={ambassador.avatar} />
                    ) : (
                      <AvatarFallback>
                        <User size={24} />
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <div>
                    <CardTitle className="flex items-center">
                      {ambassador.name}
                      {ambassador.featured && (
                        <Badge variant="outline" className="ml-2 bg-earnhub-red/10 text-earnhub-red border-earnhub-red/20">
                          Featured
                        </Badge>
                      )}
                    </CardTitle>
                    <CardDescription>{ambassador.title}</CardDescription>
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="flex flex-wrap gap-1 mb-3">
                    {ambassador.achievements.map((achievement, idx) => (
                      <Badge key={idx} variant="secondary" className="text-xs">
                        {achievement}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Users className="h-4 w-4 mr-1" />
                    <span>Managing {ambassador.members} members</span>
                  </div>
                  {ambassador.testimonial && (
                    <div className="mt-3 text-sm italic text-gray-600">
                      "{ambassador.testimonial}"
                    </div>
                  )}
                </CardContent>
                <CardFooter>
                  <Badge variant="outline" className={`
                    ${ambassador.level === 'Diamond' ? 'bg-purple-100 text-purple-800 border-purple-200' : 
                      ambassador.level === 'Platinum' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                      ambassador.level === 'Gold' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                      ambassador.level === 'Silver' ? 'bg-gray-100 text-gray-800 border-gray-200' :
                      'bg-amber-100 text-amber-800 border-amber-200'}
                  `}>
                    {ambassador.level} Level
                  </Badge>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
      
      <div className="mt-12">
        <Card className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-none animate-card">
          <CardContent className="py-8">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold mb-2">Ready to take your EarnHub experience to the next level?</h2>
              <p className="opacity-90 max-w-lg mx-auto">Become an ambassador and unlock exclusive benefits while building your network.</p>
            </div>
            <div className="flex justify-center">
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" className="bg-white text-purple-600 hover:bg-gray-100 hover:text-purple-700">
                    Apply Now
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Ambassador Application</DialogTitle>
                    <DialogDescription>
                      Fill out this form to apply for the EarnHub Ambassador Program.
                    </DialogDescription>
                  </DialogHeader>
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                      {/* Form fields (same as above) */}
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Full Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Your name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input type="email" placeholder="Your email" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                              <Input placeholder="Your phone number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="socialMedia"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Social Media Profiles</FormLabel>
                            <FormControl>
                              <Input placeholder="Links to your social media profiles" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="experience"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Relevant Experience</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Describe any relevant experience you have" 
                                {...field} 
                                className="min-h-24"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="motivation"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Why do you want to be an ambassador?</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Tell us why you want to join our ambassador program" 
                                {...field} 
                                className="min-h-24"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <DialogFooter>
                        <Button type="submit">Submit Application</Button>
                      </DialogFooter>
                    </form>
                  </Form>
                </DialogContent>
              </Dialog>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AmbassadorsPage;
