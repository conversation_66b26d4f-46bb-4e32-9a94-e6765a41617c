
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ChevronRight, Users, Award, CheckCircle, Coins } from 'lucide-react';

const Features = () => {
  const [activeTab, setActiveTab] = useState(0);
  
  const features = [
    {
      id: 0,
      icon: <Users className="w-6 h-6 text-earnhub-red" />,
      title: "Lucrative Referrals",
      description: "Earn KES 400-1,924 per referral depending on your membership tier. Our top referrers make KES 25,000+ monthly with NO LIMIT to how many people you can refer!",
      details: [
        "Up to 55% commission as a Gold member",
        "Real-time tracking of your network",
        "Instant earnings notifications",
        "Detailed analytics dashboard"
      ]
    },
    {
      id: 1,
      icon: <Award className="w-6 h-6 text-earnhub-red" />,
      title: "Exciting Competitions",
      description: "Join weekly contests with cash prizes up to KES 5,000! Compete in Referral Rush, Social Media Star, and more to win big. Some competitions are FREE to enter!",
      details: [
        "Weekly and monthly competitions",
        "Cash prizes and bonuses",
        "Live leaderboards",
        "Multiple competition types"
      ]
    },
    {
      id: 2,
      icon: <CheckCircle className="w-6 h-6 text-earnhub-red" />,
      title: "Simple Paid Tasks",
      description: "Complete quick tasks like app testing and social media sharing to earn KES 100+ per task plus bonus EarnCoins. Perfect for earning in your spare time!",
      details: [
        "5-15 minute tasks",
        "New tasks added daily",
        "No special skills required",
        "Instant payment upon completion"
      ]
    },
    {
      id: 3,
      icon: <Coins className="w-6 h-6 text-earnhub-red" />,
      title: "Exclusive Opportunities",
      description: "Access high-paying side hustles, freelance jobs, and premium income streams reserved for EarnHub members. Silver and Gold members get priority access to the best offers!",
      details: [
        "Ambassador program (earn +10% bonus)",
        "QA testing community",
        "Trading community with guides",
        "Custom online store creation"
      ]
    }
  ];

  return (
    <section id="features" className="py-20 px-6 md:px-10 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute bottom-0 right-0 w-[500px] h-[500px] bg-gradient-radial from-earnhub-lightGray to-transparent rounded-full"></div>
      </div>
      
      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 max-w-3xl mx-auto">
          <div className="inline-block px-4 py-1 rounded-full bg-earnhub-red/10 mb-4">
            <p className="text-earnhub-red font-medium text-sm">Multiple ways to earn</p>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold text-earnhub-dark mb-4 balance-text">
            How You Can Start Earning Today
          </h2>
          
          <p className="text-earnhub-darkGray text-lg balance-text">
            Our platform offers various methods to earn money online, whether you want to make <span className="font-medium">KES 5,000 extra</span> or build a <span className="font-medium">KES 50,000 monthly</span> income stream.
          </p>
        </div>
        
        {/* Features Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left - Feature Tabs */}
          <div className="space-y-6">
            {features.map((feature) => (
              <div 
                key={feature.id}
                onClick={() => setActiveTab(feature.id)}
                className={cn(
                  "p-6 rounded-xl cursor-pointer transition-all duration-300 hover-lift",
                  activeTab === feature.id 
                    ? "bg-white shadow-card border border-earnhub-red/10" 
                    : "bg-earnhub-lightGray hover:bg-white hover:shadow-sm"
                )}
              >
                <div className="flex items-start gap-4">
                  <div className={cn(
                    "p-3 rounded-lg transition-colors",
                    activeTab === feature.id ? "bg-earnhub-red/10" : "bg-white"
                  )}>
                    {feature.icon}
                  </div>
                  
                  <div>
                    <h3 className="text-xl font-semibold text-earnhub-dark mb-2">{feature.title}</h3>
                    <p className="text-earnhub-darkGray">{feature.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Right - Feature Details */}
          <div className="bg-white shadow-card rounded-2xl p-8 border border-earnhub-red/5 animate-fade-in">
            <div>
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-earnhub-red/10 rounded-lg">
                  {features[activeTab].icon}
                </div>
                <h3 className="text-2xl font-bold text-earnhub-dark">{features[activeTab].title}</h3>
              </div>
              
              <p className="text-earnhub-darkGray mb-8">
                {features[activeTab].description}
              </p>
              
              <div className="space-y-4 mb-8">
                {features[activeTab].details.map((detail, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-5 h-5 rounded-full bg-earnhub-red/10 flex items-center justify-center">
                      <div className="w-2 h-2 rounded-full bg-earnhub-red"></div>
                    </div>
                    <p className="text-earnhub-dark">{detail}</p>
                  </div>
                ))}
              </div>
              
              <Button className="bg-earnhub-red hover:bg-earnhub-red/90 text-white flex items-center gap-2 group">
                <span>Start earning now</span>
                <ChevronRight size={16} className="group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
