
import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, Card<PERSON><PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Calendar, User, ChevronRight, Briefcase, CheckCircle, AlertCircle, ExternalLink } from "lucide-react";
import anime from "animejs";
import { useToast } from "@/hooks/use-toast";

// This would come from your Sanity CMS in real implementation
interface Opportunity {
  id: string;
  title: string;
  description: string;
  type: string;
  category: string;
  reward: string;
  deadline: string;
  requirements: string[];
  company: string;
  status: "open" | "limited" | "closing-soon";
  applicationLink?: string;
  postedDate: string;
}

const OpportunityDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [opportunity, setOpportunity] = useState<Opportunity | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Animation for page elements
  useEffect(() => {
    // Simulated data fetch - replace with actual Sanity client fetch
    setTimeout(() => {
      setOpportunity({
        id: id || "1",
        title: "Content Writer for Tech Blog",
        description: "We're looking for creative writers to create engaging tech content for our blog. Topics include AI, mobile apps, and tech trends. Each article should be 1000-1500 words and include SEO keywords.",
        type: "freelance",
        category: "Content Creation",
        reward: "KES 2,500 per article",
        deadline: "2025-06-01",
        requirements: [
          "Excellent written English",
          "Knowledge of tech industry",
          "Previous writing experience",
          "Ability to meet deadlines"
        ],
        company: "TechHub Kenya",
        status: "open",
        applicationLink: "https://example.com/apply",
        postedDate: "2025-05-10"
      });
      
      setLoading(false);
      
      // Run animation after data is loaded
      anime({
        targets: '.animate-item',
        translateY: [20, 0],
        opacity: [0, 1],
        delay: anime.stagger(100),
        easing: 'easeOutExpo',
        duration: 800
      });
    }, 800);
  }, [id]);

  const handleApply = () => {
    toast({
      title: "Application Submitted!",
      description: "Your application has been sent to the opportunity provider.",
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "open":
        return <Badge className="bg-green-500">Open</Badge>;
      case "limited":
        return <Badge className="bg-yellow-500">Limited Spots</Badge>;
      case "closing-soon":
        return <Badge variant="destructive">Closing Soon</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20 flex justify-center items-center">
        <div className="animate-spin h-8 w-8 border-4 border-earnhub-red border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (!opportunity) {
    return (
      <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20">
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
            <h2 className="text-xl font-bold mb-2">Opportunity Not Found</h2>
            <p className="text-earnhub-darkGray mb-4">The opportunity you're looking for doesn't exist or has been removed.</p>
            <Button asChild>
              <Link to="/opportunities">View All Opportunities</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20">
      <div className="mb-6 flex items-center animate-item">
        <Link to="/opportunities" className="text-earnhub-darkGray flex items-center hover:text-earnhub-red">
          <Briefcase className="w-4 h-4 mr-1" /> Opportunities
        </Link>
        <ChevronRight className="w-4 h-4 mx-1 text-gray-400" />
        <span className="font-medium text-earnhub-dark">{opportunity.title}</span>
      </div>

      <Card className="mb-6 animate-item">
        <CardHeader>
          <div className="flex flex-wrap justify-between items-start gap-2">
            <div>
              <CardTitle className="text-2xl">{opportunity.title}</CardTitle>
              <CardDescription className="flex items-center mt-1">
                <User className="w-4 h-4 mr-1" /> {opportunity.company}
              </CardDescription>
            </div>
            <div className="flex flex-col items-end">
              {getStatusBadge(opportunity.status)}
              <span className="text-sm text-gray-500 mt-1">Posted: {opportunity.postedDate}</span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="animate-item">
            <h3 className="font-medium mb-2">Description</h3>
            <p className="text-earnhub-darkGray">{opportunity.description}</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 animate-item">
            <div className="p-4 bg-gray-50 rounded-md">
              <h4 className="text-sm font-medium text-gray-500 mb-1">Reward</h4>
              <p className="font-bold text-earnhub-red">{opportunity.reward}</p>
            </div>
            <div className="p-4 bg-gray-50 rounded-md">
              <h4 className="text-sm font-medium text-gray-500 mb-1">Category</h4>
              <p className="font-medium">{opportunity.category}</p>
            </div>
            <div className="p-4 bg-gray-50 rounded-md">
              <h4 className="text-sm font-medium text-gray-500 mb-1">Type</h4>
              <p className="font-medium capitalize">{opportunity.type}</p>
            </div>
          </div>
          
          <div className="animate-item">
            <h3 className="font-medium mb-2">Requirements</h3>
            <ul className="list-disc pl-5 space-y-1">
              {opportunity.requirements.map((req, index) => (
                <li key={index} className="text-earnhub-darkGray">{req}</li>
              ))}
            </ul>
          </div>
          
          <div className="flex items-center text-earnhub-darkGray border-t pt-4 border-gray-100 animate-item">
            <Calendar className="h-4 w-4 mr-1" />
            <span className="text-sm mr-4">Deadline: </span>
            <span className="font-medium">{opportunity.deadline}</span>
            
            {opportunity.status === "closing-soon" && (
              <Badge variant="outline" className="ml-3 border-red-200 text-red-600">
                <Clock className="w-3 h-3 mr-1" /> Ending Soon
              </Badge>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-100 animate-item">
          <Button className="w-full sm:w-auto bg-earnhub-red hover:bg-earnhub-red/90" onClick={handleApply}>
            Apply Now
          </Button>
          {opportunity.applicationLink && (
            <Button variant="outline" className="w-full sm:w-auto" asChild>
              <a href={opportunity.applicationLink} target="_blank" rel="noopener noreferrer">
                External Application <ExternalLink className="ml-1 h-4 w-4" />
              </a>
            </Button>
          )}
          <Button variant="ghost" className="w-full sm:w-auto" asChild>
            <Link to="/opportunities">View More Opportunities</Link>
          </Button>
        </CardFooter>
      </Card>
      
      <Card className="animate-item">
        <CardHeader>
          <CardTitle>Similar Opportunities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2].map((item) => (
              <Link key={item} to={`/opportunity/${item+1}`}>
                <Card className="hover:border-earnhub-red transition-colors cursor-pointer">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{item === 1 ? "Social Media Manager" : "Mobile App Tester"}</h3>
                        <p className="text-sm text-earnhub-darkGray">{item === 1 ? "TechHub Kenya" : "AppTest Limited"}</p>
                      </div>
                      <Badge>{item === 1 ? "KES 5,000" : "KES 1,200"}</Badge>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OpportunityDetail;
