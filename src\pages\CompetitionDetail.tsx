
import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { formatDistanceToNow, format } from 'date-fns';
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Trophy,
  Users,
  Calendar,
  ArrowLeft,
  Award,
  Flag,
  ListOrdered
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import CompetitionLeaderboard from '@/components/competitions/CompetitionLeaderboard';
import CompetitionRules from '@/components/competitions/CompetitionRules';
import { CompetitionType } from '@/types/competitions';

// Sample competitions data (this would come from Sanity CMS in production)
const competitionsData: CompetitionType[] = [
  {
    id: "1",
    title: "Summer Referral Challenge",
    description: "Refer the most friends and win amazing prizes! This competition rewards users who bring in the most new sign-ups to our platform. Invite your friends, family, and network to join EarnHub and climb the leaderboard.",
    type: "referral",
    image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b",
    startDate: new Date(2025, 4, 1).toISOString(),
    endDate: new Date(2025, 5, 15).toISOString(),
    participants: 128,
    prizes: [
      { position: 1, reward: "$500 Cash" },
      { position: 2, reward: "$250 Cash" },
      { position: 3, reward: "$100 Cash" },
      { position: 4, reward: "$50 Cash" },
      { position: 5, reward: "$25 Cash" }
    ],
    userPosition: 5,
    status: "active",
    rules: [
      "Each verified referral earns you 1 point",
      "Referrals must sign up and complete their profile",
      "Referrals must complete at least 1 task on the platform",
      "Leaderboard updates daily at midnight UTC",
      "Final winners will be announced within 48 hours of competition end"
    ]
  },
  {
    id: "2",
    title: "Coding Championship",
    description: "Show off your coding skills and compete with other developers in this exciting challenge. Complete coding tasks, solve problems, and earn points based on speed and accuracy.",
    type: "task",
    image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158",
    startDate: new Date(2025, 4, 10).toISOString(),
    endDate: new Date(2025, 6, 10).toISOString(),
    participants: 76,
    prizes: [
      { position: 1, reward: "MacBook Pro" },
      { position: 2, reward: "iPad Pro" },
      { position: 3, reward: "AirPods Pro" }
    ],
    userPosition: 12,
    status: "active",
    rules: [
      "Tasks will be released weekly",
      "Points awarded based on task difficulty and completion time",
      "All code must be original and pass plagiarism checks",
      "Participants must submit solutions through the platform",
      "Final ranking determined by total points accumulated"
    ]
  },
  {
    id: "3",
    title: "Summer Sales Challenge",
    description: "Achieve the highest sales in the platform and earn special commission bonuses. This competition is designed for our affiliate partners to boost their earnings while driving platform growth.",
    type: "sales",
    image: "https://images.unsplash.com/photo-1605810230434-7631ac76ec81",
    startDate: new Date(2025, 6, 1).toISOString(),
    endDate: new Date(2025, 8, 30).toISOString(),
    participants: 0,
    prizes: [
      { position: 1, reward: "10% Commission Bonus" },
      { position: 2, reward: "5% Commission Bonus" },
      { position: 3, reward: "3% Commission Bonus" }
    ],
    userPosition: null,
    status: "upcoming",
    rules: [
      "Only verified sales count towards competition totals",
      "Commission bonuses apply to all sales made during the competition period",
      "Minimum 5 sales required to qualify for prizes",
      "Sales must be from unique customers",
      "Commission bonuses will be paid within 30 days of competition end"
    ]
  },
  {
    id: "4",
    title: "Spring Referral Contest",
    description: "Our previous referral competition with amazing prizes that encouraged users to invite friends to join our platform.",
    type: "referral",
    image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6",
    startDate: new Date(2025, 2, 1).toISOString(),
    endDate: new Date(2025, 3, 30).toISOString(),
    participants: 215,
    prizes: [
      { position: 1, reward: "$300 Cash" },
      { position: 2, reward: "$150 Cash" },
      { position: 3, reward: "$75 Cash" }
    ],
    userPosition: 8,
    status: "past",
    rules: [
      "Each verified referral earned 1 point",
      "Referrals needed to sign up and complete their profile",
      "Referrals needed to complete at least 1 task on the platform",
      "Leaderboard updated daily at midnight UTC",
      "Final winners were announced within 48 hours of competition end"
    ]
  }
];

// Sample leaderboard entries
const getSampleLeaderboard = (competitionId: string, totalParticipants: number) => {
  const entries = [];
  for (let i = 1; i <= Math.min(totalParticipants, 50); i++) {
    entries.push({
      userId: `user-${i}`,
      username: `User${i}`,
      avatar: null,
      rank: i,
      score: Math.floor(Math.random() * 1000) + 500,
      isCurrentUser: i === 5 // hardcoding user position to 5 for this demo
    });
  }
  
  // Sort by score descending
  return entries.sort((a, b) => b.score - a.score);
};

const CompetitionDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [competition, setCompetition] = useState<CompetitionType | null>(null);
  const [leaderboard, setLeaderboard] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // In a real app, this would be an API call to get competition details
    const foundCompetition = competitionsData.find(c => c.id === id);
    
    if (foundCompetition) {
      setCompetition(foundCompetition);
      // Generate sample leaderboard data for this competition
      setLeaderboard(getSampleLeaderboard(foundCompetition.id, foundCompetition.participants));
    }
    
    setLoading(false);
  }, [id]);

  if (loading) {
    return (
      <div className="container mx-auto pt-24 pb-20 flex justify-center items-center">
        <p>Loading competition details...</p>
      </div>
    );
  }

  if (!competition) {
    return (
      <div className="container mx-auto pt-24 pb-20">
        <div className="text-center py-16">
          <h2 className="text-2xl font-bold mb-4">Competition not found</h2>
          <Button onClick={() => navigate('/competitions')}>
            Back to Competitions
          </Button>
        </div>
      </div>
    );
  }

  const getTypeColor = (type: string): string => {
    switch (type) {
      case 'referral':
        return 'bg-emerald-100 text-emerald-800';
      case 'sales':
        return 'bg-blue-100 text-blue-800';
      case 'task':
        return 'bg-amber-100 text-amber-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string): string => {
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  return (
    <div className="container mx-auto pt-24 pb-20">
      <div className="mb-8">
        <Button 
          variant="ghost" 
          onClick={() => navigate('/competitions')}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Competitions
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <div className="relative h-64 md:h-80 rounded-lg overflow-hidden mb-6">
            <img 
              src={competition.image} 
              alt={competition.title} 
              className="w-full h-full object-cover"
            />
            <div className="absolute top-4 left-4">
              <Badge className={`${getTypeColor(competition.type)} uppercase text-sm font-medium px-2.5 py-1`}>
                {competition.type}
              </Badge>
            </div>
          </div>

          <h1 className="text-3xl font-bold mb-4">{competition.title}</h1>
          
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="leaderboard">Leaderboard</TabsTrigger>
              <TabsTrigger value="rules">Rules</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview">
              <div className="space-y-6">
                <p className="text-gray-700">{competition.description}</p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Calendar className="h-5 w-5 text-gray-500 mr-2" />
                      <span className="text-sm font-medium">Start Date</span>
                    </div>
                    <p className="text-gray-700">{formatDate(competition.startDate)}</p>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Calendar className="h-5 w-5 text-gray-500 mr-2" />
                      <span className="text-sm font-medium">End Date</span>
                    </div>
                    <p className="text-gray-700">{formatDate(competition.endDate)}</p>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Users className="h-5 w-5 text-gray-500 mr-2" />
                      <span className="text-sm font-medium">Participants</span>
                    </div>
                    <p className="text-gray-700">{competition.participants}</p>
                  </div>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Trophy className="h-5 w-5 text-gray-500 mr-2" />
                    Prizes
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {competition.prizes.map((prize) => (
                      <div 
                        key={prize.position} 
                        className={`p-4 rounded-lg border ${
                          prize.position === 1 
                            ? 'bg-amber-50 border-amber-200' 
                            : prize.position === 2 
                            ? 'bg-gray-50 border-gray-200'
                            : prize.position === 3
                            ? 'bg-orange-50 border-orange-200'
                            : 'bg-white border-gray-200'
                        }`}
                      >
                        <div className="text-center">
                          <div className="mb-2">
                            {prize.position === 1 ? (
                              <span className="text-3xl">🏆</span>
                            ) : prize.position === 2 ? (
                              <span className="text-3xl">🥈</span>
                            ) : prize.position === 3 ? (
                              <span className="text-3xl">🥉</span>
                            ) : (
                              <span className="text-lg font-bold">{prize.position}th</span>
                            )}
                          </div>
                          <p className="font-medium text-gray-900">{prize.reward}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="leaderboard">
              <CompetitionLeaderboard 
                leaderboard={leaderboard} 
                competition={competition} 
              />
            </TabsContent>
            
            <TabsContent value="rules">
              <CompetitionRules 
                competition={competition} 
              />
            </TabsContent>
          </Tabs>
        </div>
        
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-gray-50 p-6 rounded-lg mb-6">
            <h3 className="text-lg font-semibold mb-4">Competition Status</h3>
            
            {competition.status === 'active' && (
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-600">Time Remaining</span>
                  </div>
                  <p className="font-medium">{formatDistanceToNow(new Date(competition.endDate))}</p>
                </div>
                
                {competition.userPosition && (
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-600">Your Position</span>
                    </div>
                    <p className="font-medium">{competition.userPosition}{competition.userPosition === 1 ? 'st' : competition.userPosition === 2 ? 'nd' : competition.userPosition === 3 ? 'rd' : 'th'} place</p>
                  </div>
                )}
                
                <Button className="w-full">
                  View Your Progress
                </Button>
              </div>
            )}
            
            {competition.status === 'upcoming' && (
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-600">Starts In</span>
                  </div>
                  <p className="font-medium">{formatDistanceToNow(new Date(competition.startDate))}</p>
                </div>
                
                <Button className="w-full">
                  Set Reminder
                </Button>
              </div>
            )}
            
            {competition.status === 'past' && (
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-600">Ended</span>
                  </div>
                  <p className="font-medium">{formatDistanceToNow(new Date(competition.endDate), { addSuffix: true })}</p>
                </div>
                
                {competition.userPosition && (
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-600">Your Final Position</span>
                    </div>
                    <p className="font-medium">{competition.userPosition}{competition.userPosition === 1 ? 'st' : competition.userPosition === 2 ? 'nd' : competition.userPosition === 3 ? 'rd' : 'th'} place</p>
                  </div>
                )}
              </div>
            )}
          </div>
          
          <div className="bg-gray-50 p-6 rounded-lg mb-6">
            <div className="flex items-center mb-4">
              <Flag className="h-5 w-5 text-gray-500 mr-2" />
              <h3 className="text-lg font-semibold">Quick Facts</h3>
            </div>
            
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-sm text-gray-600 flex-grow">Type</span>
                <span className="text-sm font-medium">{competition.type.charAt(0).toUpperCase() + competition.type.slice(1)}</span>
              </li>
              <li className="flex items-start">
                <span className="text-sm text-gray-600 flex-grow">Duration</span>
                <span className="text-sm font-medium">
                  {formatDistanceToNow(new Date(competition.startDate), { addSuffix: false })}
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-sm text-gray-600 flex-grow">Total Prizes</span>
                <span className="text-sm font-medium">{competition.prizes.length}</span>
              </li>
              <li className="flex items-start">
                <span className="text-sm text-gray-600 flex-grow">Grand Prize</span>
                <span className="text-sm font-medium">{competition.prizes[0]?.reward || 'N/A'}</span>
              </li>
            </ul>
          </div>
          
          <div className="bg-gray-50 p-6 rounded-lg">
            <div className="flex items-center mb-4">
              <ListOrdered className="h-5 w-5 text-gray-500 mr-2" />
              <h3 className="text-lg font-semibold">Top Competitors</h3>
            </div>
            
            <ul className="space-y-2">
              {leaderboard.slice(0, 5).map((entry, index) => (
                <li key={entry.userId} className="flex items-center py-2">
                  <span className="w-8 text-center font-medium">{index + 1}</span>
                  <div className="w-8 h-8 rounded-full bg-gray-200 mx-2 flex items-center justify-center">
                    {entry.avatar ? (
                      <img 
                        src={entry.avatar} 
                        alt={entry.username} 
                        className="w-full h-full rounded-full"
                      />
                    ) : (
                      <span className="text-xs">{entry.username.substring(0, 2).toUpperCase()}</span>
                    )}
                  </div>
                  <span className="flex-grow font-medium">{entry.username}</span>
                  <span className="text-sm">{entry.score} pts</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompetitionDetail;
