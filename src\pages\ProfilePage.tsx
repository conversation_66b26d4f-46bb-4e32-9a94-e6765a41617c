
import { useState, useEffect } from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { User, Edit, Award, User as UserIcon, BarChart2 } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import anime from "animejs";
import ProfileCompletion from "@/components/profile/ProfileCompletion";

// Avatar selection options
const avatarOptions = [
  { id: 1, src: "https://images.unsplash.com/photo-1581090464777-f3220bbe1b8b?auto=format&fit=crop&w=150&h=150", alt: "Light bulb" },
  { id: 2, src: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=150&h=150", alt: "Kitten" },
  { id: 3, src: "https://images.unsplash.com/photo-1466721591366-2d5fba72006d?auto=format&fit=crop&w=150&h=150", alt: "Antelope" },
  { id: 4, src: "https://images.unsplash.com/photo-1501286353178-1ec871214838?auto=format&fit=crop&w=150&h=150", alt: "Monkey" },
  { id: 5, src: "https://images.unsplash.com/photo-1441057206919-63d19fac2369?auto=format&fit=crop&w=150&h=150", alt: "Penguins" },
];

const ProfilePage = () => {
  const [selectedAvatar, setSelectedAvatar] = useState(1);
  const [progress, setProgress] = useState(0);
  const [activeTab, setActiveTab] = useState("profile");

  useEffect(() => {
    // Animate profile components on load
    anime({
      targets: '.profile-section',
      opacity: [0, 1],
      translateY: [10, 0],
      delay: anime.stagger(100),
      easing: 'easeOutExpo',
      duration: 700
    });

    // Animate progress bar
    anime({
      targets: '.progress-bar',
      width: `${progress}%`,
      easing: 'easeInOutQuad',
      duration: 1000
    });

    // Mock data loading
    setProgress(65);
  }, []);

  // Mock user data (in a real app, this would come from Sanity CMS or a database)
  const userData = {
    name: "John Doe",
    email: "<EMAIL>",
    memberSince: "March 2023",
    membershipLevel: "Silver",
    totalEarnings: "KES 15,230",
    completedTasks: 24,
    joinedCompetitions: 7,
    referrals: 12,
    nextLevel: "Gold",
    pointsToNextLevel: 240,
    pointsEarned: 760,
    pointsRequired: 1000,
  };

  return (
    <div className="container mx-auto px-4 py-6 space-y-6 pb-24 md:pb-6">
      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="profile">My Profile</TabsTrigger>
          <TabsTrigger value="complete">Complete Profile</TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile" className="space-y-6">
          <div className="profile-section flex flex-col md:flex-row gap-6 items-start">
            {/* Profile Card */}
            <Card className="w-full md:w-1/3 profile-section">
              <CardHeader className="pb-2">
                <CardTitle>Profile</CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col items-center text-center">
                <Dialog>
                  <DialogTrigger asChild>
                    <div className="relative cursor-pointer group">
                      <Avatar className="w-24 h-24 mb-4">
                        <AvatarImage src={avatarOptions.find(a => a.id === selectedAvatar)?.src} />
                        <AvatarFallback>
                          <User size={32} />
                        </AvatarFallback>
                      </Avatar>
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 rounded-full flex items-center justify-center transition-all">
                        <Edit className="text-white opacity-0 group-hover:opacity-100 transition-opacity" size={20} />
                      </div>
                    </div>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Choose a profile avatar</DialogTitle>
                    </DialogHeader>
                    <div className="grid grid-cols-3 gap-4 py-4">
                      {avatarOptions.map((avatar) => (
                        <div 
                          key={avatar.id} 
                          className={`cursor-pointer rounded-md overflow-hidden border-2 transition-all ${
                            selectedAvatar === avatar.id ? 'border-earnhub-red scale-105' : 'border-transparent hover:border-gray-200'
                          }`}
                          onClick={() => setSelectedAvatar(avatar.id)}
                        >
                          <img 
                            src={avatar.src} 
                            alt={avatar.alt} 
                            className="w-full h-auto object-cover aspect-square"
                          />
                        </div>
                      ))}
                    </div>
                    <Button onClick={() => {}} className="w-full">Save Selection</Button>
                  </DialogContent>
                </Dialog>
                <h2 className="text-xl font-bold">{userData.name}</h2>
                <p className="text-gray-500">{userData.email}</p>
                <p className="text-sm mt-1">Member since {userData.memberSince}</p>
                <div className="mt-4 bg-earnhub-red/10 px-4 py-2 rounded-full text-earnhub-red font-medium">
                  {userData.membershipLevel} Member
                </div>
                <div className="mt-6 w-full">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Progress to {userData.nextLevel}</span>
                    <span>{userData.pointsEarned}/{userData.pointsRequired} points</span>
                  </div>
                  <Progress value={progress} className="progress-bar h-2" />
                  <p className="text-xs text-gray-500 mt-1">
                    Earn {userData.pointsToNextLevel} more points to reach {userData.nextLevel}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Stats and Details */}
            <div className="w-full md:w-2/3 space-y-6">
              <Card className="profile-section">
                <CardHeader className="pb-2">
                  <CardTitle>Account Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-lg font-bold text-earnhub-red">{userData.totalEarnings}</p>
                      <p className="text-sm text-gray-600">Total Earnings</p>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-lg font-bold text-earnhub-red">{userData.completedTasks}</p>
                      <p className="text-sm text-gray-600">Tasks Completed</p>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-lg font-bold text-earnhub-red">{userData.joinedCompetitions}</p>
                      <p className="text-sm text-gray-600">Competitions</p>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-lg font-bold text-earnhub-red">{userData.referrals}</p>
                      <p className="text-sm text-gray-600">Referrals</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Tabs defaultValue="personal" className="profile-section">
                <TabsList className="grid grid-cols-3 mb-4">
                  <TabsTrigger value="personal">Personal Info</TabsTrigger>
                  <TabsTrigger value="security">Security</TabsTrigger>
                  <TabsTrigger value="preferences">Preferences</TabsTrigger>
                </TabsList>
                <TabsContent value="personal">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="space-y-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Full Name</h4>
                          <p>{userData.name}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Email</h4>
                          <p>{userData.email}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Phone</h4>
                          <p>+254 712 345 678</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Country</h4>
                          <p>Kenya</p>
                        </div>
                        <Button className="w-full sm:w-auto" onClick={() => setActiveTab("complete")}>Complete Your Profile</Button>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="security">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="space-y-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Password</h4>
                          <p>********</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Two-Factor Authentication</h4>
                          <p>Not Enabled</p>
                        </div>
                        <Button className="w-full sm:w-auto">Change Password</Button>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                <TabsContent value="preferences">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="space-y-4">
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Email Notifications</h4>
                          <p>Enabled</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">SMS Notifications</h4>
                          <p>Disabled</p>
                        </div>
                        <Button className="w-full sm:w-auto">Update Preferences</Button>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>

          {/* Quick Links */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 profile-section">
            <Card className="hover:border-earnhub-red hover:bg-earnhub-red/5 transition-colors cursor-pointer">
              <CardContent className="flex items-center p-4">
                <Award className="h-10 w-10 text-earnhub-red mr-4" />
                <div>
                  <h3 className="font-medium">View Achievements</h3>
                  <p className="text-sm text-gray-500">Track your progress</p>
                </div>
              </CardContent>
            </Card>
            <Card className="hover:border-earnhub-red hover:bg-earnhub-red/5 transition-colors cursor-pointer">
              <CardContent className="flex items-center p-4">
                <UserIcon className="h-10 w-10 text-earnhub-red mr-4" />
                <div>
                  <h3 className="font-medium">Ambassador Program</h3>
                  <p className="text-sm text-gray-500">Apply to join</p>
                </div>
              </CardContent>
            </Card>
            <Card className="hover:border-earnhub-red hover:bg-earnhub-red/5 transition-colors cursor-pointer">
              <CardContent className="flex items-center p-4">
                <BarChart2 className="h-10 w-10 text-earnhub-red mr-4" />
                <div>
                  <h3 className="font-medium">Referral Dashboard</h3>
                  <p className="text-sm text-gray-500">View your statistics</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="complete">
          <ProfileCompletion />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ProfilePage;
