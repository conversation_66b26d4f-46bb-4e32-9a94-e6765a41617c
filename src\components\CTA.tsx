
import { Button } from '@/components/ui/button';
import { ArrowR<PERSON>, Check } from 'lucide-react';

const CTA = () => {
  return (
    <section className="py-20 px-6 md:px-10 bg-gradient-to-r from-earnhub-red to-earnhub-dark text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 right-0 w-[300px] h-[300px] bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-[400px] h-[400px] bg-white/5 rounded-full blur-3xl"></div>
      </div>
      
      <div className="max-w-7xl mx-auto relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
          {/* Left Content */}
          <div className="animate-fade-in-right">
            <div className="inline-block px-4 py-1 rounded-full bg-white/10 mb-4">
              <p className="text-white font-medium text-sm">Limited time: First 100 signups only</p>
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold mb-6 balance-text">
              Don't Miss Your Chance to <span className="text-white bg-earnhub-red/30 px-2 py-0.5 rounded">Earn KES 1,000 Today</span>
            </h2>
            
            <p className="text-white/80 text-lg mb-6 max-w-lg balance-text">
              Our top earners make over <span className="font-bold">KES 50,000 monthly</span>. You could be earning your first KES 1,000 in the next 24 hours. What are you waiting for?
            </p>
            
            <ul className="space-y-3 mb-8">
              {['Sign up in under 5 minutes', 'No technical skills required', 'Withdraw earnings directly to M-Pesa'].map((item, i) => (
                <li key={i} className="flex items-center gap-2">
                  <Check size={20} className="text-white bg-earnhub-red/40 rounded-full p-1" />
                  <span>{item}</span>
                </li>
              ))}
            </ul>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-white text-earnhub-red hover:bg-white/90 font-bold text-lg px-8 relative overflow-hidden group">
                <span className="relative z-10 flex items-center">
                  Create Your Free Account
                  <ArrowRight size={18} className="ml-2 group-hover:translate-x-1 transition-transform" />
                </span>
                <span className="absolute inset-0 bg-earnhub-red scale-x-0 group-hover:scale-x-100 origin-left transition-transform duration-300"></span>
              </Button>
            </div>
            
            <p className="mt-4 text-white/70 text-sm">
              * No credit card required. Join for free and start earning immediately.
            </p>
          </div>
          
          {/* Right Content - Stats */}
          <div className="grid grid-cols-2 gap-6 animate-fade-in-left">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover-lift border border-white/10">
              <p className="text-4xl font-bold mb-2">2,000+</p>
              <p className="text-white/80">Active Members</p>
              <div className="mt-3 bg-white/20 h-1.5 rounded-full w-full overflow-hidden">
                <div className="bg-white h-full rounded-full w-[75%]"></div>
              </div>
              <p className="text-white/60 text-xs mt-1">75% earn in their first week</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover-lift border border-white/10">
              <p className="text-4xl font-bold mb-2">KES 400-1,924</p>
              <p className="text-white/80">Per Referral</p>
              <div className="mt-3 bg-white/20 h-1.5 rounded-full w-full overflow-hidden">
                <div className="bg-white h-full rounded-full w-[90%]"></div>
              </div>
              <p className="text-white/60 text-xs mt-1">Based on your membership tier</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover-lift border border-white/10">
              <p className="text-4xl font-bold mb-2">24/7</p>
              <p className="text-white/80">Earning Opportunities</p>
              <div className="mt-3 bg-white/20 h-1.5 rounded-full w-full overflow-hidden">
                <div className="bg-white h-full rounded-full w-[85%]"></div>
              </div>
              <p className="text-white/60 text-xs mt-1">Work on your own schedule</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover-lift border border-white/10">
              <p className="text-4xl font-bold mb-2">5 min</p>
              <p className="text-white/80">Quick Sign Up</p>
              <div className="mt-3 bg-white/20 h-1.5 rounded-full w-full overflow-hidden">
                <div className="bg-white h-full rounded-full w-[95%]"></div>
              </div>
              <p className="text-white/60 text-xs mt-1">Free to join, no hidden fees</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTA;
