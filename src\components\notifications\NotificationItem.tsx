
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { 
  Bell, 
  AlertCircle, 
  CheckCircle, 
  Trophy, 
  Clock, 
  Info,
  Zap,
  Users,
  ThumbsUp,
  X
} from 'lucide-react';
import { Notification } from '@/types/notification';
import { useNotifications } from '@/hooks/use-notifications';
import { Badge } from '@/components/ui/badge';

interface NotificationItemProps {
  notification: Notification;
  showActions?: boolean;
}

export function NotificationItem({ notification, showActions = true }: NotificationItemProps) {
  const navigate = useNavigate();
  const { markAsRead, deleteNotification } = useNotifications();
  const [isHovered, setIsHovered] = useState(false);
  
  // Handle notification click
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
  };
  
  // Handle delete click
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    deleteNotification(notification.id);
  };
  
  // Get icon based on notification type
  const getIcon = () => {
    switch (notification.type) {
      case 'system':
        return <Bell className="h-5 w-5" />;
      case 'account':
        return <AlertCircle className="h-5 w-5" />;
      case 'competition':
        return <Trophy className="h-5 w-5" />;
      case 'task':
        return <CheckCircle className="h-5 w-5" />;
      case 'promotion':
        return <Zap className="h-5 w-5" />;
      case 'partner':
        return <Users className="h-5 w-5" />;
      case 'recommendation':
        return <ThumbsUp className="h-5 w-5" />;
      default:
        return <Info className="h-5 w-5" />;
    }
  };
  
  // Get priority color
  const getPriorityColor = () => {
    switch (notification.priority) {
      case 'high':
        return 'text-red-500';
      case 'medium':
        return 'text-amber-500';
      case 'low':
        return 'text-blue-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div
      className={`w-full px-4 py-3 cursor-pointer border-b transition-colors last:border-0 hover:bg-gray-50
        ${notification.isRead ? 'opacity-80' : 'bg-gray-50'}`}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-start gap-3">
        <div className={`mt-0.5 rounded-full p-1 ${getPriorityColor()} bg-gray-100`}>
          {getIcon()}
        </div>
        
        <div className="flex-1 space-y-1">
          <div className="flex items-start justify-between">
            <p className={`text-sm font-medium ${!notification.isRead && 'font-semibold'}`}>
              {notification.title}
            </p>
            
            {showActions && isHovered && (
              <button 
                onClick={handleDelete}
                className="text-gray-500 hover:text-red-500 transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
          
          <p className={`text-xs text-gray-500 line-clamp-2 ${!notification.isRead && 'text-gray-600'}`}>
            {notification.message}
          </p>
          
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-2">
              <Badge 
                variant="outline" 
                className="capitalize text-xs px-1.5 py-0"
              >
                {notification.type}
              </Badge>
              
              {!notification.isRead && (
                <span className="w-2 h-2 bg-earnhub-red rounded-full" />
              )}
            </div>
            
            <span className="text-xs text-gray-400">
              {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default NotificationItem;
