// sanity/schemaTypes/membershipTier.ts
import {Rule} from 'sanity'

export default {
  name: 'membershipTier',
  title: 'Membership Tier',
  type: 'document',
  fields: [
    {
      name: 'tierId',
      title: 'Tier ID',
      type: 'string',
      description: 'Unique identifier, e.g., starter, bronze, silver, gold',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'name',
      title: 'Tier Name',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'price',
      title: 'Price (KES)',
      type: 'number',
      validation: (Rule: Rule) => Rule.required().min(0),
    },
    {
      name: 'referralCommissionRate',
      title: 'Referral Commission Rate (%)',
      type: 'number',
      validation: (Rule: Rule) => Rule.required().min(0).max(100),
    },
    {
      name: 'taskAccessLevel',
      title: 'Task Access Level',
      type: 'string',
      options: {
        list: ['basic', 'enhanced', 'premium', 'vip'],
      },
    },
    {
      name: 'minWithdrawalAmount',
      title: 'Minimum Withdrawal Amount (KES)',
      type: 'number',
    },
    {
      name: 'withdrawalFees',
      title: 'Withdrawal Fees (%)',
      type: 'number',
      validation: (Rule: Rule) => Rule.min(0).max(100),
      initialValue: 0,
    },
    {
      name: 'customStoreEnabled',
      title: 'Custom Store Enabled',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'benefits',
      title: 'Other Benefits',
      type: 'array',
      of: [{type: 'string'}],
    },
    {
      name: 'order',
      title: 'Display Order',
      type: 'number',
      description: 'Order in which to display tiers, e.g., 1 for Starter, 2 for Bronze',
    },
    {
      name: 'isPublic',
      title: 'Is Publicly Visible',
      type: 'boolean',
      initialValue: true,
    },
  ],
}
