
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ArrowUpRight, BarChart3, Link, TrendingUp, Users, Wallet, DollarSign } from "lucide-react";
import PartnerDashboardLinksPage from "./PartnerDashboardLinksPage";
import { Partner, AffiliateTransaction } from "@/types/partner";

// Mock data for demonstration
const mockPartner: Partner = {
  _id: "partner1",
  userId: "user1",
  name: "<PERSON>",
  email: "<EMAIL>",
  dateJoined: "2023-01-15",
  tier: "basic",
  affiliateCode: "JOHN25",
  totalEarnings: 45000,
  unpaidEarnings: 12000,
  totalSales: 225000,
  status: "active"
};

const mockTransactions: AffiliateTransaction[] = [
  {
    _id: "tx1",
    orderId: "ord123",
    affiliateCode: "JOHN25",
    partnerId: "partner1",
    productId: "prod1",
    productTitle: "Premium WordPress Theme",
    saleAmount: 5000,
    commissionRate: 0.2,
    commissionAmount: 1000,
    status: "paid",
    transactionDate: "2023-05-15T10:30:00Z",
    payoutDate: "2023-05-20T14:25:00Z"
  },
  {
    _id: "tx2",
    orderId: "ord124",
    affiliateCode: "JOHN25",
    partnerId: "partner1",
    productId: "prod2",
    productTitle: "Business Website Package",
    saleAmount: 15000,
    commissionRate: 0.2,
    commissionAmount: 3000,
    status: "pending",
    transactionDate: "2023-05-22T15:45:00Z"
  },
  {
    _id: "tx3",
    orderId: "ord125",
    affiliateCode: "JOHN25",
    partnerId: "partner1",
    productId: "prod3",
    productTitle: "WhatsApp Bot for Businesses",
    saleAmount: 8000,
    commissionRate: 0.25,
    commissionAmount: 2000,
    status: "approved",
    transactionDate: "2023-05-25T09:15:00Z"
  },
  {
    _id: "tx4",
    orderId: "ord126",
    affiliateCode: "JOHN25",
    partnerId: "partner1",
    productId: "prod5",
    productTitle: "CV Revamping Service",
    saleAmount: 2500,
    commissionRate: 0.4,
    commissionAmount: 1000,
    status: "pending",
    transactionDate: "2023-05-28T11:20:00Z"
  }
];

const PartnerDashboardPage = () => {
  const [activeTab, setActiveTab] = useState("overview");
  
  // Mock query for partner data
  const { data: partner, isLoading: isLoadingPartner } = useQuery({
    queryKey: ['partner'],
    queryFn: () => Promise.resolve(mockPartner),
    staleTime: Infinity
  });
  
  // Mock query for transactions
  const { data: transactions, isLoading: isLoadingTransactions } = useQuery({
    queryKey: ['partnerTransactions'],
    queryFn: () => Promise.resolve(mockTransactions),
    staleTime: Infinity
  });
  
  if (isLoadingPartner || isLoadingTransactions) {
    return (
      <div className="container mx-auto px-4 py-8 pt-24 md:pt-8">
        <div className="flex flex-col items-center justify-center h-64">
          <p className="text-lg">Loading partner dashboard...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Partner Dashboard</h1>
          <p className="text-gray-500">Manage your affiliate marketing and earnings</p>
        </div>
        
        <div className="mt-4 md:mt-0 flex flex-col sm:flex-row items-start sm:items-center gap-2">
          <Badge variant="outline" className="text-sm px-3 py-1 rounded-full">
            {partner?.tier.charAt(0).toUpperCase() + partner?.tier.slice(1)} Tier
          </Badge>
          
          <span className="text-sm text-gray-500">
            Affiliate Code: <span className="font-mono font-medium">{partner?.affiliateCode}</span>
          </span>
        </div>
      </div>
      
      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-4 flex flex-col">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-gray-500">Total Earnings</p>
              <DollarSign className="h-4 w-4 text-earnhub-red opacity-70" />
            </div>
            <p className="text-2xl font-bold">KES {partner?.totalEarnings.toLocaleString()}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 flex flex-col">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-gray-500">Pending Payout</p>
              <Wallet className="h-4 w-4 text-earnhub-red opacity-70" />
            </div>
            <p className="text-2xl font-bold">KES {partner?.unpaidEarnings.toLocaleString()}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 flex flex-col">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-gray-500">Total Sales</p>
              <TrendingUp className="h-4 w-4 text-earnhub-red opacity-70" />
            </div>
            <p className="text-2xl font-bold">KES {partner?.totalSales.toLocaleString()}</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 flex flex-col">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium text-gray-500">Conversion Rate</p>
              <BarChart3 className="h-4 w-4 text-earnhub-red opacity-70" />
            </div>
            <p className="text-2xl font-bold">18.5%</p>
          </CardContent>
        </Card>
      </div>
      
      {/* Main Tabs */}
      <Tabs defaultValue={activeTab} value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-3 md:w-[400px]">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="links">Affiliate Links</TabsTrigger>
          <TabsTrigger value="payouts">Transactions</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Performance</CardTitle>
                <CardDescription>Your affiliate marketing performance this month</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Click-through rate</span>
                    <div className="flex items-center">
                      <span className="font-medium mr-2">5.2%</span>
                      <Badge variant="outline" className="flex items-center text-green-600">
                        <ArrowUpRight className="h-3 w-3 mr-1" /> 0.8%
                      </Badge>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Conversion rate</span>
                    <div className="flex items-center">
                      <span className="font-medium mr-2">18.5%</span>
                      <Badge variant="outline" className="flex items-center text-green-600">
                        <ArrowUpRight className="h-3 w-3 mr-1" /> 2.3%
                      </Badge>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Average order value</span>
                    <div className="flex items-center">
                      <span className="font-medium mr-2">KES 4,850</span>
                      <Badge variant="outline" className="flex items-center text-green-600">
                        <ArrowUpRight className="h-3 w-3 mr-1" /> KES 350
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Next Tier Requirements</CardTitle>
                <CardDescription>Requirements to reach Silver tier</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Sales Volume</span>
                      <span className="text-sm font-medium">KES 225,000 / KES 500,000</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-earnhub-red h-2 rounded-full" style={{ width: "45%" }}></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Active Months</span>
                      <span className="text-sm font-medium">3 / 6 months</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-earnhub-red h-2 rounded-full" style={{ width: "50%" }}></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Monthly Active Customers</span>
                      <span className="text-sm font-medium">8 / 10 customers</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-earnhub-red h-2 rounded-full" style={{ width: "80%" }}></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>Your most recent sales through affiliate links</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order ID</TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead className="text-right">Commission</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions?.slice(0, 5).map((tx) => (
                    <TableRow key={tx._id}>
                      <TableCell className="font-medium">{tx.orderId}</TableCell>
                      <TableCell>{tx.productTitle}</TableCell>
                      <TableCell className="text-right">KES {tx.saleAmount.toLocaleString()}</TableCell>
                      <TableCell className="text-right">KES {tx.commissionAmount.toLocaleString()}</TableCell>
                      <TableCell>{new Date(tx.transactionDate).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Badge variant={
                          tx.status === 'paid' ? 'default' :
                          tx.status === 'approved' ? 'outline' :
                          'secondary'
                        }>
                          {tx.status.charAt(0).toUpperCase() + tx.status.slice(1)}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="links">
          <PartnerDashboardLinksPage />
        </TabsContent>
        
        <TabsContent value="payouts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>
                All your earnings from affiliate sales
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order ID</TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead className="text-right">Sale Amount</TableHead>
                    <TableHead className="text-right">Commission</TableHead>
                    <TableHead className="text-right">Rate</TableHead>
                    <TableHead>Transaction Date</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions?.map((tx) => (
                    <TableRow key={tx._id}>
                      <TableCell className="font-medium">{tx.orderId}</TableCell>
                      <TableCell>{tx.productTitle}</TableCell>
                      <TableCell className="text-right">KES {tx.saleAmount.toLocaleString()}</TableCell>
                      <TableCell className="text-right">KES {tx.commissionAmount.toLocaleString()}</TableCell>
                      <TableCell className="text-right">{(tx.commissionRate * 100).toFixed(0)}%</TableCell>
                      <TableCell>{new Date(tx.transactionDate).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Badge variant={
                          tx.status === 'paid' ? 'default' :
                          tx.status === 'approved' ? 'outline' :
                          'secondary'
                        }>
                          {tx.status.charAt(0).toUpperCase() + tx.status.slice(1)}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Payout Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-1">Payout Method</h4>
                  <p className="text-sm">EarnHub Wallet</p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Payout Schedule</h4>
                  <p className="text-sm">Monthly (1st of each month)</p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Next Payout</h4>
                  <p className="text-sm">June 1, 2023</p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Pending Amount</h4>
                  <p className="text-lg font-bold">KES {partner?.unpaidEarnings.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PartnerDashboardPage;
