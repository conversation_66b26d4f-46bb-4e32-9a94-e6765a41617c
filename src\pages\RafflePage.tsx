
import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardDescription, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User, Clock, Award, Star } from "lucide-react";
import anime from "animejs";

// Mock data structure compatible with Sanity CMS
interface Raffle {
  _id: string;
  title: string;
  description: string;
  type: 'daily' | 'weekly' | 'monthly';
  prize: string;
  prizeAmount: number;
  currency: string;
  participants: number;
  endTime: string;
  status: 'active' | 'completed';
  winners?: {
    name: string;
    avatar?: string;
  }[];
}

// Mock raffles - in a real app would be fetched from Sanity CMS
const mockRaffles: Raffle[] = [
  {
    _id: '1',
    title: 'Daily Cash Giveaway',
    description: 'Join our daily raffle for a chance to win instant cash. Winners announced at midnight.',
    type: 'daily',
    prize: 'Cash',
    prizeAmount: 1000,
    currency: 'KES',
    participants: 127,
    endTime: '2025-05-13T23:59:59Z',
    status: 'active'
  },
  {
    _id: '2',
    title: 'Weekly Mega Draw',
    description: 'Higher stakes, bigger rewards! Join our weekly draw for a substantial cash prize.',
    type: 'weekly',
    prize: 'Cash',
    prizeAmount: 5000,
    currency: 'KES',
    participants: 432,
    endTime: '2025-05-18T23:59:59Z',
    status: 'active'
  },
  {
    _id: '3',
    title: 'Monthly Grand Prize',
    description: 'Our biggest raffle with the highest rewards. Limited entry, maximum returns!',
    type: 'monthly',
    prize: 'Cash',
    prizeAmount: 25000,
    currency: 'KES',
    participants: 1250,
    endTime: '2025-05-31T23:59:59Z',
    status: 'active'
  },
  {
    _id: '4',
    title: 'Yesterday\'s Draw',
    description: 'Results from yesterday\'s daily raffle.',
    type: 'daily',
    prize: 'Cash',
    prizeAmount: 1000,
    currency: 'KES',
    participants: 98,
    endTime: '2025-05-12T23:59:59Z',
    status: 'completed',
    winners: [
      { name: 'Jane Smith', avatar: 'https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=150&h=150' },
    ]
  }
];

// Function to calculate time remaining
function getTimeRemaining(endtime: string) {
  const total = new Date(endtime).getTime() - new Date().getTime();
  const hours = Math.floor((total / (1000 * 60 * 60)) % 24);
  const minutes = Math.floor((total / 1000 / 60) % 60);
  const seconds = Math.floor((total / 1000) % 60);
  
  return {
    total,
    hours,
    minutes,
    seconds
  };
}

// Raffle card component
const RaffleCard = ({ raffle, onJoin }: { raffle: Raffle, onJoin: (id: string) => void }) => {
  const [timeRemaining, setTimeRemaining] = useState(getTimeRemaining(raffle.endTime));
  
  useEffect(() => {
    if (raffle.status === 'active') {
      const timer = setInterval(() => {
        setTimeRemaining(getTimeRemaining(raffle.endTime));
      }, 1000);
      
      return () => clearInterval(timer);
    }
  }, [raffle.endTime, raffle.status]);
  
  const formatTime = (time: number) => time.toString().padStart(2, '0');
  
  return (
    <Card className="raffle-card h-full flex flex-col">
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg md:text-xl">{raffle.title}</CardTitle>
          <Badge variant={raffle.type === 'daily' ? 'default' : raffle.type === 'weekly' ? 'outline' : 'secondary'}>
            {raffle.type.charAt(0).toUpperCase() + raffle.type.slice(1)}
          </Badge>
        </div>
        <CardDescription>{raffle.description}</CardDescription>
      </CardHeader>
      
      <CardContent className="flex-grow">
        <div className="space-y-4">
          <div className="flex justify-between">
            <span className="text-sm text-gray-500">Prize</span>
            <span className="font-bold text-earnhub-red">{raffle.currency} {raffle.prizeAmount.toLocaleString()}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm text-gray-500">Participants</span>
            <span>{raffle.participants}</span>
          </div>
          
          {raffle.status === 'active' ? (
            <div className="mt-4 p-3 rounded-md bg-gray-50 text-center">
              <div className="text-xs text-gray-500 mb-1 flex items-center justify-center">
                <Clock size={14} className="mr-1" /> Time Remaining
              </div>
              <div className="text-lg font-mono font-medium">
                {formatTime(timeRemaining.hours)}:{formatTime(timeRemaining.minutes)}:{formatTime(timeRemaining.seconds)}
              </div>
            </div>
          ) : (
            raffle.winners && (
              <div className="mt-4 p-3 rounded-md bg-gray-50 text-center">
                <div className="text-xs text-gray-500 mb-2">Winner</div>
                {raffle.winners.map((winner, idx) => (
                  <div key={idx} className="flex items-center justify-center">
                    <Avatar className="h-8 w-8 mr-2">
                      {winner.avatar ? (
                        <AvatarImage src={winner.avatar} alt={winner.name} />
                      ) : (
                        <AvatarFallback>
                          <User size={16} />
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <span className="font-medium">{winner.name}</span>
                  </div>
                ))}
              </div>
            )
          )}
        </div>
      </CardContent>
      
      <CardFooter>
        {raffle.status === 'active' ? (
          <Button 
            className="w-full" 
            onClick={() => onJoin(raffle._id)}
          >
            Enter Raffle
          </Button>
        ) : (
          <Button variant="outline" className="w-full" disabled>
            Completed
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};

const RafflePage = () => {
  const [joinedRaffles, setJoinedRaffles] = useState<string[]>([]);
  
  useEffect(() => {
    // Animation for raffle cards
    anime({
      targets: '.raffle-card',
      scale: [0.95, 1],
      opacity: [0, 1],
      delay: anime.stagger(100),
      easing: 'easeOutExpo',
      duration: 800
    });
    
    // Star animation
    anime({
      targets: '.star-icon',
      rotate: '1turn',
      duration: 5000,
      loop: true,
      easing: 'linear'
    });
  }, []);
  
  const handleJoinRaffle = (id: string) => {
    setJoinedRaffles(prev => [...prev, id]);
    // In a real app, this would make an API call to join the raffle
  };
  
  const filterRaffles = (type: 'all' | 'daily' | 'weekly' | 'monthly' | 'completed') => {
    if (type === 'all') {
      return mockRaffles.filter(raffle => raffle.status === 'active');
    } else if (type === 'completed') {
      return mockRaffles.filter(raffle => raffle.status === 'completed');
    } else {
      return mockRaffles.filter(raffle => raffle.type === type && raffle.status === 'active');
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-6 pb-24 md:pb-6">
      <div className="mb-8 text-center">
        <div className="inline-block p-4 rounded-full bg-earnhub-red/10 mb-4">
          <Star size={32} className="text-earnhub-red star-icon" />
        </div>
        <h1 className="text-2xl md:text-3xl font-bold mb-2">Daily Raffles & Giveaways</h1>
        <p className="text-gray-600 max-w-lg mx-auto">
          Join our exciting raffles for a chance to win cash prizes. New opportunities every day!
        </p>
      </div>
      
      <Card className="mb-8 bg-gradient-to-r from-blue-500 to-purple-600 text-white border-none">
        <CardContent className="py-6">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0">
              <h2 className="text-xl font-bold mb-2">Monthly Grand Prize</h2>
              <p className="opacity-90">Don't miss our biggest prize pool of KES 25,000!</p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-1">KES 25,000</div>
              <Badge variant="outline" className="bg-white/20 border-none">
                31 participants so far
              </Badge>
            </div>
            <Button variant="outline" className="bg-white text-blue-600 hover:bg-gray-100 hover:text-blue-700 mt-4 md:mt-0">
              Enter Now
            </Button>
          </div>
        </CardContent>
      </Card>
      
      <Tabs defaultValue="all">
        <TabsList className="mb-6 w-full grid grid-cols-5">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="daily">Daily</TabsTrigger>
          <TabsTrigger value="weekly">Weekly</TabsTrigger>
          <TabsTrigger value="monthly">Monthly</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
        </TabsList>
        
        {['all', 'daily', 'weekly', 'monthly', 'completed'].map((tab) => (
          <TabsContent key={tab} value={tab}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filterRaffles(tab as any).map(raffle => (
                <RaffleCard 
                  key={raffle._id} 
                  raffle={raffle} 
                  onJoin={handleJoinRaffle} 
                />
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
      
      <div className="mt-12 bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">How Raffles Work</h3>
        <ul className="space-y-3">
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Award className="h-4 w-4 text-white" />
            </div>
            <span>Enter any active raffle to participate in the draw.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Award className="h-4 w-4 text-white" />
            </div>
            <span>Each raffle has its own prize pool and deadline.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Award className="h-4 w-4 text-white" />
            </div>
            <span>Winners are selected randomly and notified immediately.</span>
          </li>
          <li className="flex items-start">
            <div className="bg-earnhub-red rounded-full p-1 mr-3 mt-1">
              <Award className="h-4 w-4 text-white" />
            </div>
            <span>Prizes are credited to your EarnHub wallet instantly.</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default RafflePage;
