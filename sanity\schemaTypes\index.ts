
import product from './product'
import productCategory from './productCategory'
import productNews from './productNews'
import partnerProgram from './partnerProgram'
import affiliateLink from './affiliateLink'
import affiliateTransaction from './affiliateTransaction'
import partner from './partner'
import notification from './notification'
import notificationPreference from './notificationPreference'
import achievementType from './achievementType'
import membershipTier from './membershipTier'
import profileAvatar from './profileAvatar'
import skillCategory from './skillCategory'
import opportunity from './opportunity'
import company from './company'
import competition from './competition'
import quest from './quest'
import raffle from './raffle'
import newsAndUpdate from './newsAndUpdate'
import liveActivityDefinition from './liveActivityDefinition'
import testimonial from './testimonial'
import faqItem from './faqItem'
import platformBenefit from './platformBenefit'
import quickAccessItem from './quickAccessItem'

export const schemaTypes = [
  product,
  productCategory,
  productNews,
  partnerProgram,
  affiliateLink,
  affiliateTransaction,
  partner,
  notification,
  notificationPreference,
  achievementType,
  membershipTier,
  profileAvatar,
  skillCategory,
  opportunity,
  company,
  competition,
  quest,
  raffle,
  newsAndUpdate,
  liveActivityDefinition,
  testimonial,
  faqItem,
  platformBenefit,
  quickAccessItem,
]
