
import { cn } from '@/lib/utils';

const TrustBadges = () => {
  const badges = [
    {
      icon: "🔒",
      title: "Secure Payments",
      description: "M-Pesa integrated & protected"
    },
    {
      icon: "🛡️",
      title: "Privacy Protected",
      description: "Your data stays private"
    },
    {
      icon: "✅",
      title: "Verified Opportunities",
      description: "All earning methods pre-screened"
    },
    {
      icon: "🔄",
      title: "Fast Withdrawals",
      description: "Get paid within 24 hours"
    }
  ];

  return (
    <section className="py-8 bg-white border-y border-earnhub-lightGray">
      <div className="max-w-7xl mx-auto px-6 md:px-10">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {badges.map((badge, index) => (
            <div key={index} className="flex flex-col items-center text-center hover:transform hover:scale-105 transition-transform">
              <div className="text-4xl mb-2">{badge.icon}</div>
              <h3 className="text-earnhub-dark font-medium mb-1">{badge.title}</h3>
              <p className="text-earnhub-darkGray text-sm">{badge.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TrustBadges;
