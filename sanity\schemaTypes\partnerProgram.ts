
export default {
  name: 'partner<PERSON>rogram',
  title: 'Partner Program',
  type: 'document',
  fields: [
    {
      name: 'name',
      title: 'Program Name',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'description',
      title: 'Program Description',
      type: 'text',
      validation: Rule => Rule.required()
    },
    {
      name: 'active',
      title: 'Program Active',
      type: 'boolean',
      initialValue: true
    },
    {
      name: 'terms',
      title: 'Terms & Conditions',
      type: 'text',
      validation: Rule => Rule.required()
    },
    {
      name: 'tiers',
      title: 'Partnership Tiers',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'name',
              title: 'Tier Name',
              type: 'string',
              validation: Rule => Rule.required()
            },
            {
              name: 'level',
              title: 'Tier Level',
              type: 'string',
              options: {
                list: [
                  { title: 'Basic', value: 'basic' },
                  { title: 'Silver', value: 'silver' },
                  { title: 'Gold', value: 'gold' },
                  { title: 'Platinum', value: 'platinum' }
                ]
              },
              validation: Rule => Rule.required()
            },
            {
              name: 'baseCommissionBonus',
              title: 'Commission Bonus Percentage',
              type: 'number',
              validation: Rule => Rule.min(0).max(100)
            },
            {
              name: 'requirements',
              title: 'Requirements',
              type: 'array',
              of: [{ type: 'string' }]
            },
            {
              name: 'benefits',
              title: 'Benefits',
              type: 'array',
              of: [{ type: 'string' }]
            },
            {
              name: 'minimumSales',
              title: 'Minimum Sales Amount (KES)',
              type: 'number',
              validation: Rule => Rule.min(0)
            }
          ]
        }
      ]
    }
  ]
}
