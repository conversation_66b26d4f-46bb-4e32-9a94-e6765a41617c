// sanity/schemaTypes/profileAvatar.ts
import {Rule} from 'sanity'

export default {
  name: 'profileAvatar',
  title: 'Profile Avatar',
  type: 'document',
  fields: [
    {
      name: 'name',
      title: 'Avatar Name',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'image',
      title: 'Avatar Image',
      type: 'image',
      options: {
        hotspot: true,
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'isDefault',
      title: 'Is Default Avatar',
      type: 'boolean',
      initialValue: false,
    },
  ],
}
