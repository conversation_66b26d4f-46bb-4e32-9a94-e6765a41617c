
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { LogOut, Menu } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
  SidebarTrigger,
  useSidebar
} from "@/components/ui/sidebar";
import { useToast } from "@/hooks/use-toast";
import { navigationItems } from "@/config/navigation";

const DesktopSidebar = () => {
  const location = useLocation();
  const { toast } = useToast();
  const { state, toggleSidebar } = useSidebar();

  const handleLogout = () => {
    toast({
      title: "Logged out",
      description: "You have been successfully logged out",
    });
  };

  // Button to reopen sidebar when collapsed
  const ReopenSidebarButton = () => {
    if (state !== "collapsed") return null;
    
    return (
      <Button
        variant="outline"
        size="icon"
        className="fixed left-4 top-4 z-50 bg-white shadow-md md:flex hidden"
        onClick={toggleSidebar}
      >
        <Menu size={20} />
        <span className="sr-only">Open sidebar</span>
      </Button>
    );
  };

  const profileItem = navigationItems.find(item => item.name === "Profile");

  return (
    <>
      <ReopenSidebarButton />
      <Sidebar className="hidden md:flex">
        <SidebarHeader className="flex items-center justify-between">
          <div className={cn("flex items-center px-2", state === "collapsed" ? "justify-center" : "")}>
            <div className="text-xl font-bold">
              {state !== "collapsed" ? (
                <>
                  <span className="text-earnhub-red">Earn</span>
                  <span className="text-earnhub-dark">Hub</span>
                </>
              ) : (
                <span className="text-earnhub-red">E</span>
              )}
            </div>
          </div>
          <SidebarTrigger />
        </SidebarHeader>
        
        <SidebarContent>
          <div className="mb-4">
            {state !== "collapsed" && (
              <div className="flex flex-col items-center justify-center py-4 border-b border-gray-200 mb-2">
                <div className="w-16 h-16 rounded-full bg-earnhub-red/10 flex items-center justify-center mb-2">
                  {profileItem?.icon && (
                    <profileItem.icon size={30} className="text-earnhub-red" />
                  )}
                </div>
                <h3 className="font-medium text-sm">Username</h3>
                <p className="text-xs text-earnhub-darkGray">Basic Member</p>
              </div>
            )}
            
            <SidebarMenu>
              {navigationItems.map((item) => {
                const isActive = location.pathname === item.path;
                
                return (
                  <SidebarMenuItem key={item.name}>
                    <SidebarMenuButton 
                      asChild 
                      isActive={isActive}
                      tooltip={state === "collapsed" ? item.name : undefined}
                    >
                      <Link to={item.path} className={cn(
                        "w-full",
                        isActive && "font-medium"
                      )}>
                        <item.icon className={cn(
                          "text-earnhub-darkGray",
                          isActive && "text-earnhub-red"
                        )} />
                        <span>{item.name}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </div>
        </SidebarContent>
        
        <SidebarFooter>
          <SidebarSeparator />
          <div className="p-2">
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start text-earnhub-darkGray hover:text-earnhub-red",
                state === "collapsed" && "justify-center"
              )}
              onClick={handleLogout}
            >
              <LogOut size={20} />
              {state !== "collapsed" && <span className="ml-2">Log Out</span>}
            </Button>
          </div>
        </SidebarFooter>
      </Sidebar>
    </>
  );
};

export default DesktopSidebar;
