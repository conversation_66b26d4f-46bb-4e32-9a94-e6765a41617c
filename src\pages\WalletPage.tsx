import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Badge } from "@/components/ui/badge";
import { 
  Wall<PERSON>, ArrowDownCircle, ArrowUpCircle, Clock, CheckCircle, XCircle, 
  Info, CircleDollarSign, Copy, ChevronRight, AlertCircle
} from "lucide-react";
import anime from "animejs";

// Form schemas
const MobileMoneyForm = z.object({
  phoneNumber: z.string().min(10, "Phone number must be at least 10 digits").max(12, "Phone number cannot exceed 12 digits"),
  amount: z.string().min(1, "Amount is required").refine(
    (val) => !isNaN(Number(val)) && Number(val) >= 500, 
    { message: "Minimum deposit amount is KES 500" }
  )
});

const CryptoDepositForm = z.object({
  cryptoType: z.enum(["USDT", "PI"]),
  amount: z.string().min(1, "Amount is required").refine(
    (val) => !isNaN(Number(val)) && Number(val) >= 500, 
    { message: "Minimum deposit amount is KES 500" }
  )
});

const WithdrawalForm = z.object({
  method: z.enum(["mpesa", "airtel", "paypal", "crypto", "bank"]),
  amount: z.string().min(1, "Amount is required").refine(
    (val) => !isNaN(Number(val)) && Number(val) >= 1000, 
    { message: "Minimum withdrawal amount is KES 1,000" }
  ),
  accountDetails: z.string().min(1, "Account details are required"),
  withdrawalSpeed: z.enum(["standard", "express"])
});

const BankForm = z.object({
  bankName: z.string().min(1, "Bank name is required"),
  accountNumber: z.string().min(1, "Account number is required"),
  beneficiaryName: z.string().min(1, "Beneficiary name is required")
});
import { useWallet } from "@/hooks/useWallet";
import { useAuth } from "@/contexts/AuthContext";

const WalletPage = () => {
  const { wallet, transactions, loading: walletLoading } = useWallet();
  const { user } = useAuth();
  const [depositDialogOpen, setDepositDialogOpen] = useState(false);
  const [withdrawDialogOpen, setWithdrawDialogOpen] = useState(false);
  const [depositType, setDepositType] = useState("mpesa");
  const [withdrawalMethod, setWithdrawalMethod] = useState<"mpesa" | "airtel" | "paypal" | "crypto" | "bank">("mpesa");
  const [transactionFilter, setTransactionFilter] = useState("all");
  const { toast } = useToast();

  const mobileMoneyForm = useForm<z.infer<typeof MobileMoneyForm>>({
    resolver: zodResolver(MobileMoneyForm),
    defaultValues: {
      phoneNumber: "",
      amount: ""
    }
  });

  const cryptoDepositForm = useForm<z.infer<typeof CryptoDepositForm>>({
    resolver: zodResolver(CryptoDepositForm),
    defaultValues: {
      cryptoType: "USDT",
      amount: ""
    }
  });

  const withdrawalForm = useForm<z.infer<typeof WithdrawalForm>>({
    resolver: zodResolver(WithdrawalForm),
    defaultValues: {
      method: "mpesa",
      amount: "",
      accountDetails: "",
      withdrawalSpeed: "standard"
    }
  });

  const bankForm = useForm<z.infer<typeof BankForm>>({
    resolver: zodResolver(BankForm),
    defaultValues: {
      bankName: "",
      accountNumber: "",
      beneficiaryName: ""
    }
  });

  useEffect(() => {
    // Reset forms when dialog closes
    if (!depositDialogOpen) {
      mobileMoneyForm.reset();
      cryptoDepositForm.reset();
    }
    if (!withdrawDialogOpen) {
      withdrawalForm.reset();
      bankForm.reset();
    }
  }, [depositDialogOpen, withdrawDialogOpen]);

  // Animation effect
  useEffect(() => {
    anime({
      targets: '.wallet-card',
      translateY: [15, 0],
      opacity: [0, 1],
      delay: anime.stagger(120),
      easing: 'easeOutExpo',
      duration: 700
    });

    anime({
      targets: '.transaction-item',
      translateX: [10, 0],
      opacity: [0, 1],
      delay: anime.stagger(80),
      easing: 'easeOutExpo',
      duration: 500
    });
  }, [transactionFilter]);

  // Handle deposit form submissions
  const onMobileMoneySubmit = (data: z.infer<typeof MobileMoneyForm>) => {
    toast({
      title: "Deposit initiated",
      description: `Processing deposit of KES ${data.amount} via M-Pesa`
    });
    setTimeout(() => {
      toast({
        title: "Deposit successful",
        description: `KES ${data.amount} has been added to your wallet`
      });
      setDepositDialogOpen(false);
    }, 2000);
  };

  const onCryptoDepositSubmit = (data: z.infer<typeof CryptoDepositForm>) => {
    toast({
      title: "Crypto deposit instructions sent",
      description: `Please follow the instructions to deposit ${data.amount} via ${data.cryptoType}`
    });
    setDepositDialogOpen(false);
  };

  // Handle withdrawal form submission
  const onWithdrawalSubmit = (data: z.infer<typeof WithdrawalForm>) => {
    // If bank method is selected, validate bank form first
    if (data.method === 'bank') {
      const bankState = bankForm.getValues();
      if (!bankState.bankName || !bankState.accountNumber || !bankState.beneficiaryName) {
        toast({
          title: "Missing bank details",
          description: "Please complete all bank account fields",
          variant: "destructive"
        });
        return;
      }

      // Combine data
      data.accountDetails = `${bankState.bankName} - ${bankState.accountNumber} (${bankState.beneficiaryName})`;
    }

    // Calculate fee based on method and speed
    let fee = 0;
    if (data.withdrawalSpeed === 'express') {
      fee = Math.max(50, Number(data.amount) * 0.02);
    } else {
      fee = Math.max(20, Number(data.amount) * 0.01);
    }

    toast({
      title: "Withdrawal request submitted",
      description: `KES ${data.amount} will be sent to your ${getMethodName(data.method)} account${data.withdrawalSpeed === 'express' ? ' within 10 minutes' : ' within 5 hours'}`
    });
    setWithdrawDialogOpen(false);
  };

  // Helper to get full method name
  const getMethodName = (method: string) => {
    switch(method) {
      case "mpesa": return "M-Pesa";
      case "airtel": return "Airtel Money";
      case "paypal": return "PayPal";
      case "crypto": return "Crypto wallet";
      case "bank": return "Bank account";
      default: return method;
    }
  };

  // Filter transactions based on real data
  const filteredTransactions = transactionFilter === 'all' 
    ? transactions
    : transactions.filter(tx => {
        if (transactionFilter === 'deposit') return tx.type === 'deposit';
        if (transactionFilter === 'withdrawal') return tx.type === 'withdrawal';
        if (transactionFilter === 'earning') return tx.type.startsWith('earning_');
        return false;
      });

  // Function to render transaction status icon
  const getStatusIcon = (status: string) => {
    switch(status) {
      case "completed": 
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "pending": 
        return <Clock className="w-4 h-4 text-amber-500" />;
      case "failed": 
        return <XCircle className="w-4 h-4 text-red-500" />;
      default: 
        return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  // Function to copy to clipboard
  const copyToClipboard = (text: string, message: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: message
    });
  };

  // Function to get transaction type display
  const getTransactionTypeDisplay = (transaction: any) => {
    if (transaction.type === 'deposit') return 'Deposit';
    if (transaction.type === 'withdrawal') return 'Withdrawal';
    if (transaction.type.startsWith('earning_')) {
      return transaction.description || 'Earning';
    }
    return transaction.description || transaction.type;
  };

  // Function to get transaction icon
  const getTransactionIcon = (type: string) => {
    if (type === 'deposit') return <ArrowDownCircle className="h-5 w-5 text-green-600" />;
    if (type === 'withdrawal') return <ArrowUpCircle className="h-5 w-5 text-amber-600" />;
    if (type.startsWith('earning_')) return <CircleDollarSign className="h-5 w-5 text-blue-600" />;
    return <CircleDollarSign className="h-5 w-5 text-gray-600" />;
  };

  if (walletLoading) {
    return (
      <div className="container mx-auto py-8 px-4 pt-24 md:pt-6 pb-20">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-earnhub-red"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 pt-24 md:pt-6 pb-20">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-earnhub-dark">Your Wallet</h1>
        
        {/* Wallet Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card className="wallet-card">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-earnhub-darkGray mb-1">Available Balance</p>
                  <p className="text-2xl font-bold text-earnhub-dark">
                    {wallet?.currency} {Number(wallet?.balance || 0).toLocaleString()}
                  </p>
                </div>
                <Wallet className="h-6 w-6 text-earnhub-red" />
              </div>
              <div className="mt-4 flex gap-2">
                <Button 
                  onClick={() => setDepositDialogOpen(true)}
                  className="bg-earnhub-red hover:bg-earnhub-red/90 flex-1"
                  size="sm"
                >
                  <ArrowDownCircle className="mr-1 h-4 w-4" /> Deposit
                </Button>
                <Button 
                  onClick={() => setWithdrawDialogOpen(true)}
                  variant="outline" 
                  className="flex-1"
                  size="sm"
                >
                  <ArrowUpCircle className="mr-1 h-4 w-4" /> Withdraw
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <Card className="wallet-card">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-earnhub-darkGray mb-1">Pending Balance</p>
                  <p className="text-2xl font-bold text-amber-500">
                    {wallet?.currency} {Number(wallet?.pending_balance || 0).toLocaleString()}
                  </p>
                </div>
                <Clock className="h-6 w-6 text-amber-500" />
              </div>
              <p className="mt-4 text-xs text-earnhub-darkGray">
                Pending balance will be available once tasks and referrals are verified.
              </p>
            </CardContent>
          </Card>
          
          <Card className="wallet-card">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-earnhub-darkGray mb-1">Total Earned</p>
                  <p className="text-2xl font-bold text-green-600">
                    {wallet?.currency} {transactions.filter(t => t.type.startsWith('earning_')).reduce((sum, t) => sum + Number(t.amount), 0).toLocaleString()}
                  </p>
                </div>
                <CircleDollarSign className="h-6 w-6 text-green-600" />
              </div>
              <p className="mt-4 text-xs text-earnhub-darkGray">
                Keep up the good work! Earn more by completing tasks and referring friends.
              </p>
            </CardContent>
          </Card>
        </div>
        
        {/* Transaction History */}
        <Card>
          <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between">
            <CardTitle>Transaction History</CardTitle>
            <div className="flex space-x-1 mt-2 md:mt-0">
              <Button 
                variant={transactionFilter === "all" ? "default" : "outline"} 
                size="sm"
                onClick={() => setTransactionFilter("all")}
                className={transactionFilter === "all" ? "bg-earnhub-red hover:bg-earnhub-red/90" : ""}
              >
                All
              </Button>
              <Button 
                variant={transactionFilter === "deposit" ? "default" : "outline"} 
                size="sm"
                onClick={() => setTransactionFilter("deposit")}
                className={transactionFilter === "deposit" ? "bg-earnhub-red hover:bg-earnhub-red/90" : ""}
              >
                Deposits
              </Button>
              <Button 
                variant={transactionFilter === "withdrawal" ? "default" : "outline"} 
                size="sm"
                onClick={() => setTransactionFilter("withdrawal")}
                className={transactionFilter === "withdrawal" ? "bg-earnhub-red hover:bg-earnhub-red/90" : ""}
              >
                Withdrawals
              </Button>
              <Button 
                variant={transactionFilter === "earning" ? "default" : "outline"} 
                size="sm"
                onClick={() => setTransactionFilter("earning")}
                className={transactionFilter === "earning" ? "bg-earnhub-red hover:bg-earnhub-red/90" : ""}
              >
                Earnings
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            {filteredTransactions.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8">
                <AlertCircle className="w-12 h-12 text-earnhub-darkGray/30 mb-2" />
                <p className="text-earnhub-darkGray">No transactions found</p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredTransactions.map((tx, index) => (
                  <div 
                    key={tx.id}
                    className="transaction-item flex items-center justify-between border-b border-gray-100 pb-3 last:border-0"
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <div className="flex items-center">
                      <div className={`
                        p-2 rounded-full mr-3
                        ${tx.type === 'deposit' ? 'bg-green-100' : ''}
                        ${tx.type === 'withdrawal' ? 'bg-amber-100' : ''}
                        ${tx.type.startsWith('earning_') ? 'bg-blue-100' : ''}
                      `}>
                        {getTransactionIcon(tx.type)}
                      </div>
                      <div>
                        <p className="font-medium">
                          {getTransactionTypeDisplay(tx)}
                        </p>
                        <div className="flex items-center text-xs text-earnhub-darkGray">
                          <span>{new Date(tx.created_at!).toLocaleDateString()}</span>
                          <span className="mx-1">•</span>
                          <span className="flex items-center">
                            {getStatusIcon(tx.status)}
                            <span className="ml-1 capitalize">{tx.status}</span>
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className={`
                      font-medium
                      ${tx.type === 'deposit' ? 'text-green-600' : ''}
                      ${tx.type === 'withdrawal' ? 'text-red-600' : ''}
                      ${tx.type.startsWith('earning_') ? 'text-blue-600' : ''}
                    `}>
                      {tx.type === 'withdrawal' ? '- ' : '+ '}
                      {wallet?.currency} {Number(tx.amount).toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
          
          <CardFooter className="flex justify-center border-t border-gray-100 pt-4">
            <Button variant="outline" className="w-full md:w-auto">
              View All Transactions <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>
      
      {/* Deposit Dialog */}
      <Dialog open={depositDialogOpen} onOpenChange={setDepositDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-2xl">Deposit Funds</DialogTitle>
            <DialogDescription>
              Add money to your wallet to upgrade your membership and access more features.
            </DialogDescription>
          </DialogHeader>
          
          <Tabs defaultValue="mpesa" onValueChange={setDepositType}>
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="mpesa">M-Pesa</TabsTrigger>
              <TabsTrigger value="crypto">Crypto</TabsTrigger>
            </TabsList>
            
            <TabsContent value="mpesa" className="space-y-4">
              <Form {...mobileMoneyForm}>
                <form onSubmit={mobileMoneyForm.handleSubmit(onMobileMoneySubmit)} className="space-y-4">
                  <FormField
                    control={mobileMoneyForm.control}
                    name="phoneNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g. **********" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={mobileMoneyForm.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount (KES)</FormLabel>
                        <FormControl>
                          <Input type="number" placeholder="Minimum KES 500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button type="submit" className="w-full bg-earnhub-red hover:bg-earnhub-red/90">
                    Deposit with M-Pesa
                  </Button>
                  
                  <div className="text-xs text-center text-earnhub-darkGray">
                    <p>An STK push will be sent to your phone to complete the payment.</p>
                    <p className="mt-1">Funds will be added to your wallet immediately after payment.</p>
                  </div>
                </form>
              </Form>
            </TabsContent>
            
            <TabsContent value="crypto" className="space-y-4">
              <Form {...cryptoDepositForm}>
                <form onSubmit={cryptoDepositForm.handleSubmit(onCryptoDepositSubmit)} className="space-y-4">
                  <FormField
                    control={cryptoDepositForm.control}
                    name="cryptoType"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Select cryptocurrency</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex flex-col space-y-1"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="USDT" id="crypto-USDT" />
                              <FormLabel htmlFor="crypto-USDT" className="cursor-pointer">USDT (Tether)</FormLabel>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="PI" id="crypto-PI" />
                              <FormLabel htmlFor="crypto-PI" className="cursor-pointer">PI Coin</FormLabel>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={cryptoDepositForm.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Amount (KES)</FormLabel>
                        <FormControl>
                          <Input type="number" placeholder="Minimum KES 500" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="p-3 bg-blue-50 rounded-md text-sm text-blue-800">
                    <p>After submission, you'll receive deposit instructions for your selected cryptocurrency.</p>
                  </div>
                  
                  <Button type="submit" className="w-full bg-earnhub-red hover:bg-earnhub-red/90">
                    Get Deposit Instructions
                  </Button>
                </form>
              </Form>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
      
      {/* Withdrawal Dialog */}
      <Dialog open={withdrawDialogOpen} onOpenChange={setWithdrawDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle className="text-2xl">Withdraw Funds</DialogTitle>
            <DialogDescription>
              Choose your preferred withdrawal method and amount.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...withdrawalForm}>
            <form onSubmit={withdrawalForm.handleSubmit(onWithdrawalSubmit)} className="space-y-4">
              <FormField
                control={withdrawalForm.control}
                name="method"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Withdrawal Method</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value: "mpesa" | "airtel" | "paypal" | "crypto" | "bank") => {
                          field.onChange(value);
                          setWithdrawalMethod(value);
                        }}
                        defaultValue={field.value}
                        className="grid grid-cols-1 md:grid-cols-2 gap-2"
                      >
                        <div className="flex items-center space-x-2 border rounded-md p-2">
                          <RadioGroupItem value="mpesa" id="mpesa" />
                          <FormLabel htmlFor="mpesa" className="cursor-pointer flex-grow">
                            M-Pesa
                          </FormLabel>
                        </div>
                        
                        <div className="flex items-center space-x-2 border rounded-md p-2">
                          <RadioGroupItem value="airtel" id="airtel" />
                          <FormLabel htmlFor="airtel" className="cursor-pointer flex-grow">
                            Airtel Money
                          </FormLabel>
                        </div>
                        
                        <div className="flex items-center space-x-2 border rounded-md p-2">
                          <RadioGroupItem value="paypal" id="paypal" />
                          <FormLabel htmlFor="paypal" className="cursor-pointer flex-grow">
                            PayPal
                          </FormLabel>
                        </div>
                        
                        <div className="flex items-center space-x-2 border rounded-md p-2">
                          <RadioGroupItem value="crypto" id="crypto" />
                          <FormLabel htmlFor="crypto" className="cursor-pointer flex-grow">
                            Crypto
                          </FormLabel>
                        </div>
                        
                        <div className="flex items-center space-x-2 border rounded-md p-2 md:col-span-2">
                          <RadioGroupItem value="bank" id="bank" />
                          <FormLabel htmlFor="bank" className="cursor-pointer flex-grow">
                            Bank Transfer
                          </FormLabel>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {withdrawalMethod === "bank" && (
                <div className="space-y-4 border-t border-b border-gray-100 py-4">
                  <h3 className="font-medium">Bank Account Details</h3>
                  
                  <FormField
                    control={bankForm.control}
                    name="bankName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bank Name</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g. Equity Bank" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={bankForm.control}
                    name="accountNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Account Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Your bank account number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={bankForm.control}
                    name="beneficiaryName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Beneficiary Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Account holder name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
              
              <FormField
                control={withdrawalForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount (KES)</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder={`Minimum KES 1,000 (Max ${wallet?.balance})`} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {withdrawalMethod !== "bank" && (
                <FormField
                  control={withdrawalForm.control}
                  name="accountDetails"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {withdrawalMethod === "mpesa" || withdrawalMethod === "airtel" 
                          ? "Phone Number" 
                          : withdrawalMethod === "paypal" 
                            ? "PayPal Email" 
                            : "Wallet Address"}
                      </FormLabel>
                      <FormControl>
                        <Input 
                          placeholder={
                            withdrawalMethod === "mpesa" || withdrawalMethod === "airtel" 
                              ? "e.g. **********" 
                              : withdrawalMethod === "paypal" 
                                ? "e.g. <EMAIL>" 
                                : "Your crypto wallet address"
                          } 
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
              
              <FormField
                control={withdrawalForm.control}
                name="withdrawalSpeed"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Withdrawal Speed</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <div className="flex items-center justify-between space-x-2 border rounded-md p-3">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="standard" id="standard" />
                            <FormLabel htmlFor="standard" className="cursor-pointer font-medium">
                              Standard
                            </FormLabel>
                          </div>
                          <div className="text-right">
                            <p className="text-xs text-earnhub-darkGray">1-5 hours</p>
                            <p className="text-xs font-medium">Fee: 1% (min KES 20)</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between space-x-2 border border-earnhub-red/20 rounded-md p-3 bg-red-50/30">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="express" id="express" />
                            <FormLabel htmlFor="express" className="cursor-pointer font-medium">
                              Express
                            </FormLabel>
                          </div>
                          <div className="text-right">
                            <p className="text-xs text-earnhub-darkGray">2-10 minutes</p>
                            <p className="text-xs font-medium">Fee: 2% (min KES 50)</p>
                          </div>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <Button type="submit" className="w-full bg-earnhub-red hover:bg-earnhub-red/90">
                Request Withdrawal
              </Button>
              
              <div className="text-xs text-center text-earnhub-darkGray space-y-1">
                <p>Minimum withdrawal amount: KES 1,000</p>
                <p>Available balance: KES {wallet?.balance.toLocaleString()}</p>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default WalletPage;
