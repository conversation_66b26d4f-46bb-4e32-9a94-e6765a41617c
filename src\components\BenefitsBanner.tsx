
import { cn } from '@/lib/utils';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';

const BenefitsBanner = () => {
  const benefits = [
    {
      title: "Multiple Income Streams",
      description: "Earn through referrals (KES 400-1,924 each), tasks, competitions & more",
      color: "bg-earnhub-red/10"
    },
    {
      title: "Freedom & Flexibility",
      description: "Work from anywhere, anytime - earn on your schedule without deadlines",
      color: "bg-earnhub-dark/10"
    },
    {
      title: "Community Support",
      description: "Join specialized guilds and get help from 2,000+ successful members",
      color: "bg-earnhub-red/10"
    }
  ];

  return (
    <section className="py-16 bg-white px-6 md:px-10">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-earnhub-dark mb-4 balance-text">
            How <span className="text-earnhub-red">EarnHub</span> Transforms Lives
          </h2>
          <p className="text-earnhub-darkGray text-lg max-w-2xl mx-auto">
            Join thousands who are building financial independence with earnings of up to <span className="font-bold text-earnhub-red">KES 50,000 monthly</span>
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <div 
              key={index}
              className={cn(
                "rounded-xl p-8 transition-all hover:shadow-card hover:-translate-y-1",
                benefit.color
              )}
            >
              <h3 className="text-xl font-bold text-earnhub-dark mb-3">{benefit.title}</h3>
              <p className="text-earnhub-darkGray mb-4">{benefit.description}</p>
              <Button variant="link" className="text-earnhub-red p-0 flex items-center gap-1 group">
                See success stories
                <ArrowRight size={16} className="group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BenefitsBanner;
