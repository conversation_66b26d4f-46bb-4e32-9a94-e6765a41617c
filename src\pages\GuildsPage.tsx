
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardDescription, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Users, ChevronRight, Trophy, UserPlus, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import anime from "animejs";

// Mock guild data - would be replaced with Sanity CMS integration
const guilds = [
  {
    id: "1",
    name: "Tech Innovators",
    category: "Technology",
    members: 124,
    level: 5,
    rewards: "Weekly challenges, exclusive tasks",
    description: "A community of tech enthusiasts working on innovative projects and sharing knowledge",
    isJoined: false
  },
  {
    id: "2",
    name: "Content Creators",
    category: "Creative",
    members: 87,
    level: 4,
    rewards: "Portfolio exposure, client connections",
    description: "For writers, designers, and content creators looking to collaborate and grow",
    isJoined: true
  },
  {
    id: "3",
    name: "Business Network",
    category: "Business",
    members: 156,
    level: 7,
    rewards: "Mentorship, business opportunities",
    description: "Entrepreneurs and business professionals sharing insights and opportunities",
    isJoined: false
  },
  {
    id: "4",
    name: "Market Research Team",
    category: "Research",
    members: 63,
    level: 3,
    rewards: "Priority access to surveys, bonus rewards",
    description: "Members focused on market research and data analysis tasks",
    isJoined: false
  },
  {
    id: "5",
    name: "Student Hub",
    category: "Education",
    members: 210,
    level: 6,
    rewards: "Study resources, internship opportunities",
    description: "For students looking to earn while learning and building their resume",
    isJoined: true
  }
];

const GuildsPage = () => {
  const [currentTab, setCurrentTab] = useState("discover");
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredGuilds, setFilteredGuilds] = useState(guilds);
  
  useEffect(() => {
    // Animation for the guild cards
    anime({
      targets: '.guild-card',
      translateY: [20, 0],
      opacity: [0, 1],
      delay: anime.stagger(100),
      easing: 'easeOutExpo'
    });
    
    // Title animation
    anime({
      targets: '.page-title',
      translateX: [-20, 0],
      opacity: [0, 1],
      easing: 'easeOutExpo',
      duration: 800
    });
  }, [currentTab]);

  // Filter guilds based on search term
  useEffect(() => {
    const results = guilds.filter(guild => 
      guild.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
      guild.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guild.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredGuilds(results);
  }, [searchTerm]);

  // Filter guilds based on tab
  const displayedGuilds = filteredGuilds.filter(guild => {
    if (currentTab === "joined") {
      return guild.isJoined;
    }
    return true;
  });

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-6 pb-20">
      <div className="mb-8">
        <h1 className="text-2xl md:text-3xl font-bold mb-2 page-title flex items-center">
          <Users className="mr-2 text-earnhub-red" />
          Guilds
        </h1>
        <p className="text-earnhub-darkGray">Join teams, collaborate on tasks, and earn together</p>
      </div>
      
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input 
            placeholder="Search guilds..." 
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>
      
      <Tabs defaultValue="discover" onValueChange={setCurrentTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="discover">Discover</TabsTrigger>
          <TabsTrigger value="joined">My Guilds</TabsTrigger>
          <TabsTrigger value="featured">Featured</TabsTrigger>
        </TabsList>
        
        <TabsContent value="discover" className="space-y-4">
          {displayedGuilds.length > 0 ? (
            displayedGuilds.map((guild) => (
              <Card key={guild.id} className="guild-card hover:border-earnhub-red transition-all">
                <CardContent className="p-5">
                  <div className="flex flex-col md:flex-row justify-between items-start gap-4">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-lg">{guild.name}</h3>
                        <Badge variant="outline" className="bg-gray-100">Lv.{guild.level}</Badge>
                      </div>
                      <div className="flex items-center space-x-2 mb-2">
                        <Badge>{guild.category}</Badge>
                        <span className="text-sm text-earnhub-darkGray">{guild.members} members</span>
                      </div>
                      <p className="text-sm text-earnhub-darkGray mb-2">{guild.description}</p>
                      <div className="text-xs text-earnhub-darkGray">
                        <strong>Rewards:</strong> {guild.rewards}
                      </div>
                    </div>
                    <div className="flex-shrink-0 mt-2 md:mt-0">
                      <Button variant={guild.isJoined ? "outline" : "default"}>
                        {guild.isJoined ? 'View Guild' : 'Join Guild'} <ChevronRight className="ml-1 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="p-5 text-center">
                <p className="text-earnhub-darkGray">No guilds found matching your search criteria.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="joined" className="space-y-4">
          {displayedGuilds.length > 0 ? (
            displayedGuilds.map((guild) => (
              <Card key={guild.id} className="guild-card hover:border-earnhub-red transition-all">
                <CardContent className="p-5">
                  <div className="flex flex-col md:flex-row justify-between items-start gap-4">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-lg">{guild.name}</h3>
                        <Badge variant="outline" className="bg-gray-100">Lv.{guild.level}</Badge>
                      </div>
                      <div className="flex items-center space-x-2 mb-2">
                        <Badge>{guild.category}</Badge>
                        <span className="text-sm text-earnhub-darkGray">{guild.members} members</span>
                      </div>
                      <p className="text-sm text-earnhub-darkGray mb-2">{guild.description}</p>
                      <div className="text-xs text-earnhub-darkGray">
                        <strong>Rewards:</strong> {guild.rewards}
                      </div>
                    </div>
                    <div className="flex-shrink-0 mt-2 md:mt-0">
                      <Button>
                        View Guild <ChevronRight className="ml-1 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="p-5 text-center">
                <p className="text-earnhub-darkGray mb-4">You haven't joined any guilds yet.</p>
                <Button onClick={() => setCurrentTab("discover")}>Discover Guilds</Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        
        <TabsContent value="featured" className="space-y-4">
          <Card className="guild-card hover:border-earnhub-red transition-all">
            <CardHeader className="bg-gradient-to-r from-earnhub-red/10 to-purple-500/10 rounded-t-lg">
              <div className="flex items-center gap-2">
                <Trophy className="text-yellow-500" />
                <CardTitle>Featured Guild of the Month</CardTitle>
              </div>
              <CardDescription>Special rewards and opportunities</CardDescription>
            </CardHeader>
            <CardContent className="p-5">
              <div className="flex flex-col md:flex-row justify-between items-start gap-4">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-medium text-lg">Tech Innovators</h3>
                    <Badge variant="outline" className="bg-gray-100">Lv.5</Badge>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <Badge>Technology</Badge>
                    <span className="text-sm text-earnhub-darkGray">124 members</span>
                  </div>
                  <p className="text-sm text-earnhub-darkGray mb-2">A community of tech enthusiasts working on innovative projects and sharing knowledge</p>
                  <div className="text-xs text-earnhub-darkGray">
                    <strong>Rewards:</strong> Weekly challenges, exclusive tasks
                  </div>
                </div>
                <div className="flex-shrink-0 mt-2 md:mt-0">
                  <Button>
                    Join Guild <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 border-t">
              <div className="w-full">
                <h4 className="text-sm font-medium mb-2">Featured Benefits:</h4>
                <ul className="text-sm text-earnhub-darkGray list-disc pl-5 space-y-1">
                  <li>Double rewards on all collaborative tasks</li>
                  <li>Priority access to premium opportunities</li>
                  <li>Exclusive mentorship from industry experts</li>
                </ul>
              </div>
            </CardFooter>
          </Card>
          
          <div className="text-center py-4">
            <Button variant="outline" className="mx-auto">
              View All Featured Guilds <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </div>
        </TabsContent>
      </Tabs>
      
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <UserPlus className="mr-2" /> Create Your Own Guild
          </CardTitle>
          <CardDescription>Start a collaborative team and invite others to join</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">Creating a guild allows you to:</p>
          <ul className="list-disc pl-5 space-y-2 mb-6">
            <li>Collaborate on tasks and earn team rewards</li>
            <li>Access exclusive group opportunities</li>
            <li>Build a community around shared interests</li>
            <li>Compete in guild-vs-guild challenges</li>
          </ul>
          <Button className="bg-earnhub-red hover:bg-earnhub-red/90">
            Create Guild
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default GuildsPage;
