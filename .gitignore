# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Added by <PERSON> Task Master
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
.vscode
# OS specific
# Task files
tasks.json
tasks/ 

.roo/
.roomodes
.taskmasterconfig
.windsurfrules
example_prd.txt

.cursor/

.plandex-v2/projects-v2.json
