
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { Home, Zap, TrendingUp, Wallet, User } from "lucide-react";

const BottomNav = () => {
  const location = useLocation();
  const isMobile = useIsMobile();
  
  if (!isMobile) return null;

  // Most important features for mobile bottom navigation
  const bottomNavItems = [
    {
      name: "Dashboard",
      icon: Home,
      path: "/dashboard",
    },
    {
      name: "Tasks",
      icon: Zap,
      path: "/tasks",
    },
    {
      name: "Referrals",
      icon: TrendingUp,
      path: "/referrals",
    },
    {
      name: "Wallet",
      icon: Wallet,
      path: "/wallet",
    },
    {
      name: "Profile",
      icon: User,
      path: "/profile",
    }
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 h-16 bg-white border-t border-gray-200 z-50 shadow-md">
      <div className="grid grid-cols-5 h-full">
        {bottomNavItems.map((item) => {
          const isActive = location.pathname === item.path;
          
          return (
            <Link
              key={item.name}
              to={item.path}
              className={cn(
                "flex flex-col items-center justify-center space-y-1 transition-colors",
                isActive ? "text-earnhub-red bg-earnhub-red/5" : "text-gray-600 hover:text-earnhub-red hover:bg-earnhub-red/5"
              )}
            >
              <item.icon size={20} />
              <span className="text-xs font-medium">{item.name}</span>
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default BottomNav;
