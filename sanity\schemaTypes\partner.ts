// sanity/schemaTypes/partner.ts
import {Rule} from 'sanity'

export default {
  name: 'partner',
  title: 'Partner Information', // Renamed for clarity
  type: 'document',
  fields: [
    {
      name: 'nileUserId', // Changed from userId to be specific
      title: 'Nile User ID',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
      description: 'The User ID from the Nile DB users table.'
    },
    {
      name: 'adminNotes',
      title: 'Administrator Notes',
      type: 'text',
      description: 'Internal notes about this partner (e.g., special agreements, performance review notes).'
    },
    {
      name: 'applicationStatus', // If there's a manual review process managed in Sanity
      title: 'Application Status',
      type: 'string',
      options: {
        list: [
          {title: 'Pending Review', value: 'pending_review'},
          {title: 'Approved', value: 'approved'},
          {title: 'Requires More Information', value: 'requires_info'},
          {title: 'Rejected', value: 'rejected'}
        ]
      },
      description: 'Status of their application to the partner program (if manually managed here).'
    },
    {
      name: 'manualOverrideTier', // If admin needs to manually set a tier in Sanity for some reason
      title: 'Manual Override Tier',
      type: 'reference',
      to: [{type: 'membershipTier'}],
      description: 'Manually override the partner tier. Nile DB is the primary source of truth for tiers.'
    },
    {
      name: 'joinedDate', // This might still be useful to log in Sanity
      title: 'Date Joined Partner Program',
      type: 'datetime',
      readOnly: true // Should be set programmatically when partner status becomes active
    },
    {
      name: 'isActiveSanity', // A specific flag in Sanity if needed for content gating
      title: 'Is Active (Sanity Flag)',
      type: 'boolean',
      description: 'A manual flag in Sanity for controlling partner status visibility or access in CMS-driven parts. Nile DB governs actual program participation.',
      initialValue: false
    }
    // Removed fields: name, email, tier (use manualOverrideTier or rely on Nile), affiliateCode (from Nile),
    // totalEarnings, unpaidEarnings, totalSales (all these will be in Nile DB).
    // The 'status' field is replaced by 'applicationStatus' and 'isActiveSanity' for more specific Sanity-side flags.
  ],
  preview: {
    select: {
      title: 'nileUserId', // Display Nile User ID in preview
      status: 'applicationStatus',
      isActive: 'isActiveSanity'
    },
    prepare({title, status, isActive}: {title: string, status: string, isActive: boolean}) {
      return {
        title: `Partner (User ID: ${title})`,
        subtitle: `Status: ${status || 'N/A'}, Active (Sanity): ${isActive}`
      }
    }
  }
}
