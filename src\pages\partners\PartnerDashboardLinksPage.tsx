
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, <PERSON>, <PERSON>ader2, ExternalLink } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { AffiliateLink } from "@/types/partner";

// Mock data
const mockProducts = [
  { _id: "prod1", title: "Premium WordPress Theme", price: 5000 },
  { _id: "prod2", title: "Business Website Package", price: 10000 },
  { _id: "prod3", title: "WhatsApp Bot for Businesses", price: 7500 },
  { _id: "prod4", title: "Social Media Management", price: 3000 },
  { _id: "prod5", title: "CV Revamping Service", price: 2000 },
];

// Mock affiliate links
const mockAffiliateLinks: AffiliateLink[] = [
  {
    _id: "link1",
    code: "JOHN25",
    partnerId: "partner1",
    product: { _id: "prod1", title: "Premium WordPress Theme" },
    isGeneralLink: false,
    dateCreated: new Date().toISOString(),
    clicks: 145,
    conversions: 12,
    revenue: 60000,
    commission: 12000,
    customName: "WordPress Promo"
  },
  {
    _id: "link2",
    code: "JOHN25",
    partnerId: "partner1",
    isGeneralLink: true,
    dateCreated: new Date().toISOString(),
    clicks: 320,
    conversions: 18,
    revenue: 78000,
    commission: 15600
  },
];

// Form schema for creating a new affiliate link
const formSchema = z.object({
  productId: z.string().optional(),
  customName: z.string().optional(),
  linkType: z.enum(["product", "general"]),
});

const PartnerDashboardLinksPage = () => {
  const [isCreatingLink, setIsCreatingLink] = useState(false);
  const [affiliateLinks, setAffiliateLinks] = useState<AffiliateLink[]>(mockAffiliateLinks);
  const [activeTab, setActiveTab] = useState("all");

  // Filter links based on active tab
  const filteredLinks = activeTab === "all" 
    ? affiliateLinks 
    : activeTab === "products" 
      ? affiliateLinks.filter(link => !link.isGeneralLink) 
      : affiliateLinks.filter(link => link.isGeneralLink);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      linkType: "product",
      productId: mockProducts[0]._id,
      customName: "",
    },
  });

  // Mock function to generate a new affiliate link
  const generateAffiliateLink = async (values: z.infer<typeof formSchema>) => {
    setIsCreatingLink(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newLink: AffiliateLink = {
        _id: `link${affiliateLinks.length + 1}`,
        code: "JOHN25", // In a real app, this would be the user's unique code
        partnerId: "partner1", // In a real app, this would be the user's ID
        isGeneralLink: values.linkType === "general",
        dateCreated: new Date().toISOString(),
        clicks: 0,
        conversions: 0,
        revenue: 0,
        commission: 0,
        customName: values.customName || undefined
      };
      
      // If it's a product-specific link, add the product info
      if (values.linkType === "product" && values.productId) {
        const product = mockProducts.find(p => p._id === values.productId);
        if (product) {
          newLink.product = { _id: product._id, title: product.title };
        }
      }
      
      setAffiliateLinks([newLink, ...affiliateLinks]);
      
      toast({
        title: "Link created successfully!",
        description: "Your new affiliate link has been generated.",
      });
      
      form.reset();
    } catch (error) {
      toast({
        title: "Error creating link",
        description: "There was a problem generating your affiliate link. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingLink(false);
    }
  };

  // Helper function to copy a link to clipboard
  const copyLinkToClipboard = (linkCode: string, isProductLink: boolean, productId?: string) => {
    let baseUrl = window.location.origin;
    const url = isProductLink && productId 
      ? `${baseUrl}/p/product/${productId}?ref=${linkCode}` 
      : `${baseUrl}/p/shop?ref=${linkCode}`;
    
    navigator.clipboard.writeText(url).then(() => {
      toast({
        title: "Link copied!",
        description: "The affiliate link has been copied to your clipboard.",
      });
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-6">
        {/* Create New Link Card */}
        <Card className="w-full md:w-1/3">
          <CardHeader>
            <CardTitle>Create New Link</CardTitle>
            <CardDescription>
              Generate a custom affiliate link to share with your audience
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(generateAffiliateLink)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="linkType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Link Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select link type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="product">Product-specific Link</SelectItem>
                          <SelectItem value="general">General Store Link</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose whether this link is for a specific product or the general store
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {form.watch("linkType") === "product" && (
                  <FormField
                    control={form.control}
                    name="productId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Product</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select product" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {mockProducts.map((product) => (
                              <SelectItem key={product._id} value={product._id}>
                                {product.title} - KES {product.price.toLocaleString()}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the product this link will promote
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                
                <FormField
                  control={form.control}
                  name="customName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Custom Name (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Summer Sale" {...field} />
                      </FormControl>
                      <FormDescription>
                        Add a name to help you identify this link
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <Button type="submit" className="w-full" disabled={isCreatingLink}>
                  {isCreatingLink ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    "Generate Link"
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
        
        {/* Affiliate Links Card */}
        <Card className="w-full md:w-2/3">
          <CardHeader>
            <CardTitle>Your Affiliate Links</CardTitle>
            <CardDescription>
              Manage and track performance of your affiliate links
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="w-full mb-4">
                <TabsTrigger value="all" className="flex-1">All Links</TabsTrigger>
                <TabsTrigger value="products" className="flex-1">Product Links</TabsTrigger>
                <TabsTrigger value="general" className="flex-1">General Links</TabsTrigger>
              </TabsList>
              
              <TabsContent value="all">
                {renderLinksTable(filteredLinks, copyLinkToClipboard)}
              </TabsContent>
              <TabsContent value="products">
                {renderLinksTable(filteredLinks, copyLinkToClipboard)}
              </TabsContent>
              <TabsContent value="general">
                {renderLinksTable(filteredLinks, copyLinkToClipboard)}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Promotional Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">Social Media</h4>
              <p className="text-sm text-gray-500">Share your affiliate links on your social media profiles. Include compelling descriptions and images to increase clicks.</p>
            </div>
            <div>
              <h4 className="font-medium">Email Marketing</h4>
              <p className="text-sm text-gray-500">Send personalized emails to your subscribers with your affiliate links. Explain how the products or services can benefit them.</p>
            </div>
            <div>
              <h4 className="font-medium">Content Creation</h4>
              <p className="text-sm text-gray-500">Create blog posts, videos, or tutorials that demonstrate the value of the products. Include your affiliate links within the content.</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Helper function to render the links table
const renderLinksTable = (links: AffiliateLink[], copyFunc: (code: string, isProductLink: boolean, productId?: string) => void) => {
  if (links.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No affiliate links found</p>
      </div>
    );
  }
  
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Link Name</TableHead>
            <TableHead>Type</TableHead>
            <TableHead className="text-right">Clicks</TableHead>
            <TableHead className="text-right">Conversions</TableHead>
            <TableHead className="text-right">Revenue</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {links.map((link) => (
            <TableRow key={link._id}>
              <TableCell className="font-medium">
                {link.customName || (link.product ? link.product.title : 'General Store Link')}
              </TableCell>
              <TableCell>
                <Badge variant={link.isGeneralLink ? "outline" : "default"}>
                  {link.isGeneralLink ? "General" : "Product"}
                </Badge>
              </TableCell>
              <TableCell className="text-right">{link.clicks}</TableCell>
              <TableCell className="text-right">{link.conversions}</TableCell>
              <TableCell className="text-right">KES {link.revenue.toLocaleString()}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button 
                    size="icon" 
                    variant="ghost" 
                    onClick={() => copyFunc(link.code, !link.isGeneralLink, link.product?._id)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button size="icon" variant="ghost" asChild>
                    <a 
                      href={link.isGeneralLink 
                        ? `/p/shop?ref=${link.code}` 
                        : `/p/product/${link.product?._id}?ref=${link.code}`}
                      target="_blank" 
                      rel="noreferrer"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default PartnerDashboardLinksPage;
