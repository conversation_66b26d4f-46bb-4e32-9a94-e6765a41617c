"EarnHub- Comprehensive Feature Analysis & Report Based on my detailed analysis of the EarnHub platform codebase, here's a comprehensive report of all current features, capabilities, and implementation recommendations.

LANDING PAGE & MARKETING ✅ Implemented Features Responsive Landing Page: Full landing page with hero section, features, testimonials, and CTA Marketing Components: Trust badges, benefits banner, limited-time offers SEO Optimization: Meta tags, structured content with React Helmet Mobile-First Design: Fully responsive with mobile hamburger menu Smooth Navigation: Anchor link scrolling, mobile-optimized navigation Key Value Propositions Highlighted: Referrals: KES 400-1,924 per person invited Simple Tasks: 5-15 minute activities for KES 50-500 Competitions: Weekly cash prizes up to KES 5,000 Partner Program: Commission-based product sales

USER AUTHENTICATION & ONBOARDING ✅ Implemented Features Sign Up/Login Pages: Complete forms with validation Onboarding Modal: Welcome new users with platform introduction Profile Management: Avatar selection, profile completion tracking 🔄 Needs Implementation Backend Authentication: No actual authentication system integrated (Will use Supabase Auth) Email Verification: Missing verification workflow (Supabase Auth provides this) Password Reset: Not implemented (Supabase Auth provides this) Social Login: No OAuth integration (Supabase Auth provides this)

MEMBERSHIP SYSTEM ✅ Implemented Features Four-Tier System: Starter (Free): 40% referral commission, basic tasks, KES 1,000 minimum withdrawal Bronze (KES 1,500): 45% commission, enhanced tasks, KES 800 minimum withdrawal Silver (KES 3,499): 50% commission, premium tasks, reduced fees Gold (KES 7,500): 55% commission, VIP tasks, no fees, custom store 🔄 Needs Implementation Payment Integration: No actual payment processing Membership Verification: No backend validation Automatic Tier Upgrades: Based on performance metrics

DASHBOARD & USER INTERFACE ✅ Implemented Features Main Dashboard: Earnings overview, quick access grid, live updates Earnings Summary: Total earnings, pending withdrawals, today's earnings, available balance Live Activity Feed: Real-time updates of user activities across platform News Carousel: Platform updates and announcements Quick Access Grid: One-click navigation to all major features Mobile-Responsive Layout: Optimized for all screen sizes

TASK MANAGEMENT SYSTEM ✅ Implemented Features Task Categories: Survey, Testing, Social, Review, Research Difficulty Levels: Easy, Medium, Hard with corresponding rewards Task Filtering: Available, Premium, Completed tabs Task Details: Estimated time, rewards, requirements 🔄 Needs Implementation Task Submission System: No actual task completion workflow Payment Processing: No automatic reward distribution Task Verification: No approval/rejection system Dynamic Task Loading: Currently using mock data

COMPETITION SYSTEM ✅ Implemented Features Competition Types: Referral, Sales, Task-based competitions Competition States: Active, Upcoming, Past Leaderboards: User ranking and prize distribution Competition Details: Rules, prizes, participant counts 🔄 Needs Implementation Entry System: No actual competition participation Winner Selection: No automated prize distribution Real-time Updates: Static competition data

REFERRAL SYSTEM ✅ Implemented Features Referral Link Generation: Unique codes for each user Referral Tracking: Click tracking, conversion metrics Performance Analytics: Charts showing referral performance over time Commission Structure: Tier-based commission rates Referral Dashboard: Comprehensive metrics and analytics 🔄 Needs Implementation Link Attribution: No actual tracking of referral clicks Commission Payments: No automatic commission processing Referral Validation: No verification of successful referrals

SHOP & E-COMMERCE ✅ Implemented Features Product Catalog: Categories, featured products, filtering Product Details: Images, descriptions, pricing, savings Shopping Cart: Add/remove items, quantity management Category Navigation: Organized product browsing Product Search & Filter: Price range, category, sort options 🔄 Needs Implementation Checkout Process: No actual payment processing Inventory Management: No stock tracking Order Management: No order history or tracking Digital Product Delivery: No download or access system

PARTNER PROGRAM (AFFILIATE SYSTEM) ✅ Implemented Features Partner Registration: Application and approval system Affiliate Dashboard: Performance metrics, earnings tracking Link Generation: Product-specific and general affiliate links Commission Tracking: Click tracking, conversion rates, earnings Multi-Tier Program: Basic, Silver, Gold, Platinum tiers Public Product Pages: Affiliate-specific landing pages Streamlined Checkout: For affiliate customers 🔄 Needs Implementation Real Link Tracking: No actual click/conversion tracking Commission Payments: No automated payout system Product Integration: Limited connection to shop inventory

NOTIFICATION SYSTEM ✅ Implemented Features Notification Types: System, Account, Competition, Task, Promotion, Partner, Recommendation Priority Levels: Low, Medium, High Filtering System: By type, priority, read status Notification Preferences: User-configurable settings Real-time Dropdown: Notification bell with unread count Grouped Notifications: Organized by date (Today, Yesterday, etc.) 🔄 Needs Implementation Real-time Updates: Currently using mock data (Supabase Realtime can be used) Push Notifications: No browser/mobile notifications Email Notifications: No email integration (Supabase Auth handles some, transactional emails via Edge Functions + provider)

WALLET & FINANCIAL MANAGEMENT ✅ Implemented Features Balance Overview: Available, pending, total earned Transaction History: Deposits, withdrawals, earnings tracking Multiple Payment Methods: M-Pesa, PayPal, Crypto (USDT, PI) Withdrawal System: Form-based withdrawal requests Transaction Status Tracking: Pending, completed, failed states 🔄 Needs Implementation Real Payment Integration: No actual financial processing KYC Verification: No identity verification system Transaction Verification: No real transaction processing Automated Payouts: No scheduled payment system

ADDITIONAL FEATURES ✅ Implemented Features Achievements System: Progress tracking, points, rewards Profile Management: Avatar selection, completion tracking Settings Page: Account preferences and configurations Responsive Design: Mobile and desktop optimization Animation System: Smooth transitions and micro-interactions 🔄 Needs Implementation Guilds/Communities: Placeholder pages only Quests System: Not fully developed Raffle System: Basic UI only Ambassador Program: Limited functionality

TECHNICAL ARCHITECTURE ✅ Current Stack Frontend: React 18, TypeScript, Vite UI Components: Radix UI, Tailwind CSS, Shadcn/ui State Management: React Query for data fetching Routing: React Router v6 Forms: React Hook Form with Zod validation Animations: Anime.js for smooth transitions CMS Ready: Sanity client integration prepared 🔄 Missing Backend Infrastructure (To be built with Supabase) Database: No persistent data storage (Will use Supabase PostgreSQL) Authentication: No user management system (Will use Supabase Auth) Payment Processing: No financial integrations File Storage: No image/document handling (Will use Supabase Storage) Email Service: No communication system (Supabase Auth for auth emails, transactional via Edge Function + provider) Real-time Features: No WebSocket implementation (Will use Supabase Realtime)

CONTENT MANAGEMENT ✅ Implemented Sanity Schemas: Prepared for all major content types Mock Data: Comprehensive dummy data for all features Type Definitions: Full TypeScript interfaces 🔄 Needs Configuration Sanity CMS Setup: Backend content management Image Optimization: CDN and asset management Dynamic Content Loading: Replace mock data with real CMS data

CRITICAL IMPLEMENTATION PRIORITIES Phase 1: Core Backend (Immediate) Supabase Integration: Authentication, database, real-time features User Management: Registration, login, profile management Basic Payment System: Withdrawal requests, balance tracking Task Completion System: Actual task submission and approval Phase 2: Business Logic (Short-term) Referral Tracking: Real link attribution and commission calculation Membership Payments: Lipia Online integration for tier upgrades (via Supabase Edge Functions) Competition System: Entry mechanism and winner selection Notification Backend: Real-time notification delivery Phase 3: Advanced Features (Medium-term) Partner Program Backend: Real affiliate tracking and payouts Shop Integration: Inventory management and order processing Achievement Engine: Automated progress tracking and rewards Analytics Dashboard: Comprehensive reporting and insights Phase 4: Scale & Optimize (Long-term) Mobile App: React Native or PWA implementation Advanced Gamification: Leaderboards, social features, guilds AI Recommendations: Personalized task and opportunity suggestions Multi-language Support: Localization for different markets

BUSINESS MODEL VALIDATION Revenue Streams Identified Membership Fees: One-time payments for tier upgrades Task Commissions: Platform fee on completed tasks Partner Program: Commission on affiliate sales Competition Entries: Premium competition participation fees Key Success Metrics to Track User retention and engagement rates Conversion from free to paid memberships Average earnings per user Referral program effectiveness Task completion rates CONCLUSION EarnHub has a comprehensive and well-structured foundation with excellent UI/UX design and clear business logic. The platform demonstrates strong understanding of user needs and gamification principles. However, it requires significant backend development to become a fully functional platform.

The immediate priority should be integrating a robust backend system like Supabase to handle user authentication, data persistence, and core business logic. With proper backend implementation, EarnHub has the potential to become a successful earning platform in the Kenyan market."

Plan for Creating Sanity CMS Schemas with Supabase DB and Auth Integration

Sanity CMS Schema Overview I'll create Sanity schemas for the following content types based on your application's needs:

User-related Content

Profile Avatars

Achievement Types

Skill Categories

Membership Tiers

Opportunity Content

Job Listings & Opportunities

Companies/Organizations

Engagement Content

Competitions

Quests & Tasks

Raffles

News & Updates

Live Activity Feed

Marketing Content

Testimonials

FAQ Items

Benefits/Features

Supabase Schema Overview: In your Supabase PostgreSQL database, we'll create the following tables (likely in the public schema initially):

Core User Data

profiles (extends auth.users from Supabase Auth)

membership_purchases (for one-off membership payments)

Financial Data

wallets

transactions

payment_methods (if needed for stored methods beyond Lipia one-offs)

Engagement Data

user_quests (replaces user_tasks)

competition_entries

raffle_entries

achievement_progress

referrals

Integration Architecture The integration between Sanity CMS and Supabase will follow these principles:

CMS for Content, DB for User Data

Sanity: Stores all content, UI elements, and configuration.

Supabase: Stores all user data, transactions, and relationships. Uses Supabase Auth for identity.

Reference IDs

User activities in Supabase will reference Sanity content using IDs (e.g., sanity_product_id).

Example: A user's task completion in Supabase references a task defined in Sanity.

Real-time Updates

User-specific content (like dashboard stats) will be pulled from Supabase (potentially using Supabase Realtime).

Content structures and templates will come from Sanity.

Schema Implementation Approach For each component in your application:

Create a Sanity schema for content structure.

Create corresponding Supabase tables for user data (with appropriate RLS policies).

Implement Supabase Edge Functions (API services) that combine both data sources.

I'll now outline detailed schemas for both Sanity CMS and Supabase DB based on your application needs. Let's start with the key components:

(The Sanity schema definitions for Achievement, Competition, Opportunity, Raffle, QuickAccessItem you provided previously are still valid and can be used here.)

2: Supabase DB Table Schemas (You would then provide the SQL DDL for Supabase tables as we discussed in the "Supabase Database Plan for EarnHub" prompt, including the profiles table linked to auth.users, RLS policies, and triggers for updated_at.)

EarnHub Partner Program Implementation Plan (for Supabase)

Sanity Schema Updates

Create a Partner Program Schema (if distinct settings are needed beyond user status).

Update the product schema in Sanity: commissionRate (%), partnerEligible (boolean), commissionTiers (array: { membershipTierId, rate }).

(Optional) Create an AffiliateLinkDefinition schema in Sanity if admins create/manage specific campaign links.

Affiliate Dashboard for Members (Frontend)

Partner Program Registration:

"Become a Partner" page.

Edge Function apply-for-partner-program updates a flag in profiles table or inserts into a partner_applications table. Admin approves.

Partner Dashboard (fetches data via Edge Functions from Supabase):

Performance metrics (clicks, conversions, earnings from affiliate_clicks, orders, partner_commissions).

Generated links.

Commission rates (logic in Edge Function combining Sanity product data and user's profiles.membership_tier_id).

Payout history (from transactions where type = 'earning_partner_commission').

Product Promotion Tools:

Page to browse eligible products (from Sanity).

Edge Function generate-partner-link using profiles.referral_code (this is their affiliate code).

Affiliate Link Generation System

Link Generator (Edge Function as above):

Format: earnhub.com/p/{product_id}?ref={user_referral_code} or earnhub.com/shop?ref={user_referral_code}.

Tracking System (Supabase Tables):

public.affiliate_clicks (stores affiliate_code_used, partner_user_id derived from code, click details).

public.orders (stores affiliate_click_id or affiliate_code_used_on_order).

public.partner_commissions (calculates commission based on sales).

Public-Facing Affiliate Product Pages (Frontend)

Landing Pages accessible without login.

Streamlined Checkout: Retain ref code (e.g., in cookie/session storage) through checkout.

Frontend pre-fills coupon/referral code if applicable, or backend create-order Edge Function uses the stored ref code.

Commission Management System (Supabase Edge Functions & DB)

Commission Calculation (Edge Function calculate-commissions triggered after paid order):

Uses orders.affiliate_click_id or affiliate_code_used_on_order to find the partner.

Fetches partner's profiles.membership_tier_id.

Fetches product's commission rules from Sanity.

Calculates and inserts into public.partner_commissions with status = 'pending_approval'.

Payout Processing (Admin action triggering an Edge Function):

Admin approves partner_commissions.

Edge Function sums approved amounts for partners.

Creates transactions (type earning_partner_commission) and updates wallets.balance.

Updates partner_commissions.status = 'paid'.

This is a comprehensive plan to implement the EarnHub Partner Program with Supabase.

For your high-value products like courses, web development services, and business support bundles, you could offer significantly higher commission rates (30-40%) to incentivize partners to promote these specifically.

The payment method will be a STK push provider service called Lipia Online. The documentation is: (Include the Lipia Online API documentation you provided previously here.)

##Please note that the callback here acts as a TRUE confirmation of successful payment so if user pays successfully, I want the user's account to be upgraded accordingly (if upgrading membership via Lipia Online) and have upgraded features available to them. This app needs clear restrictions and user access and permissions placed. Supabase RLS will be used for this.

The web tech stack currently is: Vite, TypeScript, React, shadcn-ui, Tailwind CSS.

So here is some info on my project. currently just a bunch of frontend work done but more is needed. I need help implementing more of the features especially in the backend and make robust plans towards that. Given all this information, help me do that. I need to configure the sanity cms stuff, the supabase database structure, and also the payment method which will be a stk push provider service called lipia online which i will need to perhaps configure using supabase db as well. The documentation is: "For the payment flow, I was thinking of using Lipia-online for users to pay and I receive money... of which they have a developer api: Docs Pages GETTING STARTED Payment API Documentation This describes how to use the Payment API provided by Lipia to make payments. The base URL for the API is https://lipia-api.kreativelabske.com/api Sending a Payment Request To initiate a payment, send a POST request to the following endpoint: /request/stk The request should include a JSON body with the following parameters: { "phone": "**********", "amount": "1" } The phone parameter should be a valid phone number in the format 07XXXXXXXX, and the amount parameter should be a number indicating the amount to be paid. Authentication The Payment API requires authentication using an API key. Get your apiKey from your apps dashboard, Security Tab To authenticate your requests, include your API key in the Authorization header of your requests as follows: Authorization: Bearer {api_key} Replace {api_key} with your actual API key from your app dashboard. Successful Payment Response If the payment is successful, the API will return a JSON response with the following structure: { "message": "callback received successfully", "data": { "amount": "1", "phone": "**********", "reference": "RD37AV1CXF", "CheckoutRequestID": "ws_CO_0304202315472321**********" } } The reference parameter is theMPESA unique identifier for the payment, and the CheckoutRequestID parameter is a reference ID that can be used later to track the payment status incase of a callback failure. Error Responses If an error occurs during the payment process, the API will return a JSON response with the following structure: { "message": "invalid phone number" } The message parameter will contain a brief description of the error that occurred. Possible error messages include: invalid phone number Request cancelled by user insuccifient user balance user took too long to pay Support" Please note that the callback here acts as a TRUE confirmation of successful payment so if user pays successfully, I want the user's account to be upgraded accordingly (if upgrading membership via lipia online) and have upgraded features available to them. This app needs clear restrictions and user access and permissions placed. supabase please.

Supabase DB Table Schemas "Okay, let's pivot and outline a Supabase database plan for EarnHub, leveraging its features and keeping your Sanity integration and full application scope in mind. Supabase uses PostgreSQL, so the SQL is standard, but it offers built-in Auth, Row Level Security (RLS), Storage, Edge Functions, and Realtime capabilities that we'll factor in. Core Philosophy with Supabase for EarnHub: Supabase Auth for User Management: Use Supabase's built-in authentication for sign-up, sign-in, password reset, social logins, etc. This creates records in auth.users and auth.identities. profiles Table for User Application Data: A public profiles table linked 1:1 with auth.users to store your application-specific user data. Standard PostgreSQL Schemas: Organize your tables into logical schemas (e.g., public for most, or custom schemas like shop, partner_program if desired for extreme organization, though public is fine for most cases to start). Row Level Security (RLS) for Data Access Control: This is PARAMOUNT. RLS policies will control who can see and modify what data, primarily based on auth.uid() (the logged-in user's ID). Foreign Keys for Relational Integrity: Standard PostgreSQL foreign keys. Database Functions & Triggers: Allowed and useful (e.g., for updated_at or more complex data manipulations). Supabase Edge Functions for Backend Logic: Your backend API endpoints (like the Express routes you drafted) can be implemented as Supabase Edge Functions (Deno/TypeScript). Sanity Integration: Your frontend/Edge Functions will fetch content from Sanity as needed using the Sanity client. Supabase Database Plan for EarnHub Phase 0: Supabase Project Setup Create a Supabase Project: This will provision a PostgreSQL database, Auth, Storage, etc. Enable RLS: Ensure RLS is enabled by default for new tables in your Supabase project settings (it usually is). Authentication Settings: Configure your auth providers (Email/Password is a start, consider adding Social Logins). Enable "Email confirmations" for sign-ups. Step 1: Helper Function for updated_at (Optional but Recommended) (You can create this in the Supabase SQL Editor) -- public.moddatetime function (common utility for auto-updating timestamps) CREATE OR REPLACE FUNCTION public.moddatetime() RETURNS TRIGGER AS $$ BEGIN NEW.updated_at = timezone('utc', now()); RETURN NEW; END; $$ LANGUAGE plpgsql; Use code with caution. SQL (You'll apply this via triggers on tables that have an updated_at column). Step 2: Core User Data Tables (in public schema) -- profiles Table (Extends Supabase's auth.users) -- RLS will be crucial here to ensure users can only access/modify their own profile. CREATE TABLE public.profiles ( id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE, -- Links 1:1 to Supabase auth users full_name VARCHAR(255), username VARCHAR(50) UNIQUE, -- email is in auth.users, usually no need to duplicate here created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()), account_status VARCHAR(20) DEFAULT 'active' CHECK (account_status IN ('active', 'pending_verification', 'suspended', 'deactivated')), membership_tier_id VARCHAR(50) DEFAULT 'starter', -- References Sanity membershipTier.tierId membership_expiry_date TIMESTAMPTZ, total_points INTEGER DEFAULT 0, is_ambassador BOOLEAN DEFAULT FALSE, referral_code VARCHAR(50) UNIQUE, last_login TIMESTAMPTZ, -- Profile specific fields: gender VARCHAR(20), age_range VARCHAR(20), county VARCHAR(100), country VARCHAR(100) DEFAULT 'Kenya', income_level VARCHAR(50), education_level VARCHAR(50), social_media JSONB, availability VARCHAR(50), avatar_sanity_id VARCHAR(255), -- Or avatar_url TEXT bio TEXT, profile_completion_percentage SMALLINT DEFAULT 0 CHECK (profile_completion_percentage >= 0 AND profile_completion_percentage <= 100) );

-- RLS Policy for profiles (Example - adapt as needed) ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile." ON public.profiles FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile." ON public.profiles FOR UPDATE USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- Trigger for updated_at on profiles CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE PROCEDURE public.moddatetime();

-- user_skills Table CREATE TABLE public.user_skills ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, skill_category_sanity_id VARCHAR(255) NOT NULL, skill_name VARCHAR(100) NOT NULL, proficiency_level VARCHAR(50) CHECK (proficiency_level IN ('beginner', 'intermediate', 'advanced', 'expert')), created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), UNIQUE (user_id, skill_name) ); ALTER TABLE public.user_skills ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own skills." ON public.user_skills FOR ALL USING (auth.uid() = user_id);

-- user_interests Table CREATE TABLE public.user_interests ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, interest_name VARCHAR(100) NOT NULL, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), UNIQUE (user_id, interest_name) ); ALTER TABLE public.user_interests ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own interests." ON public.user_interests FOR ALL USING (auth.uid() = user_id);

-- user_devices Table (For Push Notifications via Supabase/external service) CREATE TABLE public.user_devices ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, device_type VARCHAR(50) NOT NULL CHECK (device_type IN ('android', 'ios', 'web_push', 'other')), device_token TEXT NOT NULL UNIQUE, -- Token itself should be unique globally last_seen TIMESTAMPTZ DEFAULT timezone('utc', now()), is_active BOOLEAN DEFAULT TRUE, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) ); ALTER TABLE public.user_devices ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own devices." ON public.user_devices FOR ALL USING (auth.uid() = user_id); CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.user_devices FOR EACH ROW EXECUTE PROCEDURE public.moddatetime(); Use code with caution. SQL Step 3: Financial Data Tables -- wallets Table CREATE TABLE public.wallets ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL UNIQUE REFERENCES public.profiles(id) ON DELETE CASCADE, -- Typically one wallet per user (or per currency for user) -- If multi-currency, remove UNIQUE on user_id and add UNIQUE (user_id, currency) balance DECIMAL(14, 2) DEFAULT 0.00 CHECK (balance >= 0), pending_balance DECIMAL(14, 2) DEFAULT 0.00 CHECK (pending_balance >=0), currency VARCHAR(10) DEFAULT 'KES' NOT NULL, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) -- Add UNIQUE (user_id, currency) if users can have multiple currency wallets ); ALTER TABLE public.wallets ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can view their own wallet." ON public.wallets FOR SELECT USING (auth.uid() = user_id); -- Wallet updates should typically be done via trusted Edge Functions, not direct client updates. CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.wallets FOR EACH ROW EXECUTE PROCEDURE public.moddatetime();

-- transactions Table CREATE TABLE public.transactions ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), wallet_id UUID NOT NULL REFERENCES public.wallets(id) ON DELETE RESTRICT, user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, -- Denormalized for RLS/querying amount DECIMAL(14, 2) NOT NULL, type VARCHAR(50) NOT NULL CHECK (type IN ( 'deposit', 'withdrawal', 'earning_quest', 'earning_referral', 'earning_competition_prize', 'earning_raffle_win', 'earning_partner_commission', 'spend_membership_payment', 'spend_product_purchase', 'spend_raffle_ticket_purchase', 'spend_competition_entry_fee', 'refund_product', 'refund_membership', 'platform_bonus', 'fee_withdrawal', 'fee_platform_service', 'adjustment_credit', 'adjustment_debit', 'other' )), status VARCHAR(20) DEFAULT 'pending' NOT NULL CHECK (status IN ('pending', 'pending_payment', 'completed', 'failed', 'cancelled', 'reversed', 'expired')), description TEXT, reference_entity_type VARCHAR(50), reference_entity_id VARCHAR(255), payment_processor_checkout_id VARCHAR(255), payment_processor_reference VARCHAR(255), payment_purpose_details JSONB, notes TEXT, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) ); ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can view their own transactions." ON public.transactions FOR SELECT USING (auth.uid() = user_id); -- Inserts/updates to transactions should be done via trusted Edge Functions. CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.transactions FOR EACH ROW EXECUTE PROCEDURE public.moddatetime();

-- payment_methods Table (If users store payment methods, e.g., for future auto-renewals NOT in MVP) CREATE TABLE public.payment_methods ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, method_type VARCHAR(50) NOT NULL, -- e.g., 'card_stripe', 'paypal_recurring' details JSONB, -- Stripe PaymentMethod ID, etc. is_default BOOLEAN DEFAULT FALSE, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) ); ALTER TABLE public.payment_methods ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own payment methods." ON public.payment_methods FOR ALL USING (auth.uid() = user_id); CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.payment_methods FOR EACH ROW EXECUTE PROCEDURE public.moddatetime();

-- membership_purchases Table (For one-off membership payments) CREATE TABLE public.membership_purchases ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, sanity_membership_tier_id VARCHAR(50) NOT NULL, status VARCHAR(20) DEFAULT 'pending_payment' NOT NULL CHECK (status IN ('pending_payment', 'completed', 'failed', 'refunded')), purchase_date TIMESTAMPTZ, amount_paid DECIMAL(10, 2), currency VARCHAR(10) DEFAULT 'KES', transaction_id UUID UNIQUE REFERENCES public.transactions(id) ON DELETE SET NULL, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) ); ALTER TABLE public.membership_purchases ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can view their own membership purchases." ON public.membership_purchases FOR SELECT USING (auth.uid() = user_id); -- Inserts/updates via Edge Functions. CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.membership_purchases FOR EACH ROW EXECUTE PROCEDURE public.moddatetime(); Use code with caution. SQL Step 4: Engagement Data Tables (RLS Policies will typically allow users to manage/view their own entries, and public reads for leaderboards if applicable, handled by specific views or Edge Functions with appropriate filters). -- user_quests Table CREATE TABLE public.user_quests ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, sanity_quest_id VARCHAR(255) NOT NULL, status VARCHAR(50) DEFAULT 'started' NOT NULL CHECK (status IN ('started', 'in_progress', 'submitted_for_review', 'requires_revision', 'approved', 'rejected', 'completed_paid', 'expired')), submission_data JSONB, feedback TEXT, earned_amount DECIMAL(10, 2), earned_points INTEGER, reward_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL, started_at TIMESTAMPTZ DEFAULT timezone('utc', now()), submitted_at TIMESTAMPTZ, reviewed_at TIMESTAMPTZ, completed_at TIMESTAMPTZ, updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()), UNIQUE (user_id, sanity_quest_id) ); ALTER TABLE public.user_quests ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own quest progress." ON public.user_quests FOR ALL USING (auth.uid() = user_id); CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.user_quests FOR EACH ROW EXECUTE PROCEDURE public.moddatetime();

-- competition_entries Table CREATE TABLE public.competition_entries ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, sanity_competition_id VARCHAR(255) NOT NULL, score BIGINT DEFAULT 0, rank INTEGER, entry_fee_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL, joined_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()), notes TEXT, UNIQUE (user_id, sanity_competition_id) ); ALTER TABLE public.competition_entries ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own competition entries." ON public.competition_entries FOR ALL USING (auth.uid() = user_id); -- Allow public read for leaderboards, possibly via a security definer function or specific view CREATE POLICY "Public can read competition entries for leaderboards." ON public.competition_entries FOR SELECT USING (true); -- Further filtering might be needed for specific data in a view CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.competition_entries FOR EACH ROW EXECUTE PROCEDURE public.moddatetime();

-- raffle_entries Table CREATE TABLE public.raffle_entries ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, sanity_raffle_id VARCHAR(255) NOT NULL, ticket_id VARCHAR(100) UNIQUE NOT NULL, ticket_purchase_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL, entry_time TIMESTAMPTZ DEFAULT timezone('utc', now()), is_winner BOOLEAN DEFAULT FALSE, prize_claim_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL, claimed_at TIMESTAMPTZ -- If multiple entries per user per raffle, remove UNIQUE on (user_id, sanity_raffle_id) ); ALTER TABLE public.raffle_entries ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own raffle entries." ON public.raffle_entries FOR ALL USING (auth.uid() = user_id); CREATE POLICY "Public can read raffle winner info (selectively)." ON public.raffle_entries FOR SELECT USING (is_winner = true); -- Example, refine for actual data exposure

-- achievement_progress Table CREATE TABLE public.achievement_progress ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, sanity_achievement_type_id VARCHAR(255) NOT NULL, current_progress INTEGER DEFAULT 0, target_value INTEGER DEFAULT 1, completed BOOLEAN DEFAULT FALSE, completed_at TIMESTAMPTZ, reward_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()), UNIQUE (user_id, sanity_achievement_type_id) ); ALTER TABLE public.achievement_progress ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own achievement progress." ON public.achievement_progress FOR ALL USING (auth.uid() = user_id); CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.achievement_progress FOR EACH ROW EXECUTE PROCEDURE public.moddatetime();

-- referrals Table CREATE TABLE public.referrals ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), referrer_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, referee_user_id UUID UNIQUE REFERENCES public.profiles(id) ON DELETE SET NULL, referral_code_used VARCHAR(50) NOT NULL, -- Should match profiles.referral_code of referrer status VARCHAR(20) DEFAULT 'pending' NOT NULL CHECK (status IN ('pending', 'registered', 'criteria_met', 'rewarded', 'expired', 'invalid')), reward_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL, reward_amount_snapshot DECIMAL(10, 2), conditions_met_at TIMESTAMPTZ, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) ); ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can view their own referrals." ON public.referrals FOR SELECT USING (auth.uid() = referrer_user_id OR auth.uid() = referee_user_id); -- Inserts/updates via Edge Functions. CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.referrals FOR EACH ROW EXECUTE PROCEDURE public.moddatetime(); Use code with caution. SQL Step 5: Other Necessary Data Tables (Similar RLS patterns: users manage their own, public reads if necessary via specific views/functions). -- opportunity_applications Table CREATE TABLE public.opportunity_applications ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, sanity_opportunity_id VARCHAR(255) NOT NULL, status VARCHAR(30) DEFAULT 'submitted' NOT NULL CHECK (status IN ('submitted', 'under_review', 'shortlisted', 'interview_scheduled', 'offer_extended', 'offer_accepted', 'offer_declined', 'rejected', 'withdrawn')), application_date TIMESTAMPTZ DEFAULT timezone('utc', now()), cover_letter TEXT, attachments JSONB, -- Store Supabase Storage paths here notes TEXT, -- reviewed_by_admin_user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL, -- If admins are 'profiles' reviewed_at TIMESTAMPTZ, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()), UNIQUE (user_id, sanity_opportunity_id) ); ALTER TABLE public.opportunity_applications ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own opportunity applications." ON public.opportunity_applications FOR ALL USING (auth.uid() = user_id); -- Admins/Opportunity posters would need different access, likely via Edge Functions with service_role. CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.opportunity_applications FOR EACH ROW EXECUTE PROCEDURE public.moddatetime();

-- ambassador_applications Table CREATE TABLE public.ambassador_applications ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL UNIQUE REFERENCES public.profiles(id) ON DELETE CASCADE, application_text TEXT NOT NULL, status VARCHAR(30) DEFAULT 'pending_review' NOT NULL CHECK (status IN ('pending_review', 'approved', 'rejected', 'interview_scheduled', 'on_hold')), applied_at TIMESTAMPTZ DEFAULT timezone('utc', now()), -- reviewed_by_admin_user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL, reviewed_at TIMESTAMPTZ, reviewer_notes TEXT, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) ); ALTER TABLE public.ambassador_applications ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own ambassador application." ON public.ambassador_applications FOR ALL USING (auth.uid() = user_id); -- Admin review via Edge Functions. CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.ambassador_applications FOR EACH ROW EXECUTE PROCEDURE public.moddatetime();

-- user_notifications Table CREATE TABLE public.user_notifications ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, sanity_notification_template_id VARCHAR(255), title VARCHAR(255) NOT NULL, message TEXT NOT NULL, notification_type VARCHAR(50) NOT NULL, priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')), icon VARCHAR(100), action_url TEXT, is_read BOOLEAN DEFAULT FALSE, read_at TIMESTAMPTZ, emailed_at TIMESTAMPTZ, push_sent_at TIMESTAMPTZ, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) -- No updated_at typically for immutable notifications ); ALTER TABLE public.user_notifications ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own notifications." ON public.user_notifications FOR ALL USING (auth.uid() = user_id);

-- live_activities Table (Platform-wide or user-specific feed) CREATE TABLE public.live_activities ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL, sanity_activity_definition_key VARCHAR(100), rendered_message TEXT NOT NULL, related_entity_type VARCHAR(50), related_entity_id VARCHAR(255), icon VARCHAR(100), created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) ); ALTER TABLE public.live_activities ENABLE ROW LEVEL SECURITY; -- Allow authenticated users to read all live activities (can be refined) CREATE POLICY "Authenticated users can read live activities." ON public.live_activities FOR SELECT USING (auth.role() = 'authenticated'); -- Inserts via Edge Functions. Use code with caution. SQL Step 6: Partner Program and Shop Data Tables (RLS for these will be more complex, involving checks for partner status, order ownership, etc. Initial policies below are basic examples). -- partner_details Table CREATE TABLE public.partner_details ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID NOT NULL UNIQUE REFERENCES public.profiles(id) ON DELETE CASCADE, application_status VARCHAR(30) DEFAULT 'not_applied' NOT NULL CHECK (application_status IN ('not_applied', 'pending_review', 'approved', 'rejected', 'on_hold')), approved_at TIMESTAMPTZ, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) ); ALTER TABLE public.partner_details ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own partner application." ON public.partner_details FOR ALL USING (auth.uid() = user_id); -- Admin approval via Edge Functions. CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.partner_details FOR EACH ROW EXECUTE PROCEDURE public.moddatetime();

-- affiliate_clicks Table CREATE TABLE public.affiliate_clicks ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), affiliate_code_used VARCHAR(50) NOT NULL, -- Corresponds to a profiles.referral_code partner_user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL, clicked_at TIMESTAMPTZ DEFAULT timezone('utc', now()), ip_address INET, -- Use INET for IP addresses user_agent TEXT, referring_url TEXT, landing_page_url TEXT NOT NULL, sanity_product_id VARCHAR(255), sanity_affiliate_link_id VARCHAR(255) ); ALTER TABLE public.affiliate_clicks ENABLE ROW LEVEL SECURITY; -- Clicks are generally public to insert (from frontend/edge function), partners can see their own. CREATE POLICY "Partners can view their own clicks." ON public.affiliate_clicks FOR SELECT USING (auth.uid() = partner_user_id); CREATE POLICY "Allow public insert of clicks." ON public.affiliate_clicks FOR INSERT WITH CHECK (true); -- Further validation in Edge Function

-- orders Table CREATE TABLE public.orders ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL, -- Buyer customer_email VARCHAR(255) NOT NULL, customer_phone VARCHAR(50), shipping_address JSONB, billing_address JSONB, total_amount DECIMAL(12, 2) NOT NULL CHECK (total_amount >= 0), currency VARCHAR(10) DEFAULT 'KES' NOT NULL, status VARCHAR(30) DEFAULT 'pending_payment' NOT NULL CHECK (status IN ('pending_payment', 'paid', 'processing', 'shipped', 'delivered', 'completed', 'cancelled', 'refunded', 'failed')), payment_transaction_id UUID UNIQUE REFERENCES public.transactions(id) ON DELETE SET NULL, affiliate_click_id UUID REFERENCES public.affiliate_clicks(id) ON DELETE SET NULL, affiliate_code_used_on_order VARCHAR(50), discount_amount DECIMAL(10, 2) DEFAULT 0.00, shipping_fee DECIMAL(10, 2) DEFAULT 0.00, notes_to_seller TEXT, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) ); ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can manage their own orders." ON public.orders FOR ALL USING (auth.uid() = user_id); -- Admin/fulfillment via Edge Functions. CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.orders FOR EACH ROW EXECUTE PROCEDURE public.moddatetime();

-- order_items Table CREATE TABLE public.order_items ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE CASCADE, sanity_product_id VARCHAR(255) NOT NULL, product_name_snapshot VARCHAR(255) NOT NULL, quantity INTEGER NOT NULL CHECK (quantity > 0), price_at_purchase DECIMAL(12, 2) NOT NULL, total_line_item_amount DECIMAL(12, 2) NOT NULL, attributes_snapshot JSONB, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()) ); ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY; CREATE POLICY "Users can view items in their own orders." ON public.order_items FOR SELECT USING (EXISTS (SELECT 1 FROM public.orders o WHERE o.id = order_id AND o.user_id = auth.uid())); -- Inserts via Edge Functions as part of order creation.

-- partner_commissions Table CREATE TABLE public.partner_commissions ( id UUID PRIMARY KEY DEFAULT gen_random_uuid(), order_item_id UUID NOT NULL UNIQUE REFERENCES public.order_items(id) ON DELETE CASCADE, order_id UUID NOT NULL REFERENCES public.orders(id) ON DELETE RESTRICT, partner_user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE, commission_amount DECIMAL(12, 2) NOT NULL CHECK (commission_amount >= 0), commission_rate_snapshot DECIMAL(5,2), product_value_snapshot DECIMAL(12,2), status VARCHAR(30) DEFAULT 'pending_approval' NOT NULL CHECK (status IN ('pending_approval', 'approved', 'paid', 'rejected', 'clawed_back')), payout_transaction_id UUID REFERENCES public.transactions(id) ON DELETE SET NULL, notes TEXT, created_at TIMESTAMPTZ DEFAULT timezone('utc', now()), reviewed_at TIMESTAMPTZ, paid_at TIMESTAMPTZ, updated_at TIMESTAMPTZ DEFAULT timezone('utc', now()) ); ALTER TABLE public.partner_commissions ENABLE ROW LEVEL SECURITY; CREATE POLICY "Partners can view their own commissions." ON public.partner_commissions FOR SELECT USING (auth.uid() = partner_user_id); -- Inserts/updates via Edge Functions. CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.partner_commissions FOR EACH ROW EXECUTE PROCEDURE public.moddatetime(); Use code with caution. SQL Sanity Integration Plan: sanity.config.ts & Schemas: Your existing Sanity schemas are largely fine. You'll use the \_id from Sanity documents (e.g., membershipTier.\_id, quest.\_id, product.\_id) to store in the corresponding Supabase columns (e.g., profiles.membership_tier_id, user_quests.sanity_quest_id, order_items.sanity_product_id). Data Fetching: Frontend: Can fetch directly from Sanity for public content (news, FAQs, product listings, opportunity listings, task/quest definitions). Supabase Edge Functions: Can fetch from Sanity if backend logic needs Sanity data (e.g., fetching membership tier benefits when processing a payment, fetching product commission rules for calculateCommissionService). Use the Sanity JS client library within your Edge Functions. Key Supabase Concepts Used: auth.users: Supabase's built-in user table. auth.uid(): SQL function to get the ID of the currently authenticated user (crucial for RLS). auth.role(): SQL function to get the role of the current user (e.g., authenticated, anon). RLS Policies: Define access control at the database row level. Edge Functions: For all backend logic that needs to be secure, interact with external APIs (Lipia), or perform privileged database operations (like creating transactions, updating wallets, approving commissions). Client-side code should not directly modify sensitive tables like wallets or transactions. Storage: Supabase Storage can be used for user uploads (e.g., CVs for opportunity applications, submission files for quests, product images if not solely in Sanity). Store the path/URL in your database tables. Next Steps After DDL: Implement RLS Policies: The examples above are starting points. Review each table and define comprehensive RLS. Build Supabase Edge Functions: Port your Express route logic (payment initiation, webhooks, commission calculation, affiliate click logging) into Supabase Edge Functions. These functions will use the Supabase JS client to interact with your database and the Sanity client to fetch CMS data. Frontend Integration: Use Supabase JS client for auth (sign-up, login), user profile management, and calling your Edge Functions. Use Sanity client for fetching CMS content. Admin Panel: Consider using an existing admin panel tool that can connect to PostgreSQL (like Retool, Appsmith, or build a custom one) or build admin features within your app that call specific privileged Edge Functions. Admin operations (approving commissions, reviewing quests) should not bypass RLS without using the service_role key securely in an Edge Function. This Supabase plan provides a more standard PostgreSQL experience while leveraging powerful BaaS features. The RLS setup is critical for security. Good luck! prompt: "I want to implement a feature that allows an earnhub member to earn money by reselling the items in the shop area for a commission: like want to earn money as an earnhub partner? Join now! and lets say for things I develop myself like courses, services e.g CV revamping, website creation, business support bundles/packs that include a couple benefits, an online business starter pack that includes a guide and an e-commerce website made for users...(and any other idea for services and bundles I can offer) paid memberships/subscriptions, softwares...the commission will be higher e.g whatsapp bot for businesses at 10k, a business website creation service at 10k...user gets 40% commission or higher depending on membership tier... Yes I want to implement this so users have products they can resell...and perhaps allow the users in this program (I need a good name for it) to have a unique link they can give to the people they are reselling to...so they can buy the products. the links should lead to pages made just for buying stuff: like a whole separate module. user doesn't have to sign up or anything, just have to see a PDP, some products, an entire checkout flow... The links should be tagged with a tracker in order to track sales and reimburse sellers. The tag could just be a coupon code which could be used by the customer at checkout(autofilled), just like how an earnhub referral link works. Users can get access to individual product links to share to interested buyers, or a consolidated link." Based on my research, I now understand how the shop area is structured and how the EarnHub Partner Program should be integrated. Let me outline a comprehensive plan for implementing this feature: EarnHub Partner Program Implementation Plan Sanity Schema Updates Create a Partner Program Schema Create a new schema called partnerProgram with tiers, commission rates, and requirements Update the product schema to include affiliate information such as: commissionRate (percentage of sale) partnerEligible (boolean to mark products eligible for the program) commissionTiers (different rates based on membership level) Create an Affiliate Link Schema Track affiliate links and their performance Store information on the referrer, generated links, and conversion metrics Affiliate Dashboard for Members Partner Program Registration Create a "Become a Partner" page with program benefits and registration form Add application flow to join the program with agreement terms Include membership tier benefits comparison Partner Dashboard Create an affiliate dashboard showing: Performance metrics (clicks, conversions, earnings) Generated links and their performance Available products to promote Commission rates based on user's tier Payout history Product Promotion Tools Create a page where partners can browse eligible products Generate unique affiliate links for specific products Create marketing materials and resources partners can use Add social share functionality for easy promotion Affiliate Link Generation System Link Generator Create a system to generate unique, trackable affiliate links Format: earnhub.com/p/{product-id}?ref={affiliate-code} Add functionality to create general store links or product-specific links Tracking System Create a database schema to track: Link clicks Conversion rates Sales attributed to affiliates Commission calculations Public-Facing Affiliate Product Pages Landing Pages for Non-Members Create public product landing pages accessible without login These pages should focus on conversion, with clear product benefits Include the EarnHub branding but simplified navigation Streamlined Checkout Process Create a simplified checkout process for affiliate customers Retain affiliate code through the entire purchase journey Pre-fill coupon/referral code field based on the URL parameters Commission Management System Commission Calculation Create a system to calculate affiliate commissions based on: Product's commission rate Affiliate's membership tier Special promotions or bonuses Payout Processing Add functionality to process affiliate payouts Create a verification system for valid sales Integrate with wallet system for seamless payments Backend Integration Database Schema for supabase DB Create tables for affiliate tracking Implement tenant isolation for affiliate data Set up commission calculation and attribution logic API Endpoints Create endpoints for: Generating affiliate links Tracking clicks and conversions Retrieving affiliate performance data Managing affiliate payouts This is a comprehensive plan to implement the EarnHub Partner Program where members can earn commissions by reselling products from the shop. The implementation would involve several key components: Partner Program Structure: Members can sign up to become affiliates/partners Tiered commission structure based on membership level (higher tiers get higher commissions) Special focus on your own products (courses, services, software) with higher commission rates Affiliate Link System: Unique trackable links for each partner Product-specific links that partners can share Simple dashboard for tracking performance Public Shop Experience: Special landing pages for affiliate-referred customers Streamlined checkout process that doesn't require signup Automatic attribution via URL parameters or coupon codes Commission Management: Automatic tracking of sales through affiliate links Commission calculation based on product type and membership tier Integration with the existing wallet system for payouts The system would require updates to both the Sanity CMS schemas to track affiliate-eligible products and commission rates, as well as new database tables in your backend to track affiliate performance and calculate commissions. For your high-value products like courses, web development services, and business support bundles, you could offer significantly higher commission rates (30-40%) to incentivize partners to promote these specifically."
