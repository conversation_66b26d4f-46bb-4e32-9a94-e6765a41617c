
import { useEffect } from 'react';
import Navbar from '@/components/Navbar';
import Hero from '@/components/Hero';
import Features from '@/components/Features';
import Membership from '@/components/Membership';
import Testimonials from '@/components/Testimonials';
import CTA from '@/components/CTA';
import Footer from '@/components/Footer';
import TrustBadges from '@/components/TrustBadges';
import BenefitsBanner from '@/components/BenefitsBanner';
import LimitedTimeOffer from '@/components/LimitedTimeOffer';
import { Helmet } from 'react-helmet-async';

const Index = () => {
  // Add smooth scroll behavior for anchor links
  useEffect(() => {
    const handleAnchorClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const anchor = target.closest('a');
      
      if (anchor && anchor.hash && anchor.hash.startsWith('#') && anchor.pathname === window.location.pathname) {
        e.preventDefault();
        
        const targetElement = document.querySelector(anchor.hash);
        if (targetElement) {
          const navbarHeight = 80; // Approximate navbar height
          const targetPosition = targetElement.getBoundingClientRect().top + window.scrollY - navbarHeight;
          
          window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });
        }
      }
    };
    
    document.addEventListener('click', handleAnchorClick);
    return () => document.removeEventListener('click', handleAnchorClick);
  }, []);
  
  return (
    <div className="min-h-screen bg-white overflow-x-hidden">
      <Helmet>
        <title>EarnHub - Turn Ambition Into Income</title>
        <meta name="description" content="Join thousands of Kenyans earning up to KES 50,000 monthly with EarnHub. Start earning through referrals, tasks, competitions, and more!" />
      </Helmet>
      <Navbar />
      <Hero />
      <TrustBadges />
      <BenefitsBanner />
      <Features />
      <LimitedTimeOffer />
      <Membership />
      <Testimonials />
      <CTA />
      <Footer />
    </div>
  );
};

export default Index;
