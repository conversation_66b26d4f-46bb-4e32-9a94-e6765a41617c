
import React from 'react';
import { Grid } from 'lucide-react';
import { CompetitionType } from '@/types/competitions';
import CompetitionCard from './CompetitionCard';

interface CompetitionsListProps {
  competitions: CompetitionType[];
}

const CompetitionsList: React.FC<CompetitionsListProps> = ({ competitions }) => {
  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {competitions.map((competition) => (
          <CompetitionCard key={competition.id} competition={competition} />
        ))}
      </div>
      
      {competitions.length === 0 && (
        <div className="flex flex-col items-center justify-center py-16">
          <Grid className="h-12 w-12 text-gray-300 mb-4" />
          <h3 className="text-xl font-medium text-gray-500">No competitions found</h3>
          <p className="text-gray-400">Check back later for new competitions</p>
        </div>
      )}
    </div>
  );
};

export default CompetitionsList;
