
export default {
  name: 'affiliateLink',
  title: 'Affiliate Link',
  type: 'document',
  fields: [
    {
      name: 'code',
      title: 'Affiliate Code',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'partnerId',
      title: 'Partner ID',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'product',
      title: 'Product',
      type: 'reference',
      to: [{type: 'product'}]
    },
    {
      name: 'isGeneralLink',
      title: 'General Store Link',
      type: 'boolean',
      description: 'If true, this is a general store link not tied to a specific product',
      initialValue: false
    },
    {
      name: 'dateCreated',
      title: 'Date Created',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
      readOnly: true
    },
    {
      name: 'clicks',
      title: 'Total Clicks',
      type: 'number',
      initialValue: 0,
      readOnly: true
    },
    {
      name: 'conversions',
      title: 'Total Conversions',
      type: 'number',
      initialValue: 0,
      readOnly: true
    },
    {
      name: 'revenue',
      title: 'Total Revenue Generated',
      type: 'number',
      initialValue: 0,
      readOnly: true
    },
    {
      name: 'commission',
      title: 'Total Commission Earned',
      type: 'number',
      initialValue: 0,
      readOnly: true
    },
    {
      name: 'customName',
      title: 'Custom Link Name',
      type: 'string',
      description: 'Optional custom name for this link'
    }
  ]
}
