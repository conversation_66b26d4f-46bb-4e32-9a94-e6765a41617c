
import React from 'react';
import { CompetitionType } from '@/types/competitions';

interface CompetitionRulesProps {
  competition: CompetitionType;
}

const CompetitionRules: React.FC<CompetitionRulesProps> = ({ competition }) => {
  return (
    <div className="space-y-6">
      <div className="prose max-w-none">
        <h3 className="text-xl font-semibold mb-4">Competition Rules</h3>
        
        <div className="bg-gray-50 p-6 rounded-lg">
          <ul className="list-disc pl-6 space-y-3">
            {competition.rules && competition.rules.map((rule, index) => (
              <li key={index} className="text-gray-700">{rule}</li>
            ))}
            
            {(!competition.rules || competition.rules.length === 0) && (
              <p className="text-gray-500 italic">No specific rules have been defined for this competition.</p>
            )}
          </ul>
        </div>
        
        <div className="mt-8">
          <h4 className="text-lg font-medium mb-3">General Rules</h4>
          <div className="bg-gray-50 p-6 rounded-lg">
            <ul className="list-disc pl-6 space-y-3">
              <li className="text-gray-700">EarnHub reserves the right to disqualify any participant who violates the rules or terms of service.</li>
              <li className="text-gray-700">Prizes will be awarded within 30 days of competition completion.</li>
              <li className="text-gray-700">In the event of a tie, the prize will be awarded to the participant who reached the score first.</li>
              <li className="text-gray-700">EarnHub reserves the right to modify competition parameters if necessary.</li>
              <li className="text-gray-700">Participation in competitions implies acceptance of these rules.</li>
            </ul>
          </div>
        </div>
        
        <div className="mt-8">
          <h4 className="text-lg font-medium mb-3">Eligibility</h4>
          <div className="bg-gray-50 p-6 rounded-lg">
            <ul className="list-disc pl-6 space-y-3">
              <li className="text-gray-700">All verified EarnHub users are eligible to participate.</li>
              <li className="text-gray-700">EarnHub staff and their immediate family members are not eligible for prizes.</li>
              <li className="text-gray-700">Users must have a complete profile to be eligible for prizes.</li>
              <li className="text-gray-700">Users must be at least 18 years old or the minimum legal age in their jurisdiction.</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompetitionRules;
