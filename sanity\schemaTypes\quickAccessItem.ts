// sanity/schemaTypes/quickAccessItem.ts
import {Rule} from 'sanity'

export default {
  name: 'quickAccessItem',
  title: 'Quick Access Item',
  type: 'document',
  fields: [
    {
      name: 'name',
      title: 'Name',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'icon',
      title: 'Icon (Lucide Icon Name)',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
      options: {
        list: [
          {title: 'Zap', value: 'Zap'}, // Tasks/Quests
          {title: 'Briefcase', value: 'Briefcase'}, // Opportunities
          {title: 'Award', value: 'Award'}, // Achievements
          {title: 'User', value: 'User'}, // My Profile
          {title: 'Medal', value: 'Medal'}, // Competitions
          {title: 'Ticket', value: 'Ticket'}, // Raffles
          {title: 'ShoppingCart', value: 'ShoppingCart'}, // Marketplace/Shop
          {title: 'Wallet', value: 'Wallet'}, // Wallet/Earnings
          {title: 'Settings', value: 'Settings'}, // Settings
          {title: 'LogOut', value: 'LogOut'}, // Logout
          {title: 'UsersRound', value: 'UsersRound'}, // Partner Program / Referrals
          {title: 'ShoppingBag', value: 'ShoppingBag'}, // User's Store (if custom store enabled)
        ],
      },
    },
    {
      name: 'path',
      title: 'Path (URL)',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description (Optional)',
      type: 'text',
    },
    {
      name: 'order',
      title: 'Display Order',
      type: 'number',
    },
    {
      name: 'requiresAuth',
      title: 'Requires Authentication',
      type: 'boolean',
      initialValue: true,
    },
    {
      name: 'userRoles',
      title: 'Visible to User Roles (Optional)',
      type: 'array',
      of: [{type: 'string'}],
      description:
        'e.g., basic, silver, gold, ambassador. Empty means visible to all authenticated if requiresAuth is true.',
    },
    {
      name: 'mobileOnly',
      title: 'Mobile Only',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'desktopOnly',
      title: 'Desktop Only',
      type: 'boolean',
      initialValue: false,
    },
  ],
}
