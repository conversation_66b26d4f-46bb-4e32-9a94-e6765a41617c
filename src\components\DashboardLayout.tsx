
import { ReactNode, useEffect, useState } from 'react';
import BottomNav from './BottomNav';
import DesktopSidebar from './DesktopSidebar';
import { SidebarProvider, useSidebar } from "@/components/ui/sidebar";
import anime from 'animejs';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import { ArrowLeft, Menu } from 'lucide-react';

interface DashboardLayoutProps {
  children: ReactNode;
}

// Inner component to access sidebar context
const DashboardContent = ({ children }: { children: ReactNode }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const isDashboard = location.pathname === '/dashboard';
  const { open: sidebarOpen, setOpen: setSidebarOpen } = useSidebar();

  // Add page transition animation
  useEffect(() => {
    anime({
      targets: '.dashboard-content',
      opacity: [0, 1],
      translateY: [10, 0],
      easing: 'easeOutExpo',
      duration: 700
    });
  }, [location.pathname]);

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen flex w-full bg-white">
      <DesktopSidebar />
      <div className="flex-1 min-w-0 dashboard-content relative">
        
        {/* Back button for non-dashboard pages */}
        {!isDashboard && (
          <Button
            variant="ghost"
            size="icon"
            onClick={handleBack}
            className="fixed md:absolute left-16 top-4 md:left-4 z-40"
          >
            <ArrowLeft size={24} />
            <span className="sr-only">Go back</span>
          </Button>
        )}

        {/* Reopen sidebar button for desktop (when sidebar is closed) */}
        {!sidebarOpen && (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(true)}
            className="hidden md:flex fixed left-4 top-4 z-40"
          >
            <Menu size={24} />
            <span className="sr-only">Open sidebar</span>
          </Button>
        )}
        
        {/* Main content with consistent spacing */}
        <main className="pb-20 md:pb-0 pt-16 md:pt-4">
          {children}
        </main>
        
        {/* Fixed bottom navigation for mobile */}
        <BottomNav />
      </div>
    </div>
  );
};

const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  return (
    <SidebarProvider defaultOpen={true}>
      <DashboardContent children={children} />
    </SidebarProvider>
  );
};

export default DashboardLayout;
