
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import CompetitionsList from "@/components/competitions/CompetitionsList";
import { CompetitionType } from "@/types/competitions";

const Competitions = () => {
  const [activeTab, setActiveTab] = useState<string>("active");

  // Sample competitions data (this would come from Sanity CMS in production)
  const competitions: CompetitionType[] = [
    {
      id: "1",
      title: "Summer Referral Challenge",
      description: "Refer the most friends and win amazing prizes!",
      type: "referral",
      image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b",
      startDate: new Date(2025, 4, 1).toISOString(),
      endDate: new Date(2025, 5, 15).toISOString(),
      participants: 128,
      prizes: [
        { position: 1, reward: "$500 Cash" },
        { position: 2, reward: "$250 Cash" },
        { position: 3, reward: "$100 Cash" }
      ],
      userPosition: 5,
      status: "active"
    },
    {
      id: "2",
      title: "Coding Championship",
      description: "Show off your coding skills and compete with other developers",
      type: "task",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158",
      startDate: new Date(2025, 4, 10).toISOString(),
      endDate: new Date(2025, 6, 10).toISOString(),
      participants: 76,
      prizes: [
        { position: 1, reward: "MacBook Pro" },
        { position: 2, reward: "iPad Pro" },
        { position: 3, reward: "AirPods Pro" }
      ],
      userPosition: 12,
      status: "active"
    },
    {
      id: "3",
      title: "Summer Sales Challenge",
      description: "Achieve the highest sales in the platform",
      type: "sales",
      image: "https://images.unsplash.com/photo-1605810230434-7631ac76ec81",
      startDate: new Date(2025, 6, 1).toISOString(),
      endDate: new Date(2025, 8, 30).toISOString(),
      participants: 0,
      prizes: [
        { position: 1, reward: "10% Commission Bonus" },
        { position: 2, reward: "5% Commission Bonus" },
        { position: 3, reward: "3% Commission Bonus" }
      ],
      userPosition: null,
      status: "upcoming"
    },
    {
      id: "4",
      title: "Spring Referral Contest",
      description: "Our previous referral competition with amazing prizes",
      type: "referral",
      image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6",
      startDate: new Date(2025, 2, 1).toISOString(),
      endDate: new Date(2025, 3, 30).toISOString(),
      participants: 215,
      prizes: [
        { position: 1, reward: "$300 Cash" },
        { position: 2, reward: "$150 Cash" },
        { position: 3, reward: "$75 Cash" }
      ],
      userPosition: 8,
      status: "past"
    }
  ];

  // Filter competitions based on active tab
  const activeCompetitions = competitions.filter(c => c.status === "active");
  const upcomingCompetitions = competitions.filter(c => c.status === "upcoming");
  const pastCompetitions = competitions.filter(c => c.status === "past");

  return (
    <div className="container mx-auto pt-24 pb-20">
      <h1 className="text-3xl font-bold mb-8">Competitions</h1>
      
      <Tabs defaultValue="active" onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-8">
          <TabsTrigger value="active" className="text-base">Active</TabsTrigger>
          <TabsTrigger value="upcoming" className="text-base">Upcoming</TabsTrigger>
          <TabsTrigger value="past" className="text-base">Past</TabsTrigger>
        </TabsList>
        
        <TabsContent value="active">
          <CompetitionsList competitions={activeCompetitions} />
          {activeCompetitions.length === 0 && (
            <div className="text-center py-16">
              <p className="text-lg text-gray-500">No active competitions at the moment.</p>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="upcoming">
          <CompetitionsList competitions={upcomingCompetitions} />
          {upcomingCompetitions.length === 0 && (
            <div className="text-center py-16">
              <p className="text-lg text-gray-500">No upcoming competitions at the moment.</p>
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="past">
          <CompetitionsList competitions={pastCompetitions} />
          {pastCompetitions.length === 0 && (
            <div className="text-center py-16">
              <p className="text-lg text-gray-500">No past competitions found.</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Competitions;
