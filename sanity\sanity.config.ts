// sanity/sanity.config.ts
import {defineConfig} from 'sanity'
import {structureTool} from 'sanity/structure'
import {visionTool} from '@sanity/vision'
import {schemaTypes} from './schemaTypes'
// It's good practice to import any icons you might use if you customize further
// import { CogIcon, ShoppingCartIcon, UsersIcon, MegaphoneIcon, StarIcon, FileTextIcon, HelpCircleIcon, SparklesIcon, ZapIcon, GiftIcon, TrophyIcon, CheckSquareIcon, BriefcaseIcon, BuildingIcon, BellIcon } from '@sanity/icons'

export default defineConfig({
  name: 'default',
  title: 'earnhub1',

  projectId: 'byjrfkgi', // Keep existing projectId
  dataset: 'production',   // Keep existing dataset

  plugins: [
    structureTool({
      structure: (S) =>
        S.list()
          .title('EarnHub Content')
          .items([
            // Platform Core Settings
            S.listItem()
              .title('Platform Core')
              // .icon(CogIcon) // Example icon
              .child(
                S.list()
                  .title('Core Settings')
                  .items([
                    S.listItem().title('Membership Tiers').child(S.documentTypeList('membershipTier').title('Membership Tiers')),
                    S.listItem().title('Achievement Types').child(S.documentTypeList('achievementType').title('Achievement Types')),
                    S.listItem().title('Profile Avatars').child(S.documentTypeList('profileAvatar').title('Profile Avatars')),
                    S.listItem().title('Skill Categories').child(S.documentTypeList('skillCategory').title('Skill Categories')),
                    S.listItem().title('Quick Access Items').child(S.documentTypeList('quickAccessItem').title('Quick Access Items')),
                  ])
              ),
            S.divider(),

            // Shop & Products
            S.listItem()
              .title('Shop Management')
              // .icon(ShoppingCartIcon) // Example icon
              .child(
                S.list()
                  .title('Shop')
                  .items([
                    S.listItem().title('All Products').child(S.documentTypeList('product').title('Products')),
                    S.listItem().title('Categories').child(S.documentTypeList('productCategory').title('Product Categories')),
                    S.listItem().title('Product News & Promotions').child(S.documentTypeList('productNews').title('Product News')),
                  ])
              ),
            S.divider(),

            // Partner Program
            S.listItem()
              .title('Partner Program')
              // .icon(UsersIcon) // Example icon
              .child(
                S.list()
                  .title('Partner Program')
                  .items([
                    S.listItem().title('Program Settings').child(S.documentTypeList('partnerProgram').title('Program Settings')),
                    S.listItem().title('Partner Information').child(S.documentTypeList('partner').title('Partner Information (Admin)')),
                    S.listItem().title('Admin Affiliate Links').child(S.documentTypeList('affiliateLink').title('Admin-Managed Affiliate Links')),
                    S.listItem().title('Affiliate Transactions Log').child(S.documentTypeList('affiliateTransaction').title('Affiliate Transactions Log')),
                  ])
              ),
            S.divider(),

            // Engagement Features
            S.listItem()
              .title('Engagement Features')
              // .icon(SparklesIcon) // Example icon
              .child(
                S.list()
                  .title('Engagement')
                  .items([
                    S.listItem().title('Quests & Tasks').child(S.documentTypeList('quest').title('Quests & Tasks')),
                    S.listItem().title('Competitions').child(S.documentTypeList('competition').title('Competitions')),
                    S.listItem().title('Raffles').child(S.documentTypeList('raffle').title('Raffles')),
                    S.listItem().title('Live Activity Definitions').child(S.documentTypeList('liveActivityDefinition').title('Live Activity Definitions')),
                  ])
              ),
            S.divider(),

            // Opportunities & Companies
            S.listItem()
              .title('Opportunities')
              // .icon(BriefcaseIcon) // Example icon
              .child(
                S.list()
                  .title('Opportunities')
                  .items([
                    S.listItem().title('All Opportunities').child(S.documentTypeList('opportunity').title('Opportunities')),
                    S.listItem().title('Companies / Organizations').child(S.documentTypeList('company').title('Companies / Organizations')),
                  ])
              ),
            S.divider(),

            // Content & Marketing
            S.listItem()
              .title('Content & Marketing')
              // .icon(MegaphoneIcon) // Example icon
              .child(
                S.list()
                  .title('Content & Marketing')
                  .items([
                    S.listItem().title('News & Updates').child(S.documentTypeList('newsAndUpdate').title('News & Updates')),
                    S.listItem().title('Testimonials').child(S.documentTypeList('testimonial').title('Testimonials')),
                    S.listItem().title('FAQ Items').child(S.documentTypeList('faqItem').title('FAQ Items')),
                    S.listItem().title('Platform Benefits').child(S.documentTypeList('platformBenefit').title('Platform Benefits')),
                  ])
              ),
            S.divider(),
            
            // Notifications
            S.listItem()
              .title('Notification System')
              // .icon(BellIcon) // Example icon
              .child(
                S.list()
                  .title('Notifications')
                  .items([
                    S.listItem().title('Notification Templates').child(S.documentTypeList('notification').title('Notification Templates/Global')),
                    S.listItem().title('Notification Preferences').child(S.documentTypeList('notificationPreference').title('Notification Preferences (Templates/Defaults)')),
                  ])
              ),
            
            // Vision Tool (if you want it at the top level)
            // S.listItem().title('Vision').icon(EyeIcon).child(S.component(VisionTool)),
          ])
    }),
    visionTool() // Keep visionTool if already present
  ],

  schema: {
    types: schemaTypes,
  },
})
