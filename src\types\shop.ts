
export type ProductCategory = {
  _id: string;
  name: string;
  slug: string;
  description: string;
  image: any;
};

export type ProductNews = {
  _id: string;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  discount?: number;
  image?: any;
  productId?: string;
};

export type Product = {
  _id: string;
  title: string;
  slug: string;
  description: string;
  price: number;
  discountedPrice?: number;
  savings?: string;
  image: any;
  categoryId: string;
  category: string;
  featured: boolean;
  hot?: boolean;
  badge?: string;
  inStock: boolean;
  dateAdded: string;
};

export type CartItem = {
  productId: string;
  title: string;
  price: number;
  quantity: number;
  image: string;
  savings?: string;
};

export type ProductFilter = {
  category: string;
  minPrice: number;
  maxPrice: number;
  sort: 'latest' | 'price-low' | 'price-high' | 'popular';
};
