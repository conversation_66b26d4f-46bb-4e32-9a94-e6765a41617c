
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Home } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-earnhub-lightGray p-6">
      <div className="text-center max-w-md animate-fade-in-up">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-earnhub-red/10 mb-6">
          <p className="text-2xl font-bold text-earnhub-red">404</p>
        </div>
        
        <h1 className="text-3xl md:text-4xl font-bold text-earnhub-dark mb-4">Page Not Found</h1>
        
        <p className="text-earnhub-darkGray mb-8">
          We couldn't find the page you were looking for. It might have been moved or doesn't exist.
        </p>
        
        <Button 
          className="bg-earnhub-red hover:bg-earnhub-red/90 text-white"
          onClick={() => window.location.href = "/"}
        >
          <Home className="mr-2 h-4 w-4" /> Return to Home
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
