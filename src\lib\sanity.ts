import { createClient } from '@sanity/client';
import imageUrlBuilder from '@sanity/image-url';

const projectId = import.meta.env.VITE_SANITY_PROJECT_ID || 'byjrfkgi';
const dataset = import.meta.env.VITE_SANITY_DATASET || 'production';

export const client = createClient({
  projectId,
  dataset,
  useCdn: true,
  apiVersion: '2023-05-03',
});

// Create an image URL builder using the Sanity client
const builder = imageUrlBuilder(client);

export const urlFor = (source: any) => {
  if (!source) {
    return '/placeholder.svg';
  }
  
  // If it's already a string URL, return it
  if (typeof source === 'string') {
    return source;
  }
  
  // If it has a url property, return that
  if (source.url) {
    return source.url;
  }
  
  // Otherwise use the image URL builder
  return builder.image(source).url();
};
