
import { useEffect, useRef } from 'react';
import { LiveUpdate } from '@/types/competitions';
import anime from 'animejs';
import { Clock } from 'lucide-react';

interface LiveUpdatesScrollerProps {
  updates: LiveUpdate[];
}

const LiveUpdatesScroller = ({ updates }: LiveUpdatesScrollerProps) => {
  const scrollerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!scrollerRef.current || updates.length === 0) return;
    
    const scrollWidth = scrollerRef.current.scrollWidth;
    const viewportWidth = scrollerRef.current.offsetWidth;
    
    // Only animate if content is wider than viewport
    if (scrollWidth > viewportWidth) {
      const scrollAnimation = anime({
        targets: scrollerRef.current,
        translateX: [0, -scrollWidth / 2],
        duration: 20000,
        easing: 'linear',
        loop: true
      });
      
      // Pause animation when hovering
      scrollerRef.current.addEventListener('mouseenter', () => {
        scrollAnimation.pause();
      });
      
      scrollerRef.current.addEventListener('mouseleave', () => {
        scrollAnimation.play();
      });
      
      return () => {
        scrollAnimation.pause();
        if (scrollerRef.current) {
          scrollerRef.current.removeEventListener('mouseenter', () => {});
          scrollerRef.current.removeEventListener('mouseleave', () => {});
        }
      };
    }
  }, [updates]);

  const getTypeIndicatorClass = (type: string) => {
    switch(type) {
      case 'withdrawal': return 'bg-green-500';
      case 'competition': return 'bg-blue-500';
      case 'achievement': return 'bg-yellow-500';
      case 'referral': return 'bg-earnhub-red';
      case 'task': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="relative h-12 overflow-hidden flex items-center px-4 border-t border-gray-100">
      {/* Gradient overlay on left */}
      <div className="absolute left-0 top-0 h-full w-8 bg-gradient-to-r from-white to-transparent z-10"></div>
      
      {/* Scrolling content */}
      <div 
        ref={scrollerRef} 
        className="flex items-center space-x-8 whitespace-nowrap"
      >
        {updates.map((update) => (
          <div key={update.id} className="flex items-center flex-shrink-0">
            <div className={`w-2 h-2 rounded-full mr-3 ${getTypeIndicatorClass(update.type)}`}></div>
            <div className="text-sm">
              <span className="font-medium">{update.user}</span> {update.action}
            </div>
            <span className="text-xs text-gray-400 flex items-center ml-2">
              <Clock className="w-3 h-3 mr-1" /> 
              {update.timestamp}
            </span>
          </div>
        ))}
        
        {/* Duplicate updates for continuous loop */}
        {updates.map((update) => (
          <div key={`duplicate-${update.id}`} className="flex items-center flex-shrink-0">
            <div className={`w-2 h-2 rounded-full mr-3 ${getTypeIndicatorClass(update.type)}`}></div>
            <div className="text-sm">
              <span className="font-medium">{update.user}</span> {update.action}
            </div>
            <span className="text-xs text-gray-400 flex items-center ml-2">
              <Clock className="w-3 h-3 mr-1" /> 
              {update.timestamp}
            </span>
          </div>
        ))}
      </div>
      
      {/* Gradient overlay on right */}
      <div className="absolute right-0 top-0 h-full w-8 bg-gradient-to-l from-white to-transparent z-10"></div>
    </div>
  );
};

export default LiveUpdatesScroller;
