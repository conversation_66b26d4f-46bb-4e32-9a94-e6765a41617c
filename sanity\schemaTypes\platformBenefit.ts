// sanity/schemaTypes/platformBenefit.ts
import {Rule} from 'sanity'

export default {
  name: 'platformBenefit',
  title: 'Platform Benefit/Feature',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Benefit/Feature Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'shortDescription',
      title: 'Short Description',
      type: 'text',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'icon',
      title: 'Icon Name (e.g., Lucide icon)',
      type: 'string',
    },
    {
      name: 'detailsPageUrl',
      title: 'Link to Details Page (Optional)',
      type: 'string',
    },
    {
      name: 'displayLocation',
      title: 'Display Location(s)',
      type: 'array',
      of: [{type: 'string'}],
      options: {
        list: [
          {title: 'Landing Page - Hero', value: 'landing_hero'},
          {title: 'Landing Page - Features Section', value: 'landing_features'},
          {title: 'About Us Page', value: 'about_us'},
        ],
      },
    },
  ],
}
