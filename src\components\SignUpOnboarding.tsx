
import { useState, useEffect } from "react";
import OnboardingModal from "./onboarding/OnboardingModal";

const SignUpOnboarding = () => {
  const [showOnboarding, setShowOnboarding] = useState(false);
  
  // Check if user is newly registered
  useEffect(() => {
    // This would typically check some state or localStorage
    // For demo purposes, we'll just show it after a delay
    const timer = setTimeout(() => {
      const isNewUser = localStorage.getItem("isNewUser") === "true";
      if (isNewUser) {
        setShowOnboarding(true);
        // Clear the flag after showing onboarding
        localStorage.removeItem("isNewUser");
      }
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <OnboardingModal 
      open={showOnboarding} 
      onOpenChange={setShowOnboarding}
    />
  );
};

export default SignUpOnboarding;
