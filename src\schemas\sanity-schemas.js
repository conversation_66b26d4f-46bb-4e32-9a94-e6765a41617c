
// This file contains the Sanity schemas for your shop

// Note: Copy this content to your Sanity project when setting it up

export default [
  {
    name: 'product',
    title: 'Product',
    type: 'document',
    fields: [
      {
        name: 'title',
        title: 'Title',
        type: 'string',
        validation: Rule => Rule.required()
      },
      {
        name: 'slug',
        title: 'Slug',
        type: 'slug',
        options: {
          source: 'title',
          maxLength: 96
        },
        validation: Rule => Rule.required()
      },
      {
        name: 'description',
        title: 'Description',
        type: 'text'
      },
      {
        name: 'details',
        title: 'Product Details',
        type: 'text'
      },
      {
        name: 'image',
        title: 'Product Image',
        type: 'image',
        options: {
          hotspot: true
        }
      },
      {
        name: 'price',
        title: 'Price (KES)',
        type: 'number',
        validation: Rule => Rule.required().positive()
      },
      {
        name: 'discountedPrice',
        title: 'Discounted Price (KES)',
        type: 'number',
        validation: Rule => Rule.positive()
      },
      {
        name: 'savings',
        title: 'Savings Text',
        type: 'string',
        description: 'e.g. "20% OFF" or "Save 1000 KES"'
      },
      {
        name: 'category',
        title: 'Category',
        type: 'reference',
        to: [{type: 'productCategory'}],
        validation: Rule => Rule.required()
      },
      {
        name: 'categoryId',
        title: 'Category ID',
        type: 'string',
        validation: Rule => Rule.required()
      },
      {
        name: 'featured',
        title: 'Featured Product',
        type: 'boolean',
        default: false
      },
      {
        name: 'hot',
        title: 'Hot Deal',
        type: 'boolean',
        default: false
      },
      {
        name: 'badge',
        title: 'Badge Text',
        type: 'string',
        description: 'e.g. "Limited Time" or "New"'
      },
      {
        name: 'inStock',
        title: 'In Stock',
        type: 'boolean',
        default: true
      },
      {
        name: 'dateAdded',
        title: 'Date Added',
        type: 'datetime',
        default: () => new Date().toISOString()
      },
      {
        name: 'popularity',
        title: 'Popularity Score',
        type: 'number',
        default: 0
      }
    ],
    preview: {
      select: {
        title: 'title',
        media: 'image',
        price: 'price'
      },
      prepare(selection) {
        const {title, media, price} = selection;
        return {
          title,
          subtitle: `KES ${price}`,
          media
        };
      }
    }
  },
  {
    name: 'productCategory',
    title: 'Product Category',
    type: 'document',
    fields: [
      {
        name: 'name',
        title: 'Name',
        type: 'string',
        validation: Rule => Rule.required()
      },
      {
        name: 'slug',
        title: 'Slug',
        type: 'slug',
        options: {
          source: 'name',
          maxLength: 96
        }
      },
      {
        name: 'description',
        title: 'Description',
        type: 'text'
      },
      {
        name: 'image',
        title: 'Category Image',
        type: 'image',
        options: {
          hotspot: true
        }
      }
    ]
  },
  {
    name: 'productNews',
    title: 'Product News & Announcements',
    type: 'document',
    fields: [
      {
        name: 'title',
        title: 'Title',
        type: 'string',
        validation: Rule => Rule.required()
      },
      {
        name: 'description',
        title: 'Description',
        type: 'text',
        validation: Rule => Rule.required()
      },
      {
        name: 'startDate',
        title: 'Start Date',
        type: 'datetime',
        validation: Rule => Rule.required()
      },
      {
        name: 'endDate',
        title: 'End Date',
        type: 'datetime',
        validation: Rule => Rule.required()
      },
      {
        name: 'discount',
        title: 'Discount Percentage',
        type: 'number'
      },
      {
        name: 'image',
        title: 'Image',
        type: 'image',
        options: {
          hotspot: true
        }
      },
      {
        name: 'productId',
        title: 'Related Product ID',
        type: 'string',
        description: 'Optional - link this news to a specific product'
      }
    ],
    preview: {
      select: {
        title: 'title',
        media: 'image',
        startDate: 'startDate',
        endDate: 'endDate'
      },
      prepare(selection) {
        const {title, media, startDate, endDate} = selection;
        const start = startDate ? new Date(startDate).toLocaleDateString() : '';
        const end = endDate ? new Date(endDate).toLocaleDateString() : '';
        
        return {
          title,
          subtitle: `${start} - ${end}`,
          media
        };
      }
    }
  }
];
