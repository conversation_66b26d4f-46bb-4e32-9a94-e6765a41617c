
export default {
  name: 'product',
  title: 'Product',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96
      },
      validation: Rule => Rule.required()
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text'
    },
    {
      name: 'image',
      title: 'Image',
      type: 'image',
      options: {
        hotspot: true
      }
    },
    {
      name: 'price',
      title: 'Price',
      type: 'number',
      validation: Rule => Rule.required().positive()
    },
    {
      name: 'discountedPrice',
      title: 'Discounted Price',
      type: 'number'
    },
    {
      name: 'savings',
      title: 'Savings',
      type: 'string'
    },
    {
      name: 'category',
      title: 'Category',
      type: 'reference',
      to: [{type: 'productCategory'}]
    },
    {
      name: 'categoryId',
      title: 'Category ID',
      type: 'string'
    },
    {
      name: 'featured',
      title: 'Featured',
      type: 'boolean',
      initialValue: false
    },
    {
      name: 'hot',
      title: 'Hot Deal',
      type: 'boolean',
      initialValue: false
    },
    {
      name: 'badge',
      title: 'Badge Text',
      type: 'string'
    },
    {
      name: 'inStock',
      title: 'In Stock',
      type: 'boolean',
      initialValue: true
    },
    {
      name: 'dateAdded',
      title: 'Date Added',
      type: 'datetime',
      initialValue: () => new Date().toISOString()
    },
    {
      name: 'details',
      title: 'Product Details',
      type: 'text'
    },
    // Partner Program Fields
    {
      name: 'partnerEligible',
      title: 'Partner Program Eligible',
      type: 'boolean',
      description: 'Is this product eligible for the partner program?',
      initialValue: false
    },
    {
      name: 'commissionRate',
      title: 'Base Commission Rate (%)',
      type: 'number',
      description: 'Base percentage commission for partners',
      validation: Rule => Rule.min(0).max(100),
      hidden: ({document}) => !document?.partnerEligible
    },
    {
      name: 'commissionTiers',
      title: 'Commission Tiers',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'tier',
              title: 'Membership Tier',
              type: 'string',
              options: {
                list: [
                  { title: 'Basic', value: 'basic' },
                  { title: 'Silver', value: 'silver' },
                  { title: 'Gold', value: 'gold' },
                  { title: 'Platinum', value: 'platinum' }
                ]
              }
            },
            {
              name: 'rate',
              title: 'Commission Rate (%)',
              type: 'number',
              validation: Rule => Rule.min(0).max(100)
            }
          ],
          preview: {
            select: {
              title: 'tier',
              subtitle: 'rate'
            },
            prepare({title, subtitle}) {
              return {
                title: `${title} Tier`,
                subtitle: `${subtitle}% commission`
              }
            }
          }
        }
      ],
      hidden: ({document}) => !document?.partnerEligible
    }
  ],
  preview: {
    select: {
      title: 'title',
      media: 'image'
    }
  }
}
