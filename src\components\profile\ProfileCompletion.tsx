
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Check, AlertCircle, BookOpen, Briefcase, Clock } from "lucide-react";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";

// Counties in Kenya
const kenyanCounties = [
  "Nairobi", "Mombasa", "Ki<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Eldoret", 
  "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>", "Kakamega", "Malindi", "Lamu", "<PERSON><PERSON><PERSON>"
];

// Education levels
const educationLevels = [
  "Primary School", "High School", "Certificate", "Diploma", 
  "Bachelor's Degree", "Master's Degree", "Doctorate", "Other"
];

// Income levels
const incomeLevels = [
  "Below KES 20,000", "KES 20,000 - 50,000", "KES 50,000 - 100,000", "Above KES 100,000"
];

// Available interests
const interestOptions = [
  "QA Testing", "Content Creation", "Data Entry", "Trading", 
  "Marketing", "Design", "Writing", "Translation", "Virtual Assistant"
];

// Skill options
const skillOptions = [
  "Video Editing", "Graphic Design", "Content Writing", "Data Analysis", 
  "Social Media Management", "Programming", "Customer Service", "Translation"
];

// Device options
const deviceOptions = [
  "Android Phone", "iPhone", "Windows PC", "Mac", "Tablet"
];

// Preferred earning methods
const earningMethods = [
  "Tasks", "Competitions", "Referrals", "Content Creation", "QA Testing"
];

interface ProfileFormValues {
  fullName: string;
  username: string;
  gender: string;
  age: string;
  county: string;
  incomeLevel: string;
  interests: string[];
  skills: string[];
  devices: string[];
  educationLevel: string;
  socialMedia: string;
  preferredEarningMethods: string[];
  availability: string;
  consentDataUsage: boolean;
}

const ProfileCompletion = () => {
  const [progress, setProgress] = useState(30); // Initial progress for demo
  const { toast } = useToast();
  const [selectedInterests, setSelectedInterests] = useState<string[]>([]);
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [selectedDevices, setSelectedDevices] = useState<string[]>([]);
  const [selectedEarningMethods, setSelectedEarningMethods] = useState<string[]>([]);
  
  const form = useForm<ProfileFormValues>({
    defaultValues: {
      fullName: "John Doe",
      username: "johndoe",
      gender: "",
      age: "",
      county: "",
      incomeLevel: "",
      interests: [],
      skills: [],
      devices: [],
      educationLevel: "",
      socialMedia: "",
      preferredEarningMethods: [],
      availability: "",
      consentDataUsage: false,
    },
  });
  
  const onSubmit = (data: ProfileFormValues) => {
    console.log("Profile data:", data);
    
    // Calculate new progress value
    const newProgress = Math.min(100, progress + 70);
    setProgress(newProgress);
    
    toast({
      title: "Profile updated!",
      description: newProgress === 100 
        ? "Congratulations! You've unlocked all earning opportunities." 
        : "Your profile has been updated successfully.",
    });
  };
  
  const toggleInterest = (interest: string) => {
    setSelectedInterests(prev => 
      prev.includes(interest) 
        ? prev.filter(i => i !== interest) 
        : [...prev, interest]
    );
  };
  
  const toggleSkill = (skill: string) => {
    setSelectedSkills(prev => 
      prev.includes(skill) 
        ? prev.filter(s => s !== skill) 
        : [...prev, skill]
    );
  };
  
  const toggleDevice = (device: string) => {
    setSelectedDevices(prev => 
      prev.includes(device) 
        ? prev.filter(d => d !== device) 
        : [...prev, device]
    );
  };
  
  const toggleEarningMethod = (method: string) => {
    setSelectedEarningMethods(prev => 
      prev.includes(method) 
        ? prev.filter(m => m !== method) 
        : [...prev, method]
    );
  };
  
  return (
    <Card className="w-full mb-8 animate-fade-in">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span>Complete Your Profile</span>
          {progress === 100 && <Check className="text-green-500" size={20} />}
        </CardTitle>
        <CardDescription>
          Enhance your profile to unlock exclusive opportunities and earn a KES 500 bonus!
        </CardDescription>
        <div className="mt-2">
          <div className="flex justify-between text-sm mb-1">
            <span>Profile completion</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-4 md:col-span-2">
                <h3 className="text-lg font-medium">Basic Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="fullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Your full name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Username</FormLabel>
                        <FormControl>
                          <Input placeholder="Choose a username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              
              {/* Demographics */}
              <div className="space-y-4 md:col-span-2">
                <h3 className="text-lg font-medium">Demographics</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="gender"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Gender</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select gender" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="male">Male</SelectItem>
                            <SelectItem value="female">Female</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                            <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="age"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Age</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select age range" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="18-24">18-24</SelectItem>
                            <SelectItem value="25-34">25-34</SelectItem>
                            <SelectItem value="35-44">35-44</SelectItem>
                            <SelectItem value="45-54">45-54</SelectItem>
                            <SelectItem value="55+">55+</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="county"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>County</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select county" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {kenyanCounties.map(county => (
                              <SelectItem key={county} value={county.toLowerCase()}>
                                {county}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              
              {/* Additional Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Additional Information</h3>
                
                <FormField
                  control={form.control}
                  name="incomeLevel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Income Level</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select income level" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {incomeLevels.map(level => (
                            <SelectItem key={level} value={level.toLowerCase().replace(/\s+/g, '-')}>
                              {level}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="educationLevel"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-1">
                          <BookOpen size={16} />
                          <span>Education Level</span>
                        </div>
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select education level" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {educationLevels.map(level => (
                            <SelectItem key={level} value={level.toLowerCase().replace(/\s+/g, '-')}>
                              {level}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="socialMedia"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Social Media Handles</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter your social media handles (Instagram, TikTok, Facebook, etc.)"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Add your social media profiles for influencer opportunities
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              {/* Skills & Interests */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Skills & Interests</h3>
                
                <FormField
                  control={form.control}
                  name="interests"
                  render={() => (
                    <FormItem>
                      <FormLabel>Interests</FormLabel>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {interestOptions.map(interest => (
                          <Button
                            key={interest}
                            type="button"
                            variant={selectedInterests.includes(interest) ? "default" : "outline"}
                            size="sm"
                            onClick={() => toggleInterest(interest)}
                            className={selectedInterests.includes(interest) ? "bg-earnhub-red text-white" : ""}
                          >
                            {interest}
                          </Button>
                        ))}
                      </div>
                      <FormDescription>
                        Select the areas you're interested in
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="skills"
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-1">
                          <Briefcase size={16} />
                          <span>Skills</span>
                        </div>
                      </FormLabel>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {skillOptions.map(skill => (
                          <Button
                            key={skill}
                            type="button"
                            variant={selectedSkills.includes(skill) ? "default" : "outline"}
                            size="sm"
                            onClick={() => toggleSkill(skill)}
                            className={selectedSkills.includes(skill) ? "bg-earnhub-red text-white" : ""}
                          >
                            {skill}
                          </Button>
                        ))}
                      </div>
                      <FormDescription>
                        Select your skills and expertise
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="devices"
                  render={() => (
                    <FormItem>
                      <FormLabel>Devices Owned</FormLabel>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {deviceOptions.map(device => (
                          <Button
                            key={device}
                            type="button"
                            variant={selectedDevices.includes(device) ? "default" : "outline"}
                            size="sm"
                            onClick={() => toggleDevice(device)}
                            className={selectedDevices.includes(device) ? "bg-earnhub-red text-white" : ""}
                          >
                            {device}
                          </Button>
                        ))}
                      </div>
                      <FormDescription>
                        Select the devices you own (for QA testing eligibility)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="preferredEarningMethods"
                  render={() => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-1">
                          <span>Preferred Earning Methods</span>
                        </div>
                      </FormLabel>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {earningMethods.map(method => (
                          <Button
                            key={method}
                            type="button"
                            variant={selectedEarningMethods.includes(method) ? "default" : "outline"}
                            size="sm"
                            onClick={() => toggleEarningMethod(method)}
                            className={selectedEarningMethods.includes(method) ? "bg-earnhub-red text-white" : ""}
                          >
                            {method}
                          </Button>
                        ))}
                      </div>
                      <FormDescription>
                        Select how you prefer to earn
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="availability"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <div className="flex items-center gap-1">
                          <Clock size={16} />
                          <span>Availability</span>
                        </div>
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select availability" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="1-5">1-5 hours/week</SelectItem>
                          <SelectItem value="6-10">6-10 hours/week</SelectItem>
                          <SelectItem value="11-20">11-20 hours/week</SelectItem>
                          <SelectItem value="21-40">21-40 hours/week</SelectItem>
                          <SelectItem value="40+">40+ hours/week</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            
            {/* Consent checkbox */}
            <FormField
              control={form.control}
              name="consentDataUsage"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Data Usage Consent</FormLabel>
                    <FormDescription>
                      I agree that EarnHub may use my profile information to match me with relevant tasks and opportunities.
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
            
            <div className="flex justify-end">
              <Button type="submit" className="w-full md:w-auto">
                {progress === 100 ? "Update Profile" : "Complete Profile"}
              </Button>
            </div>
            
            {progress < 100 && (
              <div className="flex items-start space-x-3 p-4 rounded-md bg-amber-50 text-amber-800 border border-amber-200">
                <AlertCircle className="h-5 w-5 mt-0.5" />
                <div>
                  <p className="font-medium">Complete your profile</p>
                  <p className="text-sm">
                    You'll earn a KES 500 bonus and unlock exclusive tasks when your profile is 100% complete.
                  </p>
                </div>
              </div>
            )}
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default ProfileCompletion;
