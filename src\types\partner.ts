
export type PartnerTier = {
  name: string;
  level: 'basic' | 'silver' | 'gold' | 'platinum';
  baseCommissionBonus: number;
  requirements: string[];
  benefits: string[];
  minimumSales: number;
};

export type PartnerProgram = {
  _id: string;
  name: string;
  description: string;
  tiers: PartnerTier[];
  terms: string;
  active: boolean;
};

export type Partner = {
  _id: string;
  userId: string;
  name: string;
  email: string;
  dateJoined: string;
  tier: 'basic' | 'silver' | 'gold' | 'platinum';
  affiliateCode: string;
  totalEarnings: number;
  unpaidEarnings: number;
  totalSales: number;
  status: 'active' | 'inactive' | 'suspended';
};

export type AffiliateLink = {
  _id: string;
  code: string;
  partnerId: string;
  product?: any;
  isGeneralLink: boolean;
  dateCreated: string;
  clicks: number;
  conversions: number;
  revenue: number;
  commission: number;
  customName?: string;
};

export type AffiliateTransaction = {
  _id: string;
  orderId: string;
  affiliateCode: string;
  partnerId: string;
  productId?: string;
  productTitle?: string;
  saleAmount: number;
  commissionRate: number;
  commissionAmount: number;
  status: 'pending' | 'approved' | 'paid' | 'rejected';
  transactionDate: string;
  payoutDate?: string;
};
