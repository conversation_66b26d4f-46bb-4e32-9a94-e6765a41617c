// supabase/functions/lipia-callback-handler/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from './cors.ts'; // Assuming cors.ts exists as in previous function

interface LipiaCallbackData {
  message: string;
  data?: { // Data field might be absent or different in failure scenarios
    amount?: string;
    phone?: string;
    reference?: string;
    CheckoutRequestID?: string;
    // Lipia might send other fields in case of errors
    [key: string]: any; 
  };
  // Lipia might send other top-level fields in case of errors
  [key: string]: any;
}

// TODO: Implement proper callback verification (e.g., signature check, IP whitelist)
// For now, we proceed without it, noting the security risk.
// const LIPIA_CALLBACK_SECRET = Deno.env.get('LIPIA_CALLBACK_SECRET'); 
// function verifyCallback(req: Request, rawBody: string): boolean {
//   const signature = req.headers.get('X-Lipia-Signature');
//   if (!signature || !LIPIA_CALLBACK_SECRET) return false;
//   // ... implementation of signature verification ...
//   return true;
// }

async function updateProfileMembership(
  supabaseAdmin: SupabaseClient,
  userId: string,
  membershipTierSanityId: string,
  membershipPurchaseDate: Date
) {
  // TODO: Fetch membership duration from Sanity based on membershipTierSanityId
  // For now, using a fixed 30-day duration.
  const expiryDate = new Date(membershipPurchaseDate);
  expiryDate.setDate(expiryDate.getDate() + 30);

  const { error: profileUpdateError } = await supabaseAdmin
    .from('profiles')
    .update({
      membership_tier_id: membershipTierSanityId,
      membership_expiry_date: expiryDate.toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId);

  if (profileUpdateError) {
    console.error(`Error updating profile for user ${userId}:`, profileUpdateError);
    // Depending on policy, this might be a critical error or one that can be retried/logged for manual intervention
    throw new Error(`Failed to update profile membership: ${profileUpdateError.message}`);
  }
  console.log(`Profile for user ${userId} updated to tier ${membershipTierSanityId}, expiry ${expiryDate.toISOString()}`);
}


serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method Not Allowed' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    });
  }

  let callbackData: LipiaCallbackData;
  try {
    const rawBody = await req.text();
    // TODO: Add callback verification here if/when Lipia supports it
    // if (!verifyCallback(req, rawBody)) {
    //   console.warn('Lipia callback verification failed.');
    //   return new Response(JSON.stringify({ error: 'Callback verification failed' }), {
    //     headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    //     status: 403, // Forbidden
    //   });
    // }
    callbackData = JSON.parse(rawBody);
    console.log('Received Lipia callback data:', JSON.stringify(callbackData, null, 2));
  } catch (e) {
    console.error('Error parsing Lipia callback JSON:', e);
    return new Response(JSON.stringify({ error: 'Invalid JSON payload' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    });
  }

  // Use Admin client for sensitive operations triggered by webhook
  const supabaseAdmin = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '' // Use service role key for admin operations
  );

  const checkoutRequestId = callbackData.data?.CheckoutRequestID;

  if (!checkoutRequestId) {
    console.warn('CheckoutRequestID missing from Lipia callback:', callbackData);
    // Consider if this is a failure scenario that needs DB update for a known transaction
    return new Response(JSON.stringify({ error: 'CheckoutRequestID missing' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    });
  }

  try {
    // 1. Fetch the transaction using CheckoutRequestID
    const { data: transaction, error: fetchError } = await supabaseAdmin
      .from('transactions')
      .select(`
        id,
        status,
        user_id,
        reference_entity_id,
        reference_entity_type,
        membership_purchases (id, sanity_membership_tier_id, status)
      `)
      .eq('payment_processor_checkout_id', checkoutRequestId)
      .single();

    if (fetchError || !transaction) {
      console.error(`Transaction not found for CheckoutRequestID ${checkoutRequestId}:`, fetchError);
      // This could mean Lipia sent a callback for a transaction we don't know, or it's delayed
      return new Response(JSON.stringify({ error: 'Transaction not found or could not be fetched' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404, // Not Found, or 400 if this is considered a client error from Lipia's side
      });
    }

    // 2. Idempotency Check: If transaction is already completed or failed definitively, do nothing further.
    if (transaction.status === 'completed' || transaction.status === 'failed') {
      console.log(`Transaction ${transaction.id} already processed with status: ${transaction.status}. Ignoring callback.`);
      return new Response(JSON.stringify({ status: 'received', message: 'Already processed' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      });
    }

    // 3. Process based on Lipia's message
    // Lipia's success message is "callback received successfully"
    // Other messages imply failure or cancellation. E.g., "Request cancelled by user", "insufficient user balance"
    const isSuccess = callbackData.message === "callback received successfully" && callbackData.data?.reference;

    const newTransactionStatus = isSuccess ? 'completed' : 'failed'; // Default to 'failed' for non-success messages
    const newMembershipPurchaseStatus = isSuccess ? 'completed' : 'failed';
    const purchaseDate = isSuccess ? new Date().toISOString() : null;

    // Update Transaction
    const { error: transactionUpdateError } = await supabaseAdmin
      .from('transactions')
      .update({
        status: newTransactionStatus,
        payment_processor_reference: callbackData.data?.reference || transaction.payment_processor_reference, // Update if new one provided
        notes: `Lipia callback received: ${callbackData.message}`,
        updated_at: new Date().toISOString(),
      })
      .eq('id', transaction.id);

    if (transactionUpdateError) {
      console.error(`Error updating transaction ${transaction.id}:`, transactionUpdateError);
      // This is a critical error, might need a retry mechanism or manual intervention
      return new Response(JSON.stringify({ error: 'Failed to update transaction' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }
    console.log(`Transaction ${transaction.id} updated to status: ${newTransactionStatus}`);

    // Update Membership Purchase if linked
    if (transaction.reference_entity_id && transaction.reference_entity_type === 'membership_purchase') {
      // Ensure the nested membership_purchase from the query is used, or fetch if not available
      const membershipPurchase = Array.isArray(transaction.membership_purchases) 
                                 ? transaction.membership_purchases[0] 
                                 : transaction.membership_purchases;

      if (!membershipPurchase) {
          console.error(`Membership purchase not found for transaction ${transaction.id} despite reference.`);
          // This indicates a data integrity issue or an issue with the initial query structure.
          // Proceed with caution or handle as an error.
           return new Response(JSON.stringify({ error: 'Associated membership purchase data not found' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          });
      }
      
      const updatePayload: { status: string; purchase_date?: string | null, updated_at: string } = {
        status: newMembershipPurchaseStatus,
        updated_at: new Date().toISOString(),
      };
      if (isSuccess && purchaseDate) {
        updatePayload.purchase_date = purchaseDate;
      }

      const { error: membershipUpdateError } = await supabaseAdmin
        .from('membership_purchases')
        .update(updatePayload)
        .eq('id', transaction.reference_entity_id);

      if (membershipUpdateError) {
        console.error(`Error updating membership purchase ${transaction.reference_entity_id}:`, membershipUpdateError);
        // Critical error
        return new Response(JSON.stringify({ error: 'Failed to update membership purchase' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        });
      }
      console.log(`Membership purchase ${transaction.reference_entity_id} updated to status: ${newMembershipPurchaseStatus}`);

      // Update Profile if payment was successful
      if (isSuccess && transaction.user_id && membershipPurchase.sanity_membership_tier_id) {
        await updateProfileMembership(
          supabaseAdmin,
          transaction.user_id,
          membershipPurchase.sanity_membership_tier_id,
          new Date(purchaseDate!) // purchaseDate is set if isSuccess
        );
      }
    } else {
      console.log(`Transaction ${transaction.id} has no linked membership purchase or type is different. Skipping membership update.`);
    }

    // 4. Respond to Lipia
    return new Response(JSON.stringify({ status: 'received' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Unhandled error in Lipia callback handler:', error);
    return new Response(JSON.stringify({ error: error.message || 'An unexpected error occurred' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});
