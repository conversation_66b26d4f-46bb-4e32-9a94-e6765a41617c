
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: PriorityLevel;
  isRead: boolean;
  createdAt: string;
  actionUrl?: string;
  icon?: string;
  expiresAt?: string;
}

export type NotificationType = 
  | 'system'
  | 'account'
  | 'competition'
  | 'task'
  | 'promotion'
  | 'partner'
  | 'recommendation';

export type PriorityLevel = 'low' | 'medium' | 'high';

export interface NotificationPreference {
  userId: string;
  emailNotifications: boolean;
  pushNotifications: boolean;
  typePreferences: {
    type: NotificationType;
    enabled: boolean;
  }[];
  priorityThreshold: 'all' | 'medium' | 'high';
}

export interface NotificationFilterOptions {
  types: NotificationType[];
  priority: PriorityLevel | 'all';
  readStatus: 'all' | 'read' | 'unread';
}
