
import { client } from '@/lib/sanity';
import { Partner<PERSON><PERSON>ram, Partner, AffiliateLink, AffiliateTransaction } from '@/types/partner';

// Fetch partner program details
export const fetchPartnerProgram = async (): Promise<PartnerProgram> => {
  const query = `*[_type == "partnerProgram" && active == true][0]{
    _id,
    name,
    description,
    tiers[]{
      name,
      level,
      baseCommissionBonus,
      requirements,
      benefits,
      minimumSales
    },
    terms,
    active
  }`;
  
  return await client.fetch(query);
};

// Check if user is a partner
export const fetchPartnerByUserId = async (userId: string): Promise<Partner | null> => {
  const query = `*[_type == "partner" && userId == $userId][0]{
    _id,
    userId,
    name,
    email,
    dateJoined,
    tier,
    affiliateCode,
    totalEarnings,
    unpaidEarnings,
    totalSales,
    status
  }`;
  
  return await client.fetch(query, { userId });
};

// Fetch products eligible for partner program
export const fetchPartnerEligibleProducts = async () => {
  const query = `*[_type == "product" && partnerEligible == true] {
    _id,
    title,
    slug,
    description,
    price,
    discountedPrice,
    "image": image.asset->url,
    commissionRate,
    commissionTiers
  }`;
  
  return await client.fetch(query);
};

// Fetch affiliate links for a partner
export const fetchPartnerAffiliateLinks = async (partnerId: string): Promise<AffiliateLink[]> => {
  const query = `*[_type == "affiliateLink" && partnerId == $partnerId] {
    _id,
    code,
    partnerId,
    "product": product->{
      _id, 
      title, 
      "image": image.asset->url,
      price,
      discountedPrice
    },
    isGeneralLink,
    dateCreated,
    clicks,
    conversions,
    revenue,
    commission,
    customName
  } | order(dateCreated desc)`;
  
  return await client.fetch(query, { partnerId });
};

// Fetch affiliate transactions for a partner
export const fetchPartnerTransactions = async (partnerId: string): Promise<AffiliateTransaction[]> => {
  const query = `*[_type == "affiliateTransaction" && partnerId == $partnerId] {
    _id,
    orderId,
    affiliateCode,
    partnerId,
    productId,
    productTitle,
    saleAmount,
    commissionRate,
    commissionAmount,
    status,
    transactionDate,
    payoutDate
  } | order(transactionDate desc)`;
  
  return await client.fetch(query, { partnerId });
};

// Generate a new affiliate link
export const generateAffiliateLink = async (
  partnerId: string, 
  affiliateCode: string, 
  productId?: string,
  customName?: string
): Promise<string> => {
  // In a real app, you would call an API to create this in the database
  // For now, we'll just return a formatted URL
  if (productId) {
    const baseUrl = window.location.origin;
    return `${baseUrl}/p/${productId}?ref=${affiliateCode}`;
  } else {
    const baseUrl = window.location.origin;
    return `${baseUrl}/shop?ref=${affiliateCode}`;
  }
};

// Register new partner
export const registerPartner = async (partnerData: Partial<Partner>): Promise<Partner> => {
  // In a real implementation, this would call an API endpoint
  // For our demo, we'll mock this
  
  // Generate a random affiliate code if not provided
  if (!partnerData.affiliateCode) {
    partnerData.affiliateCode = generateAffiliateCode(partnerData.name || '');
  }
  
  // Mock API call response
  return {
    _id: 'new-partner-id',
    userId: partnerData.userId || '',
    name: partnerData.name || '',
    email: partnerData.email || '',
    dateJoined: new Date().toISOString(),
    tier: 'basic',
    affiliateCode: partnerData.affiliateCode,
    totalEarnings: 0,
    unpaidEarnings: 0,
    totalSales: 0,
    status: 'active'
  };
};

// Helper function to generate an affiliate code
export const generateAffiliateCode = (name: string): string => {
  const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '');
  const prefix = cleanName.substring(0, 5);
  const randomPart = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${prefix}${randomPart}`;
};
