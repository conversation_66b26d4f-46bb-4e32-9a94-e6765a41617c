
import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery } from '@tanstack/react-query';
import { client } from '@/lib/sanity';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Minus, Plus, Share2, ShoppingCart } from "lucide-react";
import { useCart } from '@/contexts/CartContext';
import { Product } from '@/types/shop';
import { CartDrawer } from '@/components/shop/CartDrawer';

const ProductDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const [quantity, setQuantity] = useState(1);
  
  const { data: product, isLoading, error } = useQuery({
    queryKey: ['product', id],
    queryFn: async () => {
      const query = `*[_type == "product" && _id == $id][0] {
        _id,
        title,
        slug,
        description,
        price,
        discountedPrice,
        savings,
        "image": image.asset->url,
        categoryId,
        "category": category->name,
        featured,
        hot,
        badge,
        inStock,
        dateAdded,
        details
      }`;
      
      return await client.fetch(query, { id });
    },
    enabled: !!id
  });
  
  // Related products query
  const { data: relatedProducts } = useQuery({
    queryKey: ['relatedProducts', product?.categoryId],
    queryFn: async () => {
      const query = `*[_type == "product" && categoryId == $categoryId && _id != $id][0...4] {
        _id,
        title,
        slug,
        description,
        price,
        discountedPrice,
        savings,
        "image": image.asset->url,
        categoryId,
        "category": category->name,
        featured,
        hot,
        badge
      }`;
      
      return await client.fetch(query, { 
        categoryId: product?.categoryId,
        id: product?._id 
      });
    },
    enabled: !!product?.categoryId
  });
  
  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity < 1) return;
    setQuantity(newQuantity);
  };
  
  const handleAddToCart = () => {
    if (!product) return;
    
    addToCart({
      productId: product._id,
      title: product.title,
      price: product.discountedPrice || product.price,
      quantity: quantity,
      image: product.image,
      savings: product.savings
    });
  };
  
  const formatPrice = (price: number) => {
    return `KES ${price.toLocaleString()}`;
  };
  
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 pt-24 md:pt-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 w-48 bg-gray-200 rounded"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="h-80 bg-gray-200 rounded"></div>
            <div className="space-y-4">
              <div className="h-8 w-full bg-gray-200 rounded"></div>
              <div className="h-6 w-32 bg-gray-200 rounded"></div>
              <div className="h-24 w-full bg-gray-200 rounded"></div>
              <div className="h-12 w-full bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8 pt-24 md:pt-8">
        <Card className="p-8 text-center">
          <p className="text-earnhub-darkGray mb-4">Product not found or an error occurred.</p>
          <Button onClick={() => navigate('/shop')}>Return to Shop</Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-8">
      <div className="flex justify-between items-center mb-6">
        <Button variant="ghost" onClick={() => navigate(-1)} className="flex items-center gap-2">
          <ArrowLeft size={16} />
          Back
        </Button>
        <CartDrawer />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        {/* Product Image */}
        <div className="relative">
          <div className="bg-gray-100 rounded-lg overflow-hidden aspect-square">
            <img 
              src={product.image || "/placeholder.svg"} 
              alt={product.title} 
              className="w-full h-full object-contain"
            />
          </div>
          {product.hot && (
            <Badge className="absolute top-4 right-4 bg-orange-500">Hot Deal</Badge>
          )}
          {product.badge && (
            <Badge className="absolute top-4 right-4 bg-earnhub-red">{product.badge}</Badge>
          )}
        </div>
        
        {/* Product Details */}
        <div className="space-y-6">
          <div>
            <Badge variant="outline" className="mb-2">{product.category}</Badge>
            <h1 className="text-2xl md:text-4xl font-bold">{product.title}</h1>
            <div className="mt-2">
              {product.discountedPrice ? (
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-earnhub-red">
                    {formatPrice(product.discountedPrice)}
                  </span>
                  <span className="text-lg text-gray-400 line-through">
                    {formatPrice(product.price)}
                  </span>
                  {product.savings && (
                    <Badge className="bg-green-500 ml-2">Save {product.savings}</Badge>
                  )}
                </div>
              ) : (
                <span className="text-2xl font-bold text-earnhub-red">
                  {formatPrice(product.price)}
                </span>
              )}
            </div>
          </div>
          
          <p className="text-gray-700">{product.description}</p>
          
          <div className="p-4 bg-gray-50 rounded-md">
            <span className={product.inStock ? 'text-green-600' : 'text-red-500'}>
              {product.inStock ? 'In Stock' : 'Out of Stock'}
            </span>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="flex items-center border rounded-md">
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-10 w-10 rounded-none"
                onClick={() => handleQuantityChange(quantity - 1)}
                disabled={quantity <= 1}
              >
                <Minus size={16} />
              </Button>
              <div className="w-12 text-center">{quantity}</div>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-10 w-10 rounded-none"
                onClick={() => handleQuantityChange(quantity + 1)}
              >
                <Plus size={16} />
              </Button>
            </div>
            
            <Button 
              className="flex-1"
              size="lg"
              onClick={handleAddToCart}
              disabled={!product.inStock}
            >
              <ShoppingCart className="mr-2" size={16} />
              Add to Cart
            </Button>
          </div>
          
          <Button variant="outline" className="w-full">
            <Share2 className="mr-2" size={16} />
            Share Product
          </Button>
          
          {product.details && (
            <div className="mt-8">
              <h3 className="text-lg font-semibold mb-2">Product Details</h3>
              <div className="prose max-w-none">
                <p>{product.details}</p>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Related Products */}
      {relatedProducts?.length > 0 && (
        <div className="mt-16">
          <Separator className="mb-8" />
          <h2 className="text-2xl font-bold mb-6">Related Products</h2>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            {relatedProducts.map((relatedProduct: Product) => (
              <Card key={relatedProduct._id} className="overflow-hidden h-full hover:shadow-md transition-all">
                <div 
                  className="h-40 bg-gray-100 cursor-pointer" 
                  onClick={() => navigate(`/shop/product/${relatedProduct._id}`)}
                >
                  <img 
                    src={relatedProduct.image || "/placeholder.svg"} 
                    alt={relatedProduct.title} 
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="p-4">
                  <h3 
                    className="font-medium hover:text-earnhub-red cursor-pointer" 
                    onClick={() => navigate(`/shop/product/${relatedProduct._id}`)}
                  >
                    {relatedProduct.title}
                  </h3>
                  <div className="flex justify-between items-center mt-2">
                    <div>
                      {relatedProduct.discountedPrice ? (
                        <div className="flex items-center gap-1">
                          <span className="font-bold text-earnhub-red">
                            {formatPrice(relatedProduct.discountedPrice)}
                          </span>
                          <span className="text-sm text-gray-400 line-through">
                            {formatPrice(relatedProduct.price)}
                          </span>
                        </div>
                      ) : (
                        <span className="font-bold text-earnhub-red">
                          {formatPrice(relatedProduct.price)}
                        </span>
                      )}
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => {
                        addToCart({
                          productId: relatedProduct._id,
                          title: relatedProduct.title,
                          price: relatedProduct.discountedPrice || relatedProduct.price,
                          quantity: 1,
                          image: relatedProduct.image,
                          savings: relatedProduct.savings
                        });
                      }}
                    >
                      <ShoppingCart size={14} />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductDetailPage;
