
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useCart } from "@/contexts/CartContext";
import { toast } from "@/hooks/use-toast";
import { CreditCard, Wallet } from "lucide-react";

const CheckoutPage = () => {
  const { items, totalPrice, clearCart } = useCart();
  const [paymentMethod, setPaymentMethod] = useState<string>("wallet");
  const navigate = useNavigate();
  
  // Mock user wallet balance - in a real app, this would come from the backend
  const walletBalance = 5000;
  
  const handleCheckout = () => {
    // This would typically process the payment via an API
    toast({
      title: "Order placed successfully!",
      description: "Thank you for your purchase"
    });
    
    clearCart();
    navigate("/dashboard");
  };
  
  const formatPrice = (price: number) => {
    return `KES ${price.toLocaleString()}`;
  };
  
  if (items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8 pt-24 md:pt-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Your cart is empty</CardTitle>
            <CardDescription>Add some items to your cart before checkout</CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => navigate("/shop")} className="w-full">
              Return to Shop
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 pt-24 md:pt-8">
      <h1 className="text-2xl md:text-3xl font-bold mb-8">Checkout</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
              <CardDescription>Review your items before completing your purchase</CardDescription>
            </CardHeader>
            <CardContent>
              {items.map((item) => (
                <div key={item.productId} className="flex gap-4 py-4">
                  <div className="w-16 h-16 bg-gray-100 rounded">
                    <img
                      src={item.image || "/placeholder.svg"}
                      alt={item.title}
                      className="w-full h-full object-cover rounded"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between">
                      <h3 className="font-medium">{item.title}</h3>
                      <span>{formatPrice(item.price * item.quantity)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>{item.quantity} x {formatPrice(item.price)}</span>
                      {item.savings && (
                        <span className="text-green-600">Save {item.savings}</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
          
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Payment Method</CardTitle>
              <CardDescription>Select how you want to pay</CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="wallet" id="wallet" />
                  <Label htmlFor="wallet" className="flex items-center gap-2 cursor-pointer">
                    <Wallet size={16} />
                    EarnHub Wallet (Balance: {formatPrice(walletBalance)})
                  </Label>
                </div>
                <div className="flex items-center space-x-2 mt-4">
                  <RadioGroupItem value="card" id="card" />
                  <Label htmlFor="card" className="flex items-center gap-2 cursor-pointer">
                    <CreditCard size={16} />
                    Credit/Debit Card
                  </Label>
                </div>
              </RadioGroup>
              
              {paymentMethod === "wallet" && walletBalance < totalPrice && (
                <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md">
                  Not enough funds in your wallet. Please add funds or select another payment method.
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Card className="sticky top-24">
            <CardHeader>
              <CardTitle>Order Total</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>{formatPrice(totalPrice)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Shipping</span>
                  <span>Free</span>
                </div>
                <Separator />
                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span>{formatPrice(totalPrice)}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                className="w-full" 
                size="lg"
                onClick={handleCheckout}
                disabled={paymentMethod === "wallet" && walletBalance < totalPrice}
              >
                Place Order
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
