
export default {
  name: 'affiliateTransaction',
  title: 'Affiliate Transaction',
  type: 'document',
  fields: [
    {
      name: 'orderId',
      title: 'Order ID',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'affiliateCode',
      title: 'Affiliate Code',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'partnerId',
      title: 'Partner ID',
      type: 'string',
      validation: Rule => Rule.required()
    },
    {
      name: 'productId',
      title: 'Product ID',
      type: 'string'
    },
    {
      name: 'productTitle',
      title: 'Product Title',
      type: 'string'
    },
    {
      name: 'saleAmount',
      title: 'Sale Amount',
      type: 'number',
      validation: Rule => Rule.required()
    },
    {
      name: 'commissionRate',
      title: 'Commission Rate (%)',
      type: 'number',
      validation: Rule => Rule.required()
    },
    {
      name: 'commissionAmount',
      title: 'Commission Amount',
      type: 'number',
      validation: Rule => Rule.required()
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          { title: 'Pending', value: 'pending' },
          { title: 'Approved', value: 'approved' },
          { title: 'Paid', value: 'paid' },
          { title: 'Rejected', value: 'rejected' }
        ]
      },
      initialValue: 'pending'
    },
    {
      name: 'transactionDate',
      title: 'Transaction Date',
      type: 'datetime',
      initialValue: () => new Date().toISOString()
    },
    {
      name: 'payoutDate',
      title: 'Payout Date',
      type: 'datetime'
    }
  ]
}
