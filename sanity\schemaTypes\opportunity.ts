// sanity/schemaTypes/opportunity.ts
import {Rule} from 'sanity'

export default {
  name: 'opportunity',
  title: 'Opportunity',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
    },
    {
      name: 'type',
      title: 'Type',
      type: 'string',
      options: {
        list: ['job', 'program', 'sales', 'task_bundle'],
      },
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: 'category',
      title: 'Category',
      type: 'string',
      description: "e.g., 'Digital Marketing', 'Content Creation', 'Software Development'",
    },
    {
      name: 'company',
      title: 'Company/Organization',
      type: 'reference',
      to: [{type: 'company'}],
    },
    {
      name: 'reward',
      title: 'Reward',
      type: 'string',
      description: "e.g., 'KES 10,000', '20% Commission', 'KES 500 per task'",
    },
    {
      name: 'deadline',
      title: 'Deadline',
      type: 'date',
    },
    {
      name: 'requirements',
      title: 'Requirements',
      type: 'array',
      of: [{type: 'string'}],
    },
    {
      name: 'applicationLink',
      title: 'External Application Link',
      type: 'url',
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: ['open', 'limited', 'closing-soon', 'closed'],
      },
      initialValue: 'open',
    },
    {
      name: 'postedDate',
      title: 'Posted Date',
      type: 'date',
      initialValue: () => new Date().toISOString().split('T')[0],
    },
    {
      name: 'featured',
      title: 'Featured',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'location',
      title: 'Location',
      type: 'string',
      description: "e.g., 'Remote', 'Nairobi', 'Online'",
    },
    {
      name: 'duration',
      title: 'Duration',
      type: 'string',
      description: "e.g., 'Ongoing', '3 Months', 'Fixed Term'",
    },
    {
      name: 'effort',
      title: 'Effort',
      type: 'string',
      description: "e.g., 'Part-time', 'Full-time', 'Flexible'",
    },
  ],
}
