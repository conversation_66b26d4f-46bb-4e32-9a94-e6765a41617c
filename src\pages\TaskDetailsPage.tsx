
import { useParams } from 'react-router-dom';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

const TaskDetailsPage = () => {
  const { taskId } = useParams();

  return (
    <div className="p-6">
      <Card>
        <CardHeader>
          <CardTitle>Task Details</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Task ID: {taskId}</p>
          <p>Task details will be implemented in Phase 2 of the roadmap.</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default TaskDetailsPage;
