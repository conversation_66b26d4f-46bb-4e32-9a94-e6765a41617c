
import React, { useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { LogOut } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from "@/hooks/use-toast";
import { navigationItems } from "@/config/navigation";

interface MobileSidebarProps {
  onClose: () => void;
}

const MobileSidebar = ({ onClose }: MobileSidebarProps) => {
  const isMobile = useIsMobile();
  const location = useLocation();
  const { toast } = useToast();

  // Close sidebar when switching to desktop view
  useEffect(() => {
    if (!isMobile) {
      onClose();
    }
  }, [isMobile, onClose]);

  const handleLogout = () => {
    toast({
      title: "Logged out",
      description: "You have been successfully logged out",
    });
    onClose();
  };

  const handleNavClick = () => {
    onClose();
  };

  // Check if we're on the landing page
  const isLandingPage = location.pathname === '/';
  const profileItem = navigationItems.find(item => item.name === "Profile");

  if (!isMobile) return null;

  return (
    <div className="flex flex-col h-full overflow-hidden">
      <div className="py-6 px-4 flex-shrink-0">
        <div className="text-2xl font-bold">
          <span className="text-earnhub-red">Earn</span>
          <span className="text-earnhub-dark">Hub</span>
        </div>
      </div>
      
      <div className="flex flex-col flex-1 overflow-hidden">
        {!isLandingPage ? (
          <>
            <div className="flex-shrink-0 px-2">
              <div className="flex flex-col items-center justify-center py-6 border-b border-gray-200 mb-4">
                <div className="w-20 h-20 rounded-full bg-earnhub-red/10 flex items-center justify-center mb-4">
                  {profileItem?.icon && (
                    <profileItem.icon size={40} className="text-earnhub-red" />
                  )}
                </div>
                <h3 className="font-bold">Username</h3>
                <p className="text-sm text-earnhub-darkGray">Basic Member</p>
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto px-2 pb-4">
              <div className="space-y-1">
                {navigationItems.map((item) => {
                  const isActive = location.pathname === item.path;
                  return (
                    <Link 
                      key={item.name}
                      to={item.path} 
                      className={`flex items-center px-3 py-2 rounded-md ${isActive ? 'bg-earnhub-red/10 text-earnhub-red' : 'hover:bg-earnhub-lightGray'}`}
                      onClick={handleNavClick}
                    >
                      <item.icon className="mr-2" size={20} />
                      <span>{item.name}</span>
                    </Link>
                  );
                })}
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 overflow-y-auto px-2 pb-4">
            <div className="space-y-1 mt-4">
              <a
                href="#features"
                className="flex items-center px-3 py-2 rounded-md hover:bg-earnhub-lightGray"
                onClick={handleNavClick}
              >
                <span>Ways to Earn</span>
              </a>
              <a
                href="#membership"
                className="flex items-center px-3 py-2 rounded-md hover:bg-earnhub-lightGray"
                onClick={handleNavClick}
              >
                <span>Membership</span>
              </a>
              <a
                href="#testimonials"
                className="flex items-center px-3 py-2 rounded-md hover:bg-earnhub-lightGray"
                onClick={handleNavClick}
              >
                <span>Success Stories</span>
              </a>
            </div>
          </div>
        )}
        
        {!isLandingPage && (
          <div className="flex-shrink-0 p-4 border-t border-gray-200">
            <Button
              variant="destructive"
              className="w-full"
              onClick={handleLogout}
            >
              <LogOut className="mr-2" size={18} />
              Log Out
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileSidebar;
