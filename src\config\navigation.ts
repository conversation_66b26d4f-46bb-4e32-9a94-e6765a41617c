
import { Home, BarChart2, Award, Briefcase, User, Settings, Zap, Medal, Users, Ticket, ShoppingCart, Wallet, TrendingUp, Crown, Handshake } from "lucide-react";

export const navigationItems = [
  {
    name: "Dashboard",
    icon: Home,
    path: "/dashboard",
  },
  {
    name: "Tasks",
    icon: Zap,
    path: "/tasks",
  },
  {
    name: "Competitions",
    icon: Award,
    path: "/competitions",
  },
  {
    name: "Opportunities",
    icon: Briefcase,
    path: "/opportunities",
  },
  {
    name: "Referrals",
    icon: TrendingUp,
    path: "/referrals",
  },
  {
    name: "Guilds",
    icon: Users,
    path: "/guilds",
  },
  {
    name: "Membership",
    icon: Medal,
    path: "/membership",
  },
  {
    name: "Quest<PERSON>",
    icon: Medal,
    path: "/quests",
  },
  {
    name: "Achievements",
    icon: Crown,
    path: "/achievements",
  },
  {
    name: "Ambassadors",
    icon: Users,
    path: "/ambassadors",
  },
  {
    name: "<PERSON>",
    icon: Handshake,
    path: "/partners",
  },
  {
    name: "Ra<PERSON>",
    icon: Ticket,
    path: "/raffle",
  },
  {
    name: "Shop",
    icon: ShoppingCart,
    path: "/shop",
  },
  {
    name: "Profile",
    icon: User,
    path: "/profile",
  },
  {
    name: "Wallet",
    icon: Wallet,
    path: "/wallet",
  },
  {
    name: "Settings",
    icon: Settings,
    path: "/settings",
  },
];
