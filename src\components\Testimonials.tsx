
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Quote } from 'lucide-react';

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Student",
    image: "",
    content: "With Earn<PERSON><PERSON>, I've gone from struggling to pay school fees to earning KES 45,000 in just 4 months. By referring classmates and completing tasks between classes, I now cover my rent and expenses while still focusing on my studies!",
    earned: 45000,
    duration: "4 months"
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Content Creator",
    image: "",
    content: "I was skeptical about earning money online until EarnHub. In my first month, I made KES 20,000 from referrals alone! Now after 7 months, I've earned over KES 120,000 and upgraded to Gold membership. The competitions are addictive and profitable!",
    earned: 120000,
    duration: "7 months"
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "Entrepreneur",
    image: "",
    content: "As a business owner looking to diversify income, EarnHub has been a revelation. I've earned nearly KES 300,000 in my first year! The ambassador program gives me an extra 10% on referrals, and the custom online store feature helped me launch my side business.",
    earned: 290000,
    duration: "1 year"
  }
];

const Testimonials = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const nextTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <section id="testimonials" className="py-20 px-6 md:px-10 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -left-40 w-[400px] h-[400px] bg-gradient-radial from-earnhub-lightGray to-transparent rounded-full"></div>
      </div>
      
      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 max-w-3xl mx-auto">
          <div className="inline-block px-4 py-1 rounded-full bg-earnhub-red/10 mb-4">
            <p className="text-earnhub-red font-medium text-sm">Real success stories</p>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold text-earnhub-dark mb-4 balance-text">
            Real People, Real Earnings
          </h2>
          
          <p className="text-earnhub-darkGray text-lg balance-text">
            Don't just take our word for it. Here's how our members are transforming their finances with EarnHub.
          </p>
        </div>
        
        {/* Testimonials Content */}
        <div className="relative">
          {/* Testimonial Cards */}
          <div className="flex overflow-hidden">
            <div 
              className="flex transition-transform duration-500 ease-in-out w-full"
              style={{ transform: `translateX(-${activeIndex * 100}%)` }}
            >
              {testimonials.map((testimonial) => (
                <div key={testimonial.id} className="w-full flex-shrink-0 px-4">
                  <div className="bg-white rounded-2xl shadow-card p-8 md:p-10 border border-earnhub-gray/10">
                    <div className="flex flex-col md:flex-row gap-8 md:gap-12 items-center">
                      {/* Left side - User Profile */}
                      <div className="w-full md:w-1/3 flex flex-col items-center text-center">
                        {/* User Image */}
                        <div className="w-24 h-24 rounded-full bg-earnhub-lightGray mb-4 flex items-center justify-center">
                          <span className="text-2xl font-bold text-earnhub-red">
                            {testimonial.name.charAt(0)}
                          </span>
                        </div>
                        
                        {/* User Info */}
                        <h3 className="text-xl font-bold text-earnhub-dark mb-1">{testimonial.name}</h3>
                        <p className="text-earnhub-darkGray mb-4">{testimonial.role}</p>
                        
                        {/* Stats */}
                        <div className="bg-earnhub-lightGray rounded-xl p-4 w-full">
                          <div className="mb-3">
                            <p className="text-earnhub-darkGray text-sm">Total Earned</p>
                            <p className="text-xl font-bold text-earnhub-red">
                              KES {testimonial.earned.toLocaleString()}
                            </p>
                          </div>
                          <div>
                            <p className="text-earnhub-darkGray text-sm">Member for</p>
                            <p className="text-lg font-medium text-earnhub-dark">{testimonial.duration}</p>
                          </div>
                        </div>
                      </div>
                      
                      {/* Right side - Testimonial Content */}
                      <div className="w-full md:w-2/3">
                        <div className="text-earnhub-red mb-4">
                          <Quote size={32} />
                        </div>
                        
                        <p className="text-lg text-earnhub-dark mb-6 balance-text">
                          {testimonial.content}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            {testimonials.map((_, index) => (
                              <div 
                                key={index} 
                                className={cn(
                                  "w-3 h-3 rounded-full transition-colors",
                                  index === activeIndex ? "bg-earnhub-red" : "bg-earnhub-gray"
                                )}
                              ></div>
                            ))}
                          </div>
                          
                          <div className="flex space-x-2">
                            <Button 
                              variant="outline" 
                              size="icon" 
                              className="h-9 w-9 rounded-full border-earnhub-gray"
                              onClick={prevTestimonial}
                            >
                              <ChevronLeft className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="outline" 
                              size="icon" 
                              className="h-9 w-9 rounded-full border-earnhub-gray"
                              onClick={nextTestimonial}
                            >
                              <ChevronRight className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Pagination Dots - Mobile Only */}
          {isMobile && (
            <div className="flex justify-center mt-6 space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveIndex(index)}
                  className={cn(
                    "w-3 h-3 rounded-full transition-all",
                    index === activeIndex ? "bg-earnhub-red" : "bg-earnhub-gray"
                  )}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
